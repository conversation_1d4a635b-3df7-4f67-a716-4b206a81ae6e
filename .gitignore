# Ignore generated files and directories
**.log
**.jou
**.out
**.vcd
**.ucdb
**.syn
**.rpt
**.chk
**.sim
**.wlf
**.sdf
**.pvl
**.svf
**.trn
**.dsn
**.cmd
**.drc
**.lvs
**.spc
**.prm

# Ignore synthesis and implementation directories
**/build/
/synth/
!**/sim/
/impl/
*.runs
*.cache

# Ignore temporary and backup files
*~
*.swp
*.bak
*.tmp

# Ignore user-specific files
.idea/
.vscode/
*.user
*.log
*.jou

# Ignore version control directories
.hg/
.git/
.svn/

# Ignore other generated files and directories
**/gen/
**/out/
**/output/
**/results/
**/tmp/
**/temp/
**/work/

# Ignore Cadence tool-specific files
*.cdslck
*.diag
*.err
*.log
*.crashinfo
*.tcf
*.rcf
*.pat
*.act
*.fsdb
*.shm

# Ignore Synopsys tool-specific files
*.syn
*.synsyn
*.synlib
*.synsynthesis

# Ignore Mentor Graphics tool-specific files
*.mgen

# Ignore Xilinx tool-specific files
*.xise
*.xst
*.ngc
*.ngd
*.pcf
*.bmm
*.ncd
*.bit

# Ignore Altera tool-specific files
*.qpf
*.qsf
*.qws
*.sof
*.pof
*.rpt
!**.tcl

# Ignore other EDA tool-specific files
*.def
*.lef
*.lib
*.spef
*.sp

# Ignore binary files
*.bin
*.exe
*.dll
*.so
*.lib
*.a
*.o

# Ignore compressed files
*.zip
*.tar.gz
*.tgz
*.tar.bz2
*.rar
*.7z

# Ignore documentation files
*.pdf
*.doc
*.docx
*.xls
*.xlsx
*.ppt
*.pptx


**/WORK/
**/alib-52/
**/spyglass*/
vp/
hw/

.vscode/
.xmake/
build/
docs/html/
.obsidian/
Doxyfile
*.pyc
**/__pycache__
.pytest_cache/

# windsurf rules
.windsurfrules

# coverage
coverage_reports/
*.gcno
*.gcda
*.info

# cline
.clinerules*

# build files
**/test_output/
*.sym

build/
third_party/