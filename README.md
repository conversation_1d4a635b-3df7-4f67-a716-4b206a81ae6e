# NPU SystemC Simulation Framework

[![CI Pipeline](https://github.com/shiyi-jiaqiu/npu_sc/workflows/NPU%20SystemC%20CI%2FCD%20Pipeline/badge.svg)](https://github.com/shiyi-jiaqiu/npu_sc/actions/workflows/ci.yml)
[![Code Quality](https://github.com/shiyi-jiaqiu/npu_sc/workflows/Code%20Quality%20Checks/badge.svg)](https://github.com/shiyi-jiaqiu/npu_sc/actions/workflows/code-quality.yml)
[![Coverage Analysis](https://github.com/shiyi-jiaqiu/npu_sc/workflows/Code%20Coverage%20Analysis/badge.svg)](https://github.com/shiyi-jiaqiu/npu_sc/actions/workflows/coverage.yml)
[![Release](https://github.com/shiyi-jiaqiu/npu_sc/workflows/Release/badge.svg)](https://github.com/shiyi-jiaqiu/npu_sc/actions/workflows/release.yml)
[![codecov](https://codecov.io/gh/shiyi-jiaqiu/npu_sc/branch/main/graph/badge.svg)](https://codecov.io/gh/shiyi-jiaqiu/npu_sc)

一个基于SystemC的神经处理单元(NPU)仿真框架，支持模块化设计和全面的测试覆盖。

## 🚀 特性

- **模块化架构**: 支持多种处理单元的独立开发和测试
- **SystemC仿真**: 基于SystemC 3.0.0的高精度仿真环境  
- **自动化CI/CD**: 完整的GitHub Actions工作流
- **智能构建**: 基于文件变更的条件构建，提高CI效率
- **覆盖率分析**: 集成Codecov的代码覆盖率报告


## 📋 系统要求

### 必需依赖
- **SystemC**: 3.0.0+ (需要手动安装)
- **xmake**: 2.9.8+
- **编译器**: 支持C++17的编译器 (GCC, Clang)
- **操作系统**: Linux (Ubuntu 20.04+推荐)

### 包依赖 (自动管理)
- `magic_enum`: 0.9.6
- `gtest`: 1.15.2  
- `nlohmann_json`: 3.11.3

## 🛠️ 快速开始

### 1. 环境准备

```bash
# 安装xmake
curl -fsSL https://xmake.io/shget.text | bash

# 安装系统依赖
sudo apt-get update
sudo apt-get install -y build-essential cmake clang-18
```

### 2. 安装SystemC

```bash
# 下载SystemC 3.0.0
wget https://github.com/accellera-official/systemc/archive/refs/tags/3.0.0.tar.gz
tar -xzf 3.0.0.tar.gz
cd systemc-3.0.0

# 构建和安装
mkdir build_dir && cd build_dir
../configure --prefix=/usr/local/systemc-3.0.0
make -j$(nproc)
sudo make install

# 设置环境变量
export SYSTEMC_HOME=/usr/local/systemc-3.0.0
export LD_LIBRARY_PATH=$SYSTEMC_HOME/lib-linux64:$LD_LIBRARY_PATH
```

## 🧪 开发工作流

### 本地开发

```bash
# 克隆仓库
git clone https://github.com/shiyi-jiaqiu/npu_sc.git
cd npu_sc

# 配置构建
xmake f -m release -c

# 构建所有目标
xmake build target

# 运行测试
xmake run target
```

```bash
# 开发模式构建
xmake f -m debug -c
xmake build

# 运行特定模块测试
xmake test test_float --yes
xmake test test_dcim_cluster --yes

# 覆盖率分析
./scripts/coverage_workaround.sh
```

### CI/CD 流程

项目配置了完整的GitHub Actions工作流：

#### 1. 主CI流程 (`.github/workflows/ci.yml`)
- **智能构建**: 基于文件变更检测，只构建和测试相关模块
- **并行测试**: 支持debug和release两种模式的并行构建
- **模块依赖**: 自动处理模块间依赖关系，确保构建顺序
- **SystemC缓存**: 缓存SystemC安装，减少构建时间

**支持的模块组**：
- `cim_cluster` 
- `cmd_scheduler` / `cmd_decoder` 
- `tsu` / `tlu` / `dev_tsu_tlu` 
- `vpu` / `feat_mpu` / `feat_tmu` 
- `local_mem` / `scoreboard` 

#### 2. 代码质量检查 (`.github/workflows/code-quality.yml`)
- **格式检查**: 使用clang-format-18进行代码格式标准化
- **静态分析**: 集成clang-tidy和cppcheck
- **自动修复**: 支持自动格式化并提交修正

#### 3. 覆盖率分析 (`.github/workflows/coverage.yml`)
- **执行**: 运行`coverage_workaround.sh`脚本生成覆盖率数据
- **Codecov集成**: 自动上传覆盖率报告到Codecov平台
- **多格式报告**: 生成HTML和LCOV格式的详细报告
- **PR评论**: 在Pull Request中自动添加覆盖率摘要
- **Artifacts**: 上传覆盖率报告供下载

**覆盖率特性**：
- 智能过滤：排除第三方代码和测试文件
- 模块级统计：每个模块独立的覆盖率报告
- 合并报告：生成项目整体覆盖率数据
- 趋势分析：通过Codecov查看覆盖率变化趋势

#### 4. 发布流程 (`.github/workflows/release.yml`)
- **版本管理**: 支持标签触发和手动触发
- **多格式打包**: 生成源码包和文档包
- **自动发布**: 创建GitHub Release并上传assets
- **版本文档**: 自动生成发布说明

### 触发条件

| 工作流 | Push (main/dev) | Pull Request | 手动触发 | 标签 |
|--------|----------------|--------------|----------|------|
| CI Pipeline | ✅ | ✅ | ✅ | ❌ |
| Code Quality | ✅ | ✅ | ❌ | ❌ |
| Coverage Analysis | ✅ | ✅ | ✅ | ❌ |
| Release | ❌ | ❌ | ✅ | ✅ |

### 缓存策略

- **xmake缓存**: 缓存包管理器和构建工具
- **SystemC缓存**: 缓存SystemC编译和安装
- **包依赖缓存**: 缓存第三方依赖包
- **构建缓存**: 缓存编译的二进制文件

---
