#!/usr/bin/env python3
"""
Fix Coverage Issues for NPU SC Project
This script diagnoses and fixes common coverage analysis problems
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Tuple, Any

class CoverageFixAnalyzer:
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.build_dir = self.project_root / "build" / ".objs"
        
    def diagnose_coverage_issues(self) -> Dict[str, Any]:
        """Diagnose coverage-related issues"""
        print("=== Coverage Issue Diagnosis ===\n")
        
        issues = {
            "missing_gcno_files": [],
            "missing_source_files": [],
            "build_config_issues": [],
            "target_issues": {}
        }
        
        # Check if build directory exists
        if not self.build_dir.exists():
            issues["build_config_issues"].append("Build directory does not exist. Run 'xmake build' first.")
            return issues
        
        # Check each target
        for target_dir in self.build_dir.iterdir():
            if target_dir.is_dir():
                target_name = target_dir.name
                coverage_dir = target_dir / "linux" / "x86_64" / "coverage"
                
                print(f"Checking target: {target_name}")
                
                if not coverage_dir.exists():
                    issues["target_issues"][target_name] = "No coverage directory found"
                    print(f"  ✗ No coverage directory: {coverage_dir}")
                    continue
                
                # Find coverage files
                gcno_files = list(coverage_dir.rglob("*.gcno"))
                gcda_files = list(coverage_dir.rglob("*.gcda"))
                
                print(f"  Found {len(gcno_files)} .gcno files and {len(gcda_files)} .gcda files")
                
                target_issues = []
                
                # Check for missing .gcno files
                if not gcno_files and gcda_files:
                    target_issues.append("Missing .gcno files - target may not be built with coverage flags")
                    issues["missing_gcno_files"].extend([str(f) for f in gcda_files])
                
                # Check for missing .gcda files
                if gcno_files and not gcda_files:
                    target_issues.append("Missing .gcda files - tests may not have been executed")
                
                # Check source file mapping
                for gcda_file in gcda_files:
                    rel_path = gcda_file.relative_to(coverage_dir)
                    potential_sources = [
                        self.project_root / rel_path.with_suffix('.cpp'),
                        self.project_root / rel_path.with_suffix('.c'),
                        self.project_root / rel_path.with_suffix('.cc')
                    ]
                    
                    source_found = any(src.exists() for src in potential_sources)
                    if not source_found:
                        issues["missing_source_files"].append(str(gcda_file))
                        print(f"    ✗ Source file not found for {rel_path}")
                
                if target_issues:
                    issues["target_issues"][target_name] = target_issues
                else:
                    print(f"  ✓ Target {target_name} looks good")
        
        return issues
    
    def check_build_configuration(self) -> List[str]:
        """Check if targets are properly configured for coverage"""
        print("\n=== Build Configuration Check ===\n")
        
        issues = []
        
        # Check toolchain configuration
        try:
            # Read xmake.lua to check toolchain
            xmake_lua_path = self.project_root / "xmake.lua"
            if xmake_lua_path.exists():
                content = xmake_lua_path.read_text()
                if "clang" in content and not content.startswith("--") and "set_toolchains" in content:
                    if "clang" in content:
                        issues.append("Detected Clang toolchain. Clang uses different coverage format than gcov.")
                        issues.append("Consider: 1) Switch to GCC, or 2) Use scripts/clang_coverage.sh")
                        print("⚠ Clang toolchain detected - may cause gcov compatibility issues")
                else:
                    print("✓ Using GCC-compatible toolchain")
            else:
                print("⚠ xmake.lua not found")
        except Exception as e:
            print(f"⚠ Could not check toolchain configuration: {e}")
        
        # Check if coverage mode is enabled
        try:
            result = subprocess.run(
                ["xmake", "config", "--dump"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                config_output = result.stdout
                if "coverage" in config_output:
                    print("✓ Coverage mode is configured")
                else:
                    issues.append("Coverage mode not configured. Run 'xmake config --mode=coverage'")
                    print("✗ Coverage mode not configured")
            else:
                issues.append("Could not check xmake configuration")
                print("✗ Could not check xmake configuration")
                
        except FileNotFoundError:
            issues.append("xmake not found in PATH")
            print("✗ xmake not found in PATH")
        
        # Check compiler flags
        print("\nChecking for coverage-related compiler flags...")
        
        # Look for coverage flags in build files
        build_files = list(self.project_root.rglob("*.o"))
        if build_files:
            print(f"Found {len(build_files)} object files")
        else:
            issues.append("No object files found. Build the project first.")
            print("✗ No object files found")
        
        # Check gcov version compatibility
        try:
            result = subprocess.run(
                ["gcov", "--version"],
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                gcov_version = result.stdout.split('\n')[0]
                print(f"\nCurrent gcov version: {gcov_version}")
                
                # Check for .gcno files with version info
                gcno_files = list(self.build_dir.rglob("*.gcno"))
                if gcno_files:
                    issues.append("Version compatibility issue detected - consider using --ignore-errors version")
                    print("⚠ Potential version compatibility issues with .gcno files")
            else:
                issues.append("Could not check gcov version")
                print("✗ Could not check gcov version")
        except FileNotFoundError:
            issues.append("gcov not found in PATH")
            print("✗ gcov not found in PATH")
        
        return issues
    
    def suggest_fixes(self, issues: Dict[str, Any]) -> List[str]:
        """Suggest fixes for identified issues"""
        print("\n=== Suggested Fixes ===\n")
        
        fixes = []
        
        if issues["build_config_issues"]:
            fixes.append("1. Configure and build with coverage:")
            fixes.append("   xmake config --mode=coverage")
            fixes.append("   xmake build")
            fixes.append("   xmake test")
        
        if issues["missing_gcno_files"]:
            fixes.append("2. Fix missing .gcno files:")
            fixes.append("   - Ensure targets are built with coverage flags")
            fixes.append("   - Check that --mode=coverage is set before building")
            fixes.append("   - Clean and rebuild: xmake clean && xmake build")
        
        if issues["missing_source_files"]:
            fixes.append("3. Fix source file mapping:")
            fixes.append("   - Verify source file paths in xmake.lua")
            fixes.append("   - Check that source files exist in expected locations")
        
        if issues["target_issues"]:
            fixes.append("4. Target-specific issues:")
            for target, target_issues in issues["target_issues"].items():
                if isinstance(target_issues, list):
                    for issue in target_issues:
                        fixes.append(f"   {target}: {issue}")
                else:
                    fixes.append(f"   {target}: {target_issues}")
        
        # Version compatibility fixes
        if any("version" in str(issue) for issue_list in issues.values() for issue in (issue_list if isinstance(issue_list, list) else [issue_list])):
            fixes.extend([
                "",
                "5. Fix GCC/GCOV version compatibility:",
                "   a) Ignore version errors (recommended):",
                "      lcov --capture --directory build --ignore-errors gcov,source,empty,version -o coverage.info",
                "   b) Rebuild with current compiler:",
                "      xmake clean && xmake config --mode=coverage && xmake build",
                "   c) Use geninfo directly:",
                "      geninfo build/.objs/target/linux/x86_64/coverage --ignore-errors version -o coverage.info"
            ])
        
        # General fixes
        fixes.extend([
            "",
            "6. Generate combined coverage for codecov:",
            "   # Method 1: Use multiple --add-tracefile parameters",
            "   lcov --output-file coverage_reports/combined_coverage.info \\",
            "        --add-tracefile file1_codecov.info \\",
            "        --add-tracefile file2_codecov.info \\",
            "        --ignore-errors gcov,source,empty,version,unused",
            "   # Method 2: Use the workaround script which handles this automatically",
            "",
            "7. Alternative approaches:",
            "   a) Use manual gcov for specific files:",
            "      gcov -b -c file.cpp -o build/.objs/target/linux/x86_64/coverage/path/",
            "",
            "   b) Use lcov with comprehensive error ignoring:",
            "      lcov --capture --directory build --ignore-errors gcov,source,empty,version,unused -o coverage.info",
            "",
            "   c) Check specific target coverage:",
            "      python3 scripts/coverage_analysis.py --targets target_name"
        ])
        
        return fixes
    
    def create_workaround_script(self) -> None:
        """Create a workaround script for current issues"""
        print("\n=== Creating Workaround Script ===\n")
        
        workaround_script = """#!/bin/bash

# Workaround script for coverage analysis issues

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo "=== Coverage Analysis Workaround ==="

# Step 1: Clean and reconfigure
echo "1. Cleaning and reconfiguring..."
xmake clean
xmake config --mode=coverage 

# Step 2: Run tests
echo "2. Running tests..."
xmake test || true


# Create coverage reports directory
mkdir -p coverage_reports

# Process each target with available .gcda files
for target_dir in build/.objs/*/; do
    if [ -d "$target_dir" ]; then
        target_name=$(basename "$target_dir")
        coverage_dir="$target_dir/linux/x86_64/coverage"
        
        if [ -d "$coverage_dir" ]; then
            echo "Processing target: $target_name"
            
            # Create target coverage directory
            mkdir -p "coverage_reports/$target_name"
            
            # Generate coverage data with immediate filtering
            lcov --capture \\
                 --directory "$coverage_dir" \\
                 --base-directory "$PROJECT_ROOT" \\
                 --output-file "coverage_reports/$target_name/${target_name}_raw.info" \\
                 --ignore-errors gcov,source,empty,version,unused || echo "LCOV capture failed for $target_name"
            
            # Extract only project source files
            if [ -f "coverage_reports/$target_name/${target_name}_raw.info" ]; then
                lcov --extract "coverage_reports/$target_name/${target_name}_raw.info" \\
                     "$PROJECT_ROOT/src/*" \\
                     "$PROJECT_ROOT/include/*" \\
                     --output-file "coverage_reports/$target_name/${target_name}_project.info" \\
                     --ignore-errors gcov,source,empty,version,unused
                
                # Remove third-party and build artifacts
                lcov --remove "coverage_reports/$target_name/${target_name}_project.info" \\
                     "*/ac_types-master/*" "*/.xmake/*" "*/build/*" "*/cmake-build-*/*" "*/gtest/*" "*/gmock/*" \\
                     --output-file "coverage_reports/$target_name/$target_name.info" \\
                     --ignore-errors gcov,source,empty,version,unused
                
                # Prepare for codecov
                cp "coverage_reports/$target_name/$target_name.info" "coverage_reports/${target_name}_codecov.info"
                
                # Clean temporary files
                rm -f "coverage_reports/$target_name/${target_name}_raw.info" \\
                      "coverage_reports/$target_name/${target_name}_project.info"
            fi
            
            # If LCOV info file was created, generate HTML
            if [ -f "coverage_reports/$target_name/$target_name.info" ]; then
                genhtml "coverage_reports/$target_name/$target_name.info" \\
                        --output-directory "coverage_reports/$target_name/html" \\
                        --title "Coverage Report - $target_name" \\
                        --ignore-errors empty,unused || echo "HTML generation failed for $target_name"
            fi
        fi
    fi
done

echo ""
echo "=== Workaround Complete ==="
echo "Check coverage_reports/ directory for results"
"""
        
        workaround_path = self.project_root / "scripts" / "coverage_workaround.sh"
        workaround_path.write_text(workaround_script)
        workaround_path.chmod(0o755)
        
        print(f"✓ Created workaround script: {workaround_path}")
        print("  Run with: ./scripts/coverage_workaround.sh")
    
    def run_analysis(self) -> None:
        """Run complete coverage issue analysis"""
        print(f"Analyzing coverage issues in: {self.project_root}\n")
        
        # Diagnose issues
        issues = self.diagnose_coverage_issues()
        
        # Check build configuration
        build_issues = self.check_build_configuration()
        if build_issues:
            issues["build_config_issues"].extend(build_issues)
        
        # Suggest fixes
        fixes = self.suggest_fixes(issues)
        
        # Print fixes
        for fix in fixes:
            print(fix)
        
        # Create workaround script
        self.create_workaround_script()
        
        # Summary
        print(f"\n=== Summary ===")
        total_issues = (
            len(issues["missing_gcno_files"]) +
            len(issues["missing_source_files"]) +
            len(issues["build_config_issues"]) +
            len(issues["target_issues"])
        )
        
        if total_issues > 0:
            print(f"Found {total_issues} issues that need to be addressed.")
            print("Follow the suggested fixes above.")
        else:
            print("No major issues found. Try running the coverage analysis again.")

def main():
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = "."
    
    analyzer = CoverageFixAnalyzer(project_root)
    analyzer.run_analysis()

if __name__ == "__main__":
    main()