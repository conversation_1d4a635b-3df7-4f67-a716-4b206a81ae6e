#!/bin/bash

# Workaround script for coverage analysis issues

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo "=== Coverage Analysis Workaround ==="

# Step 1: Clean and reconfigure
echo "1. Cleaning and reconfiguring..."
xmake clean --all
xmake f -c
xmake f --mode=coverage  -y

# Step 2: Run tests
echo "2. Running tests..."
xmake test || true

# Step 3: Check for version compatibility issues
echo "3. Checking gcov version compatibility..."
gcov_version=$(gcov --version | head -n1)
echo "Current gcov version: $gcov_version"


# Create coverage reports directory
mkdir -p coverage_reports

# Process each target with available .gcda files
for target_dir in build/.objs/*/; do
    if [ -d "$target_dir" ]; then
        target_name=$(basename "$target_dir")
        coverage_dir="$target_dir/linux/x86_64/coverage"
        
        if [ -d "$coverage_dir" ]; then
            echo "Processing target: $target_name"
            
            # 调试信息：显示找到的文件
            echo "  Coverage directory: $coverage_dir"
            gcda_count=$(find "$coverage_dir" -name "*.gcda" | wc -l)
            gcno_count=$(find "$coverage_dir" -name "*.gcno" | wc -l)
            echo "  Found $gcda_count .gcda files and $gcno_count .gcno files"
            
            # Create target coverage directory
            mkdir -p "coverage_reports/$target_name"
            
            # Generate initial coverage data with strict filtering
            echo "  Generating coverage data with filtering..."
            lcov --capture \
                 --directory "$coverage_dir" \
                 --base-directory "$PROJECT_ROOT" \
                 --output-file "coverage_reports/$target_name/${target_name}_raw.info" \
                 --ignore-errors gcov,source,empty,version,unused || echo "LCOV capture failed for $target_name"
            
            # 立即过滤，只保留项目源代码
            if [ -f "coverage_reports/$target_name/${target_name}_raw.info" ]; then
                echo "  Extracting project source files only..."
                lcov --extract "coverage_reports/$target_name/${target_name}_raw.info" \
                     "$PROJECT_ROOT/src/*" \
                     "$PROJECT_ROOT/include/*" \
                     --output-file "coverage_reports/$target_name/${target_name}_project.info" \
                     --ignore-errors gcov,source,empty,version,unused || echo "  Extract failed for $target_name"
                
                # 排除第三方和不需要的代码
                if [ -f "coverage_reports/$target_name/${target_name}_project.info" ]; then
                    echo "  Removing third-party code..."
                    lcov --remove "coverage_reports/$target_name/${target_name}_project.info" \
                         "*/ac_types-master/*" \
                         "*/.xmake/*" \
                         "*/build/*" \
                         "*/cmake-build-*/*" \
                         "*/gtest/*" \
                         "*/gmock/*" \
                         --output-file "coverage_reports/$target_name/$target_name.info" \
                         --ignore-errors gcov,source,empty,version,unused || echo "  Remove failed for $target_name"
                    
                    # 清理临时文件
                    rm -f "coverage_reports/$target_name/${target_name}_raw.info" \
                          "coverage_reports/$target_name/${target_name}_project.info"
                fi
            fi
            
            # If LCOV info file was created, generate HTML and prepare for codecov
            if [ -f "coverage_reports/$target_name/$target_name.info" ]; then
                # Check if the info file has actual coverage data
                lines_count=$(grep -c "^SF:" "coverage_reports/$target_name/$target_name.info" 2>/dev/null || echo "0")
                if [ "$lines_count" -gt 0 ]; then
                    echo "  Found $lines_count source files with coverage data"
                    
                    # Generate HTML report
                    genhtml "coverage_reports/$target_name/$target_name.info" \
                            --output-directory "coverage_reports/$target_name/html" \
                            --title "Coverage Report - $target_name" \
                            --ignore-errors empty,unused || echo "HTML generation failed for $target_name"
                    
                    # Copy for codecov (GitHub Actions)
                    cp "coverage_reports/$target_name/$target_name.info" "coverage_reports/${target_name}_codecov.info"
                else
                    echo "  No coverage data found in info file"
                fi
            else
                # Try alternative approach with geninfo directly
                echo "  Trying alternative approach with geninfo..."
                geninfo "$coverage_dir" \
                        --base-directory "$PROJECT_ROOT" \
                        --output-file "coverage_reports/$target_name/${target_name}_alt_raw.info" \
                        --ignore-errors gcov,source,empty,version,unused || echo "  Alternative geninfo failed for $target_name"
                
                # 同样进行过滤
                if [ -f "coverage_reports/$target_name/${target_name}_alt_raw.info" ]; then
                    echo "    Extracting project files (alternative approach)..."
                    lcov --extract "coverage_reports/$target_name/${target_name}_alt_raw.info" \
                         "$PROJECT_ROOT/src/*" \
                         "$PROJECT_ROOT/include/*" \
                         --output-file "coverage_reports/$target_name/${target_name}_alt_project.info" \
                         --ignore-errors gcov,source,empty,version,unused || echo "    Alternative extract failed"
                    
                    # 排除第三方代码
                    if [ -f "coverage_reports/$target_name/${target_name}_alt_project.info" ]; then
                        echo "    Removing third-party code (alternative approach)..."
                        lcov --remove "coverage_reports/$target_name/${target_name}_alt_project.info" \
                             "*/ac_types-master/*" \
                             "*/.xmake/*" \
                             "*/build/*" \
                             "*/cmake-build-*/*" \
                             "*/gtest/*" \
                             "*/gmock/*" \
                             --output-file "coverage_reports/$target_name/$target_name.info" \
                             --ignore-errors gcov,source,empty,version,unused || echo "    Alternative remove failed"
                        
                        # 清理临时文件
                        rm -f "coverage_reports/$target_name/${target_name}_alt_raw.info" \
                              "coverage_reports/$target_name/${target_name}_alt_project.info"
                    fi
                fi
                
                # Try generating HTML if info file was created
                if [ -f "coverage_reports/$target_name/$target_name.info" ]; then
                    # Check if the info file has actual coverage data
                    lines_count=$(grep -c "^SF:" "coverage_reports/$target_name/$target_name.info" 2>/dev/null || echo "0")
                    if [ "$lines_count" -gt 0 ]; then
                        echo "    Found $lines_count source files with coverage data (alternative)"
                        
                        genhtml "coverage_reports/$target_name/$target_name.info" \
                                --output-directory "coverage_reports/$target_name/html" \
                                --title "Coverage Report - $target_name" \
                                --ignore-errors empty,unused || echo "  HTML generation failed for $target_name"
                        
                        # Copy for codecov (GitHub Actions)
                        cp "coverage_reports/$target_name/$target_name.info" "coverage_reports/${target_name}_codecov.info"
                    else
                        echo "    No coverage data found in alternative info file"
                    fi
                fi
            fi
        fi
    fi
done

echo ""
echo "=== Generating Combined Coverage Report for Codecov ==="

# Combine all individual coverage reports for codecov
valid_files=()
for info_file in coverage_reports/*_codecov.info; do
    if [ -f "$info_file" ]; then
        lines_count=$(grep -c "^SF:" "$info_file" 2>/dev/null || echo "0")
        if [ "$lines_count" -gt 0 ]; then
            valid_files+=("$info_file")
        fi
    fi
done

if [ ${#valid_files[@]} -gt 0 ]; then
    echo "Combining ${#valid_files[@]} coverage files..."
    
    # Build lcov command with multiple --add-tracefile parameters
    lcov_cmd="lcov --output-file coverage_reports/combined_coverage.info --ignore-errors gcov,source,empty,version,unused"
    for file in "${valid_files[@]}"; do
        lcov_cmd="$lcov_cmd --add-tracefile $file"
    done
    
    echo "Running: $lcov_cmd"
    eval $lcov_cmd || echo "Failed to combine coverage files"
    
    if [ -f "coverage_reports/combined_coverage.info" ]; then
        # Generate combined HTML report
        genhtml "coverage_reports/combined_coverage.info" \
                --output-directory "coverage_reports/combined_html" \
                --title "Combined Coverage Report" \
                --ignore-errors empty,unused || echo "Failed to generate combined HTML"
        
        echo "Combined coverage report generated:"
        echo "  - LCOV file: coverage_reports/combined_coverage.info (for codecov)"
        echo "  - HTML report: coverage_reports/combined_html/index.html"
        
        # Show summary
        lines_total=$(grep -c "^SF:" "coverage_reports/combined_coverage.info" 2>/dev/null || echo "0")
        echo "  - Total source files: $lines_total"
    fi
else
    echo "No valid coverage data found to combine"
fi

echo ""
echo "=== Workaround Complete ==="
echo "Check coverage_reports/ directory for results"
echo "For codecov upload, use: coverage_reports/combined_coverage.info"
