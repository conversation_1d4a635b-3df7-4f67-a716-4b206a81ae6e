# Codecov configuration for NPU SystemC project

coverage:
  precision: 2
  round: down
  range: "70...95"

  status:
    project:
      default:
        target: 80%          # 项目整体覆盖率目标
        threshold: 1%        # 允许的覆盖率下降幅度
        base: auto           # 比较基准：自动选择
        flags:
          - unittests
        if_no_uploads: error
        if_not_found: success
        if_ci_failed: error

    patch:
      default:
        target: 70%          # 新增代码覆盖率目标
        threshold: 5%        # 新增代码允许的覆盖率下降幅度
        base: auto
        flags:
          - unittests
        if_no_uploads: error
        if_not_found: success
        if_ci_failed: error

  ignore:
    # 第三方代码
    - "ac_types-master/**"
    
    # 构建输出目录
    - "build/**"
    - ".xmake/**"
    - "cmake-build-*/**"
    
    # 测试文件 (保留测试源码的覆盖率统计)
    - "**/test_*"
    - "**/*_test.cpp"
    - "**/*_test.h"
    - "**/*_test.hpp"
    
    # 配置和脚本文件
    - "scripts/**"
    - "*.lua"
    - "*.sh"
    - "*.yml"
    - "*.yaml"
    
    # 文档和静态文件
    - "docs/**"
    - "img/**"
    - "*.md"
    - "*.txt"

comment:
  layout: "reach,diff,flags,tree,reach"
  behavior: default
  require_changes: false     # 即使没有覆盖率变化也要评论
  require_base: no          # 不需要基准覆盖率即可评论
  require_head: yes         # 需要当前提交的覆盖率数据

github_checks:
  annotations: true

flags:
  unittests:
    paths:
      - src/
      - include/ 