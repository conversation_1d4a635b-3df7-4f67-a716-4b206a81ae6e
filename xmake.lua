add_rules("mode.debug","mode.coverage")
set_project("npu_sc")
add_rules("plugin.compile_commands.autoupdate", {outputdir = ".vscode"})
add_requires("magic_enum 0.9.6")
add_requires("gtest 1.15.2")
add_requires("nlohmann_json 3.11.3")
add_requires("spdlog 1.15.2")
set_languages("cxx11")
set_toolchains("gcc")
-- set_policy("build.sanitizer.address", true)
-- set_policy("build.sanitizer.undefined", true)
-- set_policy("build.sanitizer.leak", true)

set_warnings("all", "extra", "pedantic")

-- 调试模式下不进行优化 -O0
if is_mode("debug") then
    set_optimize("none")
end

target("common")
    set_kind("static")
    add_files("include/npu_config.cpp")
    add_includedirs("/usr/local/systemc-2.3.2/include", {public = true})
    add_links("systemc", {public = true})
    add_includedirs("./include", {public = true})
    add_rpathdirs("/usr/local/systemc-2.3.2/lib-linux64", {public = true})
    add_linkdirs("/usr/local/systemc-2.3.2/lib-linux64", {public = true})
    -- add_cxxflags("-g", "-O0", "-Wall","-Wextra","-Wpedantic", {force = true})
    add_defines("SC_INCLUDE_DYNAMIC_PROCESSES", {public = true})
    if is_mode("debug") then
        add_defines("DEBUG",{public = true})
        add_defines("DEBUG",{public = true})
    end


target("utils")
    set_kind("static")
    add_includedirs("./include/utils", {public = true})
    add_deps("ac_types")
    add_packages("magic_enum")

target("systemc_logger")
    add_defines("ENABLE_LOGGING", {public = true})
    add_files("include/utils/systemc_logger.cpp")
    set_kind("static")
    add_headerfiles("include/utils/systemc_logger.h")
    add_deps("common")

target("sc_logger")
    set_kind("static")
    add_headerfiles("include/utils/sc_logger.h",{public=true})
    add_includedirs("include/utils", {public = true})
    add_deps("common")
    add_packages("spdlog")

target("test_sc_logger")
    set_kind("binary")
    add_files("include/utils/test_sc_logger.cpp")
    add_deps("sc_logger")
    add_packages("spdlog")

target("test_register")
    set_kind("binary")
    add_files("include/utils/register_debug_example.cpp")
    add_deps("sc_logger")
    add_packages("spdlog")
    add_deps("utils")
    add_defines("SPDLOG_H")
target("ac_types")
    set_kind("static")
    add_includedirs("ac_types-master/include", {public = true})

includes("src/scoreboard/xmake.lua")
includes("src/cmd_scheduler/xmake.lua")
includes("src/tsu/xmake.lua")
includes("src/tlu/xmake.lua")
includes("src/dev_tsu_tlu/xmake.lua")
includes("src/cmd_decoder/xmake.lua")
includes("src/vpu/xmake.lua")
includes("ac_types-master/xmake.lua")
includes("src/local_mem/xmake.lua")
includes("src/feat_mpu/xmake.lua")

includes("src/cim_cluster/xmake.lua")
includes("src/feat_tmu/xmake.lua")

