# this is the build file for project npu_sc
# it is autogenerated by the xmake build system.
# do not edit by hand.

ifneq ($(VERBOSE),1)
VV=@
endif

CCACHE=/usr/bin/ccache
AS=/usr/bin/gcc
RC=/home/<USER>/.cargo/bin/rustc
CC=/usr/bin/gcc
MM=/usr/bin/gcc
CXX=/usr/bin/gcc
FC=/usr/bin/gfortran
MXX=/usr/bin/gcc

SH=/usr/bin/g++
FCSH=/usr/bin/gfortran
RCSH=/home/<USER>/.cargo/bin/rustc
LD=/usr/bin/g++
FCLD=/usr/bin/gfortran
RCLD=/home/<USER>/.cargo/bin/rustc
AR=/usr/bin/ar
RCAR=/home/<USER>/.cargo/bin/rustc

test_npu_datatypes_LD=/usr/bin/clang++-18
test_npu_datatypes_CXX=/usr/bin/clang-18
test_npu_datatypes_CXX=/usr/bin/clang-18
test_float_LD=/usr/bin/clang++-18
test_float_CXX=/usr/bin/clang-18
test_float_CXX=/usr/bin/clang-18
systemc_logger_AR=/usr/bin/ar
systemc_logger_CXX=/usr/bin/clang-18
systemc_logger_CXX=/usr/bin/clang-18
test_dcim_array_LD=/usr/bin/clang++-18
test_dcim_array_CXX=/usr/bin/clang-18
test_dcim_array_CXX=/usr/bin/clang-18
rsp_rob_AR=/usr/bin/ar
rsp_rob_CXX=/usr/bin/clang-18
rsp_rob_CXX=/usr/bin/clang-18
cmd_dispatch_AR=/usr/bin/ar
cmd_dispatch_CXX=/usr/bin/clang-18
cmd_dispatch_CXX=/usr/bin/clang-18
vpe_test_LD=/usr/bin/clang++-18
vpe_test_CXX=/usr/bin/clang-18
vpe_test_CXX=/usr/bin/clang-18
feat_mpu_AR=/usr/bin/ar
feat_mpu_CXX=/usr/bin/clang-18
feat_mpu_CXX=/usr/bin/clang-18
test_cmd_dispatch_LD=/usr/bin/clang++-18
test_cmd_dispatch_CXX=/usr/bin/clang-18
test_cmd_dispatch_CXX=/usr/bin/clang-18
common_AR=/usr/bin/ar
feat_tmu_AR=/usr/bin/ar
feat_tmu_CXX=/usr/bin/clang-18
feat_tmu_CXX=/usr/bin/clang-18
test_tlu_LD=/usr/bin/clang++-18
test_tlu_CXX=/usr/bin/clang-18
test_tlu_CXX=/usr/bin/clang-18
top_LD=/usr/bin/clang++-18
top_CXX=/usr/bin/clang-18
top_CXX=/usr/bin/clang-18
cmd_test_op_LD=/usr/bin/clang++-18
cmd_test_op_CXX=/usr/bin/clang-18
cmd_test_op_CXX=/usr/bin/clang-18
dcim_cluster_AR=/usr/bin/ar
dcim_cluster_CXX=/usr/bin/clang-18
dcim_cluster_CXX=/usr/bin/clang-18
tsu_memory_AR=/usr/bin/ar
tsu_memory_CXX=/usr/bin/clang-18
tsu_memory_CXX=/usr/bin/clang-18
test_func_LD=/usr/bin/clang++-18
test_func_CXX=/usr/bin/clang-18
test_func_CXX=/usr/bin/clang-18
tlu_cfg_reg_AR=/usr/bin/ar
test_npu_macunit_LD=/usr/bin/clang++-18
test_npu_macunit_CXX=/usr/bin/clang-18
test_npu_macunit_CXX=/usr/bin/clang-18
npu_datagen_AR=/usr/bin/ar
npu_datagen_CXX=/usr/bin/clang-18
npu_datagen_CXX=/usr/bin/clang-18
tlu_memory_AR=/usr/bin/ar
tlu_memory_CXX=/usr/bin/clang-18
tlu_memory_CXX=/usr/bin/clang-18
cmd_encode_AR=/usr/bin/ar
cmd_encode_CXX=/usr/bin/clang-18
cmd_encode_CXX=/usr/bin/clang-18
tsu_desc_gen_AR=/usr/bin/ar
tsu_desc_gen_CXX=/usr/bin/clang-18
tsu_desc_gen_CXX=/usr/bin/clang-18
vpu_AR=/usr/bin/ar
vpu_CXX=/usr/bin/clang-18
vpu_CXX=/usr/bin/clang-18
test_reg_LD=/usr/bin/clang++-18
test_reg_CXX=/usr/bin/clang-18
test_reg_CXX=/usr/bin/clang-18
dcim_macro_cpp_AR=/usr/bin/ar
dcim_macro_cpp_CXX=/usr/bin/clang-18
dcim_macro_cpp_CXX=/usr/bin/clang-18
test_dcim_cluster_LD=/usr/bin/clang++-18
test_dcim_cluster_CXX=/usr/bin/clang-18
test_dcim_cluster_CXX=/usr/bin/clang-18
scoreboard_AR=/usr/bin/ar
scoreboard_CXX=/usr/bin/clang-18
scoreboard_CXX=/usr/bin/clang-18
instruction_generator_AR=/usr/bin/ar
instruction_generator_CXX=/usr/bin/clang-18
instruction_generator_CXX=/usr/bin/clang-18
dcim_engine_cpp_AR=/usr/bin/ar
dcim_engine_cpp_CXX=/usr/bin/clang-18
dcim_engine_cpp_CXX=/usr/bin/clang-18
test_scoreboard_LD=/usr/bin/clang++-18
test_scoreboard_CXX=/usr/bin/clang-18
test_scoreboard_CXX=/usr/bin/clang-18
test_dcim_engine_LD=/usr/bin/clang++-18
test_dcim_engine_CXX=/usr/bin/clang-18
test_dcim_engine_CXX=/usr/bin/clang-18
test_mpu_LD=/usr/bin/clang++-18
test_mpu_CXX=/usr/bin/clang-18
test_mpu_CXX=/usr/bin/clang-18
cmd_decoder_op_AR=/usr/bin/ar
cmd_decoder_op_CXX=/usr/bin/clang-18
cmd_decoder_op_CXX=/usr/bin/clang-18
test_vpu_LD=/usr/bin/clang++-18
test_vpu_CXX=/usr/bin/clang-18
test_vpu_CXX=/usr/bin/clang-18
test_cmd_scheduler_AR=/usr/bin/ar
test_cmd_scheduler_CXX=/usr/bin/clang-18
test_cmd_scheduler_CXX=/usr/bin/clang-18
cmd_scheduler_AR=/usr/bin/ar
cmd_scheduler_CXX=/usr/bin/clang-18
cmd_scheduler_CXX=/usr/bin/clang-18
tlu_desc_gen_AR=/usr/bin/ar
tlu_desc_gen_CXX=/usr/bin/clang-18
tlu_desc_gen_CXX=/usr/bin/clang-18
cmd_decoder_AR=/usr/bin/ar
cmd_decoder_CXX=/usr/bin/clang-18
cmd_decoder_CXX=/usr/bin/clang-18
dcim_macro_AR=/usr/bin/ar
dcim_macro_CC=/usr/bin/clang-18
ac_types_AR=/usr/bin/ar
test_tsu_LD=/usr/bin/clang++-18
test_tsu_CXX=/usr/bin/clang-18
test_tsu_CXX=/usr/bin/clang-18
test_lmem_LD=/usr/bin/clang++-18
test_lmem_CXX=/usr/bin/clang-18
test_lmem_CXX=/usr/bin/clang-18
tsu_cfg_reg_AR=/usr/bin/ar
test_rsp_rob_LD=/usr/bin/clang++-18
test_rsp_rob_CXX=/usr/bin/clang-18
test_rsp_rob_CXX=/usr/bin/clang-18
dev_tsu_tlu_AR=/usr/bin/ar
dev_tsu_tlu_CXX=/usr/bin/clang-18
dev_tsu_tlu_CXX=/usr/bin/clang-18
test_dev_tsu_tlu_LD=/usr/bin/clang++-18
test_dev_tsu_tlu_CXX=/usr/bin/clang-18
test_dev_tsu_tlu_CXX=/usr/bin/clang-18
vpe_AR=/usr/bin/ar
vpe_CXX=/usr/bin/clang-18
vpe_CXX=/usr/bin/clang-18
utils_AR=/usr/bin/ar
test_storage_compute_LD=/usr/bin/clang++-18
test_storage_compute_CXX=/usr/bin/clang-18
test_storage_compute_CXX=/usr/bin/clang-18
local_mem_AR=/usr/bin/ar
local_mem_CXX=/usr/bin/clang-18
local_mem_CXX=/usr/bin/clang-18

test_npu_datatypes_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Iac_types-master -Ibuild/.gens/test_npu_datatypes/linux/x86_64/coverage/platform/windows/idl -Iac_types-master/include -isystem /home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/include -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_npu_datatypes_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Iac_types-master -Ibuild/.gens/test_npu_datatypes/linux/x86_64/coverage/platform/windows/idl -Iac_types-master/include -isystem /home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/include -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_npu_datatypes_LDFLAGS=-m64 -L/home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/lib -lgmock -lgtest -lpthread -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_float_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/cim_cluster/test -Ibuild/.gens/test_float/linux/x86_64/coverage/platform/windows/idl -Iac_types-master/include -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_float_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/cim_cluster/test -Ibuild/.gens/test_float/linux/x86_64/coverage/platform/windows/idl -Iac_types-master/include -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_float_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -ldcim_macro -lsystemc -Wl,--undefined=sc_main -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
systemc_logger_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
systemc_logger_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
systemc_logger_ARFLAGS=-cr
test_dcim_array_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/cim_cluster/test -Ibuild/.gens/test_dcim_array/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_dcim_array_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/cim_cluster/test -Ibuild/.gens/test_dcim_array/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_dcim_array_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -ldcim_macro_cpp -ldcim_macro -lsystemc -Wl,--undefined=sc_main -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
rsp_rob_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/rsp_rob/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
rsp_rob_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/rsp_rob/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
rsp_rob_ARFLAGS=-cr
cmd_dispatch_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/cmd_dispatch/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_decoder/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
cmd_dispatch_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/cmd_dispatch/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_decoder/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
cmd_dispatch_ARFLAGS=-cr
vpe_test_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/vpu/vpe/test -Ibuild/.gens/vpe_test/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/vpe/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -Iinclude/utils -Iac_types-master/include -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
vpe_test_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/vpu/vpe/test -Ibuild/.gens/vpe_test/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/vpe/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -Iinclude/utils -Iac_types-master/include -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
vpe_test_LDFLAGS=-m64 -L/home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/lib -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -lgmock -lgtest -lvpe -ldcim_macro -lsystemc_logger -lsystemc -lpthread -Wl,--undefined=sc_main -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
feat_mpu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/feat_mpu -Ibuild/.gens/feat_mpu/linux/x86_64/coverage/platform/windows/idl -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -Iinclude/utils -Iac_types-master/include -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
feat_mpu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/feat_mpu -Ibuild/.gens/feat_mpu/linux/x86_64/coverage/platform/windows/idl -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -Iinclude/utils -Iac_types-master/include -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
feat_mpu_ARFLAGS=-cr
test_cmd_dispatch_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_cmd_dispatch/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_dispatch/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_decoder/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_cmd_dispatch_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_cmd_dispatch/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_dispatch/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_decoder/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_cmd_dispatch_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -lcmd_dispatch -lcmd_decoder -lscoreboard -lsystemc_logger -lsystemc -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
common_ARFLAGS=-cr
feat_tmu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/feat_tmu/linux/x86_64/coverage/platform/windows/idl -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
feat_tmu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/feat_tmu/linux/x86_64/coverage/platform/windows/idl -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
feat_tmu_ARFLAGS=-cr
test_tlu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_tlu/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/tlu_desc_gen/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/tlu_memory/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined -fvisibility=default --coverage
test_tlu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_tlu/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/tlu_desc_gen/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/tlu_memory/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined -fvisibility=default --coverage
test_tlu_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -ltlu_desc_gen -ltlu_memory -lsystemc -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
top_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/top/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/test_cmd_scheduler/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_scheduler/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_dispatch/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_decoder/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/rsp_rob/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
top_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/top/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/test_cmd_scheduler/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_scheduler/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_dispatch/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_decoder/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/rsp_rob/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
top_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -ltest_cmd_scheduler -lcmd_scheduler -lcmd_dispatch -lcmd_decoder -lrsp_rob -lscoreboard -lsystemc_logger -lsystemc -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
cmd_test_op_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/cmd_test_op/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_decoder_op/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/instruction_generator/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_encode/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/include -fsanitize=address -fsanitize=leak -fsanitize=undefined -fvisibility=default --coverage
cmd_test_op_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/cmd_test_op/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_decoder_op/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/instruction_generator/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_encode/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/include -fsanitize=address -fsanitize=leak -fsanitize=undefined -fvisibility=default --coverage
cmd_test_op_LDFLAGS=-m64 -L/home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/lib -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -lgmock -lgtest -lcmd_decoder_op -linstruction_generator -lcmd_encode -lsystemc_logger -lsystemc -lpthread -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
dcim_cluster_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
dcim_cluster_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
dcim_cluster_ARFLAGS=-cr
tsu_memory_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/tsu_memory/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
tsu_memory_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/tsu_memory/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
tsu_memory_ARFLAGS=-cr
test_func_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_func/linux/x86_64/coverage/platform/windows/idl -Isrc/vpu -Ibuild/.gens/vpu/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/vpe/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -Iinclude/utils -Iac_types-master/include -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/include -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_func_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_func/linux/x86_64/coverage/platform/windows/idl -Isrc/vpu -Ibuild/.gens/vpu/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/vpe/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -Iinclude/utils -Iac_types-master/include -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/include -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_func_LDFLAGS=-m64 -L/home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/lib -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -lgmock -lgtest -lvpu -lvpe -ldcim_macro -lsystemc_logger -lsystemc -lpthread -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
tlu_cfg_reg_ARFLAGS=-cr
test_npu_macunit_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Iac_types-master -Ibuild/.gens/test_npu_macunit/linux/x86_64/coverage/platform/windows/idl -Iac_types-master/include -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_npu_macunit_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Iac_types-master -Ibuild/.gens/test_npu_macunit/linux/x86_64/coverage/platform/windows/idl -Iac_types-master/include -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_npu_macunit_LDFLAGS=-m64 -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
npu_datagen_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Iac_types-master -Ibuild/.gens/npu_datagen/linux/x86_64/coverage/platform/windows/idl -Iac_types-master/include -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
npu_datagen_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Iac_types-master -Ibuild/.gens/npu_datagen/linux/x86_64/coverage/platform/windows/idl -Iac_types-master/include -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
npu_datagen_ARFLAGS=-cr
tlu_memory_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/tlu_memory/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
tlu_memory_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/tlu_memory/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
tlu_memory_ARFLAGS=-cr
cmd_encode_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/cmd_encode/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
cmd_encode_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/cmd_encode/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
cmd_encode_ARFLAGS=-cr
tsu_desc_gen_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/tsu_desc_gen/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
tsu_desc_gen_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/tsu_desc_gen/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
tsu_desc_gen_ARFLAGS=-cr
vpu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/vpu -Ibuild/.gens/vpu/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/vpe/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -Iinclude/utils -Iac_types-master/include -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
vpu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/vpu -Ibuild/.gens/vpu/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/vpe/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -Iinclude/utils -Iac_types-master/include -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
vpu_ARFLAGS=-cr
test_reg_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_reg/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_reg_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_reg/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_reg_LDFLAGS=-m64 -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -lsystemc -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
dcim_macro_cpp_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
dcim_macro_cpp_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
dcim_macro_cpp_ARFLAGS=-cr
test_dcim_cluster_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/cim_cluster/test -Ibuild/.gens/test_dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_dcim_cluster_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/cim_cluster/test -Ibuild/.gens/test_dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_dcim_cluster_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -ldcim_cluster -ldcim_engine_cpp -ldcim_macro_cpp -ldcim_macro -lsystemc -Wl,--undefined=sc_main -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
scoreboard_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
scoreboard_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
scoreboard_ARFLAGS=-cr
instruction_generator_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/instruction_generator/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_encode/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
instruction_generator_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/instruction_generator/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_encode/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
instruction_generator_ARFLAGS=-cr
dcim_engine_cpp_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
dcim_engine_cpp_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
dcim_engine_cpp_ARFLAGS=-cr
test_scoreboard_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_scoreboard_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_scoreboard_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -lscoreboard -lsystemc_logger -lsystemc -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_dcim_engine_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/cim_cluster/test -Ibuild/.gens/test_dcim_engine/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_dcim_engine_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/cim_cluster/test -Ibuild/.gens/test_dcim_engine/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_dcim_engine_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -ldcim_engine_cpp -ldcim_macro_cpp -ldcim_macro -lsystemc -Wl,--undefined=sc_main -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_mpu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_mpu/linux/x86_64/coverage/platform/windows/idl -Isrc/feat_mpu -Ibuild/.gens/feat_mpu/linux/x86_64/coverage/platform/windows/idl -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -Iinclude/utils -Iac_types-master/include -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_mpu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_mpu/linux/x86_64/coverage/platform/windows/idl -Isrc/feat_mpu -Ibuild/.gens/feat_mpu/linux/x86_64/coverage/platform/windows/idl -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -Iinclude/utils -Iac_types-master/include -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_mpu_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -lfeat_mpu -llocal_mem -ldcim_cluster -ldcim_engine_cpp -ldcim_macro_cpp -ldcim_macro -lsystemc -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
cmd_decoder_op_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/cmd_decoder_op/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
cmd_decoder_op_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/cmd_decoder_op/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
cmd_decoder_op_ARFLAGS=-cr
test_vpu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/vpu/test -Ibuild/.gens/test_vpu/linux/x86_64/coverage/platform/windows/idl -Isrc/vpu -Ibuild/.gens/vpu/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/vpe/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -Iinclude/utils -Iac_types-master/include -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_vpu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/vpu/test -Ibuild/.gens/test_vpu/linux/x86_64/coverage/platform/windows/idl -Isrc/vpu -Ibuild/.gens/vpu/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/vpe/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -Iinclude/utils -Iac_types-master/include -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_vpu_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -lvpu -lvpe -lsystemc_logger -llocal_mem -ldcim_cluster -ldcim_engine_cpp -ldcim_macro_cpp -ldcim_macro -lsystemc -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_cmd_scheduler_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_cmd_scheduler/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_scheduler/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_dispatch/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_decoder/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/rsp_rob/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_cmd_scheduler_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_cmd_scheduler/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_scheduler/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_dispatch/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_decoder/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/rsp_rob/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_cmd_scheduler_ARFLAGS=-cr
cmd_scheduler_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/cmd_scheduler/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_dispatch/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_decoder/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/rsp_rob/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
cmd_scheduler_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/cmd_scheduler/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_dispatch/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/cmd_decoder/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/rsp_rob/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/scoreboard/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
cmd_scheduler_ARFLAGS=-cr
tlu_desc_gen_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/tlu_desc_gen/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
tlu_desc_gen_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/tlu_desc_gen/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
tlu_desc_gen_ARFLAGS=-cr
cmd_decoder_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/cmd_decoder/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
cmd_decoder_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/cmd_decoder/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include -isystem /home/<USER>/.xmake/packages/m/magic_enum/v0.9.6/6a64084c31f74a98adc1c5d3b6ee9c93/include/magic_enum -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
cmd_decoder_ARFLAGS=-cr
dcim_macro_CCFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
dcim_macro_ARFLAGS=-cr
ac_types_ARFLAGS=-cr
test_tsu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_tsu/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/tsu_desc_gen/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/tsu_memory/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined -fvisibility=default --coverage
test_tsu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_tsu/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/tsu_desc_gen/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/tsu_memory/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined -fvisibility=default --coverage
test_tsu_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -ltsu_desc_gen -ltsu_memory -lsystemc -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_lmem_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/local_mem/test -Ibuild/.gens/test_lmem/linux/x86_64/coverage/platform/windows/idl -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/include -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_lmem_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/local_mem/test -Ibuild/.gens/test_lmem/linux/x86_64/coverage/platform/windows/idl -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -isystem /home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/include -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_lmem_LDFLAGS=-m64 -L/home/<USER>/.xmake/packages/g/gtest/v1.15.2/fc0b8c21102d475c8f37bbbf4370b053/lib -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -lgmock -lgtest -llocal_mem -ldcim_cluster -ldcim_engine_cpp -ldcim_macro_cpp -ldcim_macro -lsystemc -lpthread -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
tsu_cfg_reg_ARFLAGS=-cr
test_rsp_rob_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_rsp_rob/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/rsp_rob/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_rsp_rob_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Ibuild/.gens/test_rsp_rob/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/rsp_rob/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_rsp_rob_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -lrsp_rob -lsystemc_logger -lsystemc -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
dev_tsu_tlu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/dev_tsu_tlu -Ibuild/.gens/dev_tsu_tlu/linux/x86_64/coverage/platform/windows/idl -Iinclude/utils -Iac_types-master/include -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
dev_tsu_tlu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/dev_tsu_tlu -Ibuild/.gens/dev_tsu_tlu/linux/x86_64/coverage/platform/windows/idl -Iinclude/utils -Iac_types-master/include -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
dev_tsu_tlu_ARFLAGS=-cr
test_dev_tsu_tlu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/dev_tsu_tlu/test -Ibuild/.gens/test_dev_tsu_tlu/linux/x86_64/coverage/platform/windows/idl -Isrc/dev_tsu_tlu -Ibuild/.gens/dev_tsu_tlu/linux/x86_64/coverage/platform/windows/idl -Iinclude/utils -Iac_types-master/include -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_dev_tsu_tlu_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/dev_tsu_tlu/test -Ibuild/.gens/test_dev_tsu_tlu/linux/x86_64/coverage/platform/windows/idl -Isrc/dev_tsu_tlu -Ibuild/.gens/dev_tsu_tlu/linux/x86_64/coverage/platform/windows/idl -Iinclude/utils -Iac_types-master/include -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_dev_tsu_tlu_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -ldev_tsu_tlu -llocal_mem -ldcim_cluster -ldcim_engine_cpp -ldcim_macro_cpp -ldcim_macro -lsystemc_logger -lsystemc -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
vpe_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/vpu/vpe -Ibuild/.gens/vpe/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -Iinclude/utils -Iac_types-master/include -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
vpe_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/vpu/vpe -Ibuild/.gens/vpe/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/systemc_logger/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -Iinclude/utils -Iac_types-master/include -DENABLE_LOGGING -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
vpe_ARFLAGS=-cr
utils_ARFLAGS=-cr
test_storage_compute_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/cim_cluster/test -Ibuild/.gens/test_storage_compute/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_storage_compute_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/cim_cluster/test -Ibuild/.gens/test_storage_compute/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
test_storage_compute_LDFLAGS=-m64 -Lbuild/linux/x86_64/coverage -L/usr/local/systemc-3.0.0/lib-linux64 -Wl,-rpath=/usr/local/systemc-3.0.0/lib-linux64 -ldcim_macro -lsystemc -Wl,--undefined=sc_main -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
local_mem_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
local_mem_CXXFLAGS=-Qunused-arguments -m64 -g -Wall -Wextra -Wpedantic -O0 -std=c++17 -Isrc/local_mem -Ibuild/.gens/local_mem/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_cluster/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_engine_cpp/linux/x86_64/coverage/platform/windows/idl -Ibuild/.gens/dcim_macro_cpp/linux/x86_64/coverage/platform/windows/idl -Isrc/cim_cluster/inc -Ibuild/.gens/dcim_macro/linux/x86_64/coverage/platform/windows/idl -I/usr/local/systemc-3.0.0/include -Iinclude -DSC_INCLUDE_DYNAMIC_PROCESSES -fsanitize=address -fsanitize=leak -fsanitize=undefined --coverage
local_mem_ARFLAGS=-cr

default:  test_npu_datatypes test_float systemc_logger test_dcim_array rsp_rob cmd_dispatch vpe_test feat_mpu test_cmd_dispatch common feat_tmu test_tlu top cmd_test_op dcim_cluster tsu_memory test_func tlu_cfg_reg test_npu_macunit npu_datagen tlu_memory cmd_encode tsu_desc_gen vpu test_reg dcim_macro_cpp test_dcim_cluster scoreboard instruction_generator dcim_engine_cpp test_scoreboard test_dcim_engine test_mpu cmd_decoder_op test_vpu test_cmd_scheduler cmd_scheduler tlu_desc_gen cmd_decoder dcim_macro ac_types test_tsu test_lmem tsu_cfg_reg test_rsp_rob dev_tsu_tlu test_dev_tsu_tlu vpe utils test_storage_compute local_mem

all:  test_npu_datatypes test_float systemc_logger test_dcim_array rsp_rob cmd_dispatch vpe_test feat_mpu test_cmd_dispatch common feat_tmu test_tlu top cmd_test_op dcim_cluster tsu_memory test_func tlu_cfg_reg test_npu_macunit npu_datagen tlu_memory cmd_encode tsu_desc_gen vpu test_reg dcim_macro_cpp test_dcim_cluster scoreboard instruction_generator dcim_engine_cpp test_scoreboard test_dcim_engine test_mpu cmd_decoder_op test_vpu test_cmd_scheduler cmd_scheduler tlu_desc_gen cmd_decoder dcim_macro ac_types test_tsu test_lmem tsu_cfg_reg test_rsp_rob dev_tsu_tlu test_dev_tsu_tlu vpe utils test_storage_compute local_mem

.PHONY: default all  test_npu_datatypes test_float systemc_logger test_dcim_array rsp_rob cmd_dispatch vpe_test feat_mpu test_cmd_dispatch common feat_tmu test_tlu top cmd_test_op dcim_cluster tsu_memory test_func tlu_cfg_reg test_npu_macunit npu_datagen tlu_memory cmd_encode tsu_desc_gen vpu test_reg dcim_macro_cpp test_dcim_cluster scoreboard instruction_generator dcim_engine_cpp test_scoreboard test_dcim_engine test_mpu cmd_decoder_op test_vpu test_cmd_scheduler cmd_scheduler tlu_desc_gen cmd_decoder dcim_macro ac_types test_tsu test_lmem tsu_cfg_reg test_rsp_rob dev_tsu_tlu test_dev_tsu_tlu vpe utils test_storage_compute local_mem

test_npu_datatypes: build/linux/x86_64/coverage/test_npu_datatypes
build/linux/x86_64/coverage/test_npu_datatypes: build/linux/x86_64/coverage/libac_types.a build/.objs/test_npu_datatypes/linux/x86_64/coverage/ac_types-master/test/npu_datatype_test.cpp.o
	@echo linking.coverage test_npu_datatypes
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_npu_datatypes_LD) -o build/linux/x86_64/coverage/test_npu_datatypes build/.objs/test_npu_datatypes/linux/x86_64/coverage/ac_types-master/test/npu_datatype_test.cpp.o $(test_npu_datatypes_LDFLAGS)

build/.objs/test_npu_datatypes/linux/x86_64/coverage/ac_types-master/test/npu_datatype_test.cpp.o: ac_types-master/test/npu_datatype_test.cpp
	@echo ccache compiling.coverage ac_types-master/test/npu_datatype_test.cpp
	@mkdir -p build/.objs/test_npu_datatypes/linux/x86_64/coverage/ac_types-master/test
	$(VV)$(test_npu_datatypes_CXX) -c $(test_npu_datatypes_CXXFLAGS) -o build/.objs/test_npu_datatypes/linux/x86_64/coverage/ac_types-master/test/npu_datatype_test.cpp.o ac_types-master/test/npu_datatype_test.cpp

test_float: build/linux/x86_64/coverage/test_float
build/linux/x86_64/coverage/test_float: build/linux/x86_64/coverage/libac_types.a build/linux/x86_64/coverage/libdcim_macro.a build/.objs/test_float/linux/x86_64/coverage/src/cim_cluster/test/test_float.cpp.o
	@echo linking.coverage test_float
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_float_LD) -o build/linux/x86_64/coverage/test_float build/.objs/test_float/linux/x86_64/coverage/src/cim_cluster/test/test_float.cpp.o $(test_float_LDFLAGS)

build/.objs/test_float/linux/x86_64/coverage/src/cim_cluster/test/test_float.cpp.o: src/cim_cluster/test/test_float.cpp
	@echo ccache compiling.coverage src/cim_cluster/test/test_float.cpp
	@mkdir -p build/.objs/test_float/linux/x86_64/coverage/src/cim_cluster/test
	$(VV)$(test_float_CXX) -c $(test_float_CXXFLAGS) -o build/.objs/test_float/linux/x86_64/coverage/src/cim_cluster/test/test_float.cpp.o src/cim_cluster/test/test_float.cpp

systemc_logger: build/linux/x86_64/coverage/libsystemc_logger.a
build/linux/x86_64/coverage/libsystemc_logger.a: build/linux/x86_64/coverage/libcommon.a build/.objs/systemc_logger/linux/x86_64/coverage/include/utils/systemc_logger.cpp.o
	@echo linking.coverage libsystemc_logger.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(systemc_logger_AR) $(systemc_logger_ARFLAGS) build/linux/x86_64/coverage/libsystemc_logger.a build/.objs/systemc_logger/linux/x86_64/coverage/include/utils/systemc_logger.cpp.o

build/.objs/systemc_logger/linux/x86_64/coverage/include/utils/systemc_logger.cpp.o: include/utils/systemc_logger.cpp
	@echo ccache compiling.coverage include/utils/systemc_logger.cpp
	@mkdir -p build/.objs/systemc_logger/linux/x86_64/coverage/include/utils
	$(VV)$(systemc_logger_CXX) -c $(systemc_logger_CXXFLAGS) -o build/.objs/systemc_logger/linux/x86_64/coverage/include/utils/systemc_logger.cpp.o include/utils/systemc_logger.cpp

test_dcim_array: build/linux/x86_64/coverage/test_dcim_array
build/linux/x86_64/coverage/test_dcim_array: build/linux/x86_64/coverage/libdcim_macro_cpp.a build/.objs/test_dcim_array/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_array.cpp.o
	@echo linking.coverage test_dcim_array
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_dcim_array_LD) -o build/linux/x86_64/coverage/test_dcim_array build/.objs/test_dcim_array/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_array.cpp.o $(test_dcim_array_LDFLAGS)

build/.objs/test_dcim_array/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_array.cpp.o: src/cim_cluster/test/test_dcim_array.cpp
	@echo ccache compiling.coverage src/cim_cluster/test/test_dcim_array.cpp
	@mkdir -p build/.objs/test_dcim_array/linux/x86_64/coverage/src/cim_cluster/test
	$(VV)$(test_dcim_array_CXX) -c $(test_dcim_array_CXXFLAGS) -o build/.objs/test_dcim_array/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_array.cpp.o src/cim_cluster/test/test_dcim_array.cpp

rsp_rob: build/linux/x86_64/coverage/librsp_rob.a
build/linux/x86_64/coverage/librsp_rob.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libsystemc_logger.a build/.objs/rsp_rob/linux/x86_64/coverage/src/cmd_scheduler/rsp_rob.cpp.o
	@echo linking.coverage librsp_rob.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(rsp_rob_AR) $(rsp_rob_ARFLAGS) build/linux/x86_64/coverage/librsp_rob.a build/.objs/rsp_rob/linux/x86_64/coverage/src/cmd_scheduler/rsp_rob.cpp.o

build/.objs/rsp_rob/linux/x86_64/coverage/src/cmd_scheduler/rsp_rob.cpp.o: src/cmd_scheduler/rsp_rob.cpp
	@echo ccache compiling.coverage src/cmd_scheduler/rsp_rob.cpp
	@mkdir -p build/.objs/rsp_rob/linux/x86_64/coverage/src/cmd_scheduler
	$(VV)$(rsp_rob_CXX) -c $(rsp_rob_CXXFLAGS) -o build/.objs/rsp_rob/linux/x86_64/coverage/src/cmd_scheduler/rsp_rob.cpp.o src/cmd_scheduler/rsp_rob.cpp

cmd_dispatch: build/linux/x86_64/coverage/libcmd_dispatch.a
build/linux/x86_64/coverage/libcmd_dispatch.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libcmd_decoder.a build/linux/x86_64/coverage/libscoreboard.a build/linux/x86_64/coverage/libsystemc_logger.a build/.objs/cmd_dispatch/linux/x86_64/coverage/src/cmd_scheduler/cmd_dispatch.cpp.o
	@echo linking.coverage libcmd_dispatch.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(cmd_dispatch_AR) $(cmd_dispatch_ARFLAGS) build/linux/x86_64/coverage/libcmd_dispatch.a build/.objs/cmd_dispatch/linux/x86_64/coverage/src/cmd_scheduler/cmd_dispatch.cpp.o

build/.objs/cmd_dispatch/linux/x86_64/coverage/src/cmd_scheduler/cmd_dispatch.cpp.o: src/cmd_scheduler/cmd_dispatch.cpp
	@echo ccache compiling.coverage src/cmd_scheduler/cmd_dispatch.cpp
	@mkdir -p build/.objs/cmd_dispatch/linux/x86_64/coverage/src/cmd_scheduler
	$(VV)$(cmd_dispatch_CXX) -c $(cmd_dispatch_CXXFLAGS) -o build/.objs/cmd_dispatch/linux/x86_64/coverage/src/cmd_scheduler/cmd_dispatch.cpp.o src/cmd_scheduler/cmd_dispatch.cpp

vpe_test: build/linux/x86_64/coverage/vpe_test
build/linux/x86_64/coverage/vpe_test: build/linux/x86_64/coverage/libvpe.a build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libutils.a build/.objs/vpe_test/linux/x86_64/coverage/src/vpu/vpe/test/vpe_test.cpp.o build/.objs/vpe_test/linux/x86_64/coverage/src/vpu/vpe/test/test_case_generator.cpp.o
	@echo linking.coverage vpe_test
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(vpe_test_LD) -o build/linux/x86_64/coverage/vpe_test build/.objs/vpe_test/linux/x86_64/coverage/src/vpu/vpe/test/vpe_test.cpp.o build/.objs/vpe_test/linux/x86_64/coverage/src/vpu/vpe/test/test_case_generator.cpp.o $(vpe_test_LDFLAGS)

build/.objs/vpe_test/linux/x86_64/coverage/src/vpu/vpe/test/vpe_test.cpp.o: src/vpu/vpe/test/vpe_test.cpp
	@echo ccache compiling.coverage src/vpu/vpe/test/vpe_test.cpp
	@mkdir -p build/.objs/vpe_test/linux/x86_64/coverage/src/vpu/vpe/test
	$(VV)$(vpe_test_CXX) -c $(vpe_test_CXXFLAGS) -o build/.objs/vpe_test/linux/x86_64/coverage/src/vpu/vpe/test/vpe_test.cpp.o src/vpu/vpe/test/vpe_test.cpp

build/.objs/vpe_test/linux/x86_64/coverage/src/vpu/vpe/test/test_case_generator.cpp.o: src/vpu/vpe/test/test_case_generator.cpp
	@echo ccache compiling.coverage src/vpu/vpe/test/test_case_generator.cpp
	@mkdir -p build/.objs/vpe_test/linux/x86_64/coverage/src/vpu/vpe/test
	$(VV)$(vpe_test_CXX) -c $(vpe_test_CXXFLAGS) -o build/.objs/vpe_test/linux/x86_64/coverage/src/vpu/vpe/test/test_case_generator.cpp.o src/vpu/vpe/test/test_case_generator.cpp

feat_mpu: build/linux/x86_64/coverage/libfeat_mpu.a
build/linux/x86_64/coverage/libfeat_mpu.a: build/linux/x86_64/coverage/libdcim_cluster.a build/linux/x86_64/coverage/liblocal_mem.a build/linux/x86_64/coverage/libutils.a build/.objs/feat_mpu/linux/x86_64/coverage/src/feat_mpu/feat_mpu.cpp.o
	@echo linking.coverage libfeat_mpu.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(feat_mpu_AR) $(feat_mpu_ARFLAGS) build/linux/x86_64/coverage/libfeat_mpu.a build/.objs/feat_mpu/linux/x86_64/coverage/src/feat_mpu/feat_mpu.cpp.o

build/.objs/feat_mpu/linux/x86_64/coverage/src/feat_mpu/feat_mpu.cpp.o: src/feat_mpu/feat_mpu.cpp
	@echo ccache compiling.coverage src/feat_mpu/feat_mpu.cpp
	@mkdir -p build/.objs/feat_mpu/linux/x86_64/coverage/src/feat_mpu
	$(VV)$(feat_mpu_CXX) -c $(feat_mpu_CXXFLAGS) -o build/.objs/feat_mpu/linux/x86_64/coverage/src/feat_mpu/feat_mpu.cpp.o src/feat_mpu/feat_mpu.cpp

test_cmd_dispatch: build/linux/x86_64/coverage/test_cmd_dispatch
build/linux/x86_64/coverage/test_cmd_dispatch: build/linux/x86_64/coverage/libcmd_dispatch.a build/.objs/test_cmd_dispatch/linux/x86_64/coverage/src/cmd_scheduler/test/test_cmd_dispatch.cpp.o
	@echo linking.coverage test_cmd_dispatch
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_cmd_dispatch_LD) -o build/linux/x86_64/coverage/test_cmd_dispatch build/.objs/test_cmd_dispatch/linux/x86_64/coverage/src/cmd_scheduler/test/test_cmd_dispatch.cpp.o $(test_cmd_dispatch_LDFLAGS)

build/.objs/test_cmd_dispatch/linux/x86_64/coverage/src/cmd_scheduler/test/test_cmd_dispatch.cpp.o: src/cmd_scheduler/test/test_cmd_dispatch.cpp
	@echo ccache compiling.coverage src/cmd_scheduler/test/test_cmd_dispatch.cpp
	@mkdir -p build/.objs/test_cmd_dispatch/linux/x86_64/coverage/src/cmd_scheduler/test
	$(VV)$(test_cmd_dispatch_CXX) -c $(test_cmd_dispatch_CXXFLAGS) -o build/.objs/test_cmd_dispatch/linux/x86_64/coverage/src/cmd_scheduler/test/test_cmd_dispatch.cpp.o src/cmd_scheduler/test/test_cmd_dispatch.cpp

common: build/linux/x86_64/coverage/libcommon.a
build/linux/x86_64/coverage/libcommon.a:
	@echo linking.coverage libcommon.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(common_AR) $(common_ARFLAGS) build/linux/x86_64/coverage/libcommon.a

feat_tmu: build/linux/x86_64/coverage/libfeat_tmu.a
build/linux/x86_64/coverage/libfeat_tmu.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/liblocal_mem.a build/.objs/feat_tmu/linux/x86_64/coverage/src/feat_tmu/feat_tmu.cpp.o
	@echo linking.coverage libfeat_tmu.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(feat_tmu_AR) $(feat_tmu_ARFLAGS) build/linux/x86_64/coverage/libfeat_tmu.a build/.objs/feat_tmu/linux/x86_64/coverage/src/feat_tmu/feat_tmu.cpp.o

build/.objs/feat_tmu/linux/x86_64/coverage/src/feat_tmu/feat_tmu.cpp.o: src/feat_tmu/feat_tmu.cpp
	@echo ccache compiling.coverage src/feat_tmu/feat_tmu.cpp
	@mkdir -p build/.objs/feat_tmu/linux/x86_64/coverage/src/feat_tmu
	$(VV)$(feat_tmu_CXX) -c $(feat_tmu_CXXFLAGS) -o build/.objs/feat_tmu/linux/x86_64/coverage/src/feat_tmu/feat_tmu.cpp.o src/feat_tmu/feat_tmu.cpp

test_tlu: build/linux/x86_64/coverage/test_tlu
build/linux/x86_64/coverage/test_tlu: build/linux/x86_64/coverage/libtlu_desc_gen.a build/linux/x86_64/coverage/libtlu_memory.a build/.objs/test_tlu/linux/x86_64/coverage/src/tlu/test/test_tlu.cpp.o
	@echo linking.coverage test_tlu
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_tlu_LD) -o build/linux/x86_64/coverage/test_tlu build/.objs/test_tlu/linux/x86_64/coverage/src/tlu/test/test_tlu.cpp.o $(test_tlu_LDFLAGS)

build/.objs/test_tlu/linux/x86_64/coverage/src/tlu/test/test_tlu.cpp.o: src/tlu/test/test_tlu.cpp
	@echo ccache compiling.coverage src/tlu/test/test_tlu.cpp
	@mkdir -p build/.objs/test_tlu/linux/x86_64/coverage/src/tlu/test
	$(VV)$(test_tlu_CXX) -c $(test_tlu_CXXFLAGS) -o build/.objs/test_tlu/linux/x86_64/coverage/src/tlu/test/test_tlu.cpp.o src/tlu/test/test_tlu.cpp

top: build/linux/x86_64/coverage/top
build/linux/x86_64/coverage/top: build/linux/x86_64/coverage/libcmd_scheduler.a build/linux/x86_64/coverage/libtest_cmd_scheduler.a build/.objs/top/linux/x86_64/coverage/src/cmd_scheduler/test/top.cpp.o
	@echo linking.coverage top
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(top_LD) -o build/linux/x86_64/coverage/top build/.objs/top/linux/x86_64/coverage/src/cmd_scheduler/test/top.cpp.o $(top_LDFLAGS)

build/.objs/top/linux/x86_64/coverage/src/cmd_scheduler/test/top.cpp.o: src/cmd_scheduler/test/top.cpp
	@echo ccache compiling.coverage src/cmd_scheduler/test/top.cpp
	@mkdir -p build/.objs/top/linux/x86_64/coverage/src/cmd_scheduler/test
	$(VV)$(top_CXX) -c $(top_CXXFLAGS) -o build/.objs/top/linux/x86_64/coverage/src/cmd_scheduler/test/top.cpp.o src/cmd_scheduler/test/top.cpp

cmd_test_op: build/linux/x86_64/coverage/cmd_test_op
build/linux/x86_64/coverage/cmd_test_op: build/linux/x86_64/coverage/libcmd_decoder_op.a build/linux/x86_64/coverage/libcmd_encode.a build/linux/x86_64/coverage/libinstruction_generator.a build/.objs/cmd_test_op/linux/x86_64/coverage/src/cmd_decoder/cmd_test.cpp.o
	@echo linking.coverage cmd_test_op
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(cmd_test_op_LD) -o build/linux/x86_64/coverage/cmd_test_op build/.objs/cmd_test_op/linux/x86_64/coverage/src/cmd_decoder/cmd_test.cpp.o $(cmd_test_op_LDFLAGS)

build/.objs/cmd_test_op/linux/x86_64/coverage/src/cmd_decoder/cmd_test.cpp.o: src/cmd_decoder/cmd_test.cpp
	@echo ccache compiling.coverage src/cmd_decoder/cmd_test.cpp
	@mkdir -p build/.objs/cmd_test_op/linux/x86_64/coverage/src/cmd_decoder
	$(VV)$(cmd_test_op_CXX) -c $(cmd_test_op_CXXFLAGS) -o build/.objs/cmd_test_op/linux/x86_64/coverage/src/cmd_decoder/cmd_test.cpp.o src/cmd_decoder/cmd_test.cpp

dcim_cluster: build/linux/x86_64/coverage/libdcim_cluster.a
build/linux/x86_64/coverage/libdcim_cluster.a: build/linux/x86_64/coverage/libdcim_engine_cpp.a build/.objs/dcim_cluster/linux/x86_64/coverage/src/cim_cluster/src/dcim_cluster.cpp.o
	@echo linking.coverage libdcim_cluster.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(dcim_cluster_AR) $(dcim_cluster_ARFLAGS) build/linux/x86_64/coverage/libdcim_cluster.a build/.objs/dcim_cluster/linux/x86_64/coverage/src/cim_cluster/src/dcim_cluster.cpp.o

build/.objs/dcim_cluster/linux/x86_64/coverage/src/cim_cluster/src/dcim_cluster.cpp.o: src/cim_cluster/src/dcim_cluster.cpp
	@echo ccache compiling.coverage src/cim_cluster/src/dcim_cluster.cpp
	@mkdir -p build/.objs/dcim_cluster/linux/x86_64/coverage/src/cim_cluster/src
	$(VV)$(dcim_cluster_CXX) -c $(dcim_cluster_CXXFLAGS) -o build/.objs/dcim_cluster/linux/x86_64/coverage/src/cim_cluster/src/dcim_cluster.cpp.o src/cim_cluster/src/dcim_cluster.cpp

tsu_memory: build/linux/x86_64/coverage/libtsu_memory.a
build/linux/x86_64/coverage/libtsu_memory.a: build/linux/x86_64/coverage/libcommon.a build/.objs/tsu_memory/linux/x86_64/coverage/src/tsu/test_memory/mem.cpp.o
	@echo linking.coverage libtsu_memory.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(tsu_memory_AR) $(tsu_memory_ARFLAGS) build/linux/x86_64/coverage/libtsu_memory.a build/.objs/tsu_memory/linux/x86_64/coverage/src/tsu/test_memory/mem.cpp.o

build/.objs/tsu_memory/linux/x86_64/coverage/src/tsu/test_memory/mem.cpp.o: src/tsu/test_memory/mem.cpp
	@echo ccache compiling.coverage src/tsu/test_memory/mem.cpp
	@mkdir -p build/.objs/tsu_memory/linux/x86_64/coverage/src/tsu/test_memory
	$(VV)$(tsu_memory_CXX) -c $(tsu_memory_CXXFLAGS) -o build/.objs/tsu_memory/linux/x86_64/coverage/src/tsu/test_memory/mem.cpp.o src/tsu/test_memory/mem.cpp

test_func: build/linux/x86_64/coverage/test_func
build/linux/x86_64/coverage/test_func: build/linux/x86_64/coverage/libvpu.a build/.objs/test_func/linux/x86_64/coverage/src/vpu/test/test_data_padding.cpp.o
	@echo linking.coverage test_func
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_func_LD) -o build/linux/x86_64/coverage/test_func build/.objs/test_func/linux/x86_64/coverage/src/vpu/test/test_data_padding.cpp.o $(test_func_LDFLAGS)

build/.objs/test_func/linux/x86_64/coverage/src/vpu/test/test_data_padding.cpp.o: src/vpu/test/test_data_padding.cpp
	@echo ccache compiling.coverage src/vpu/test/test_data_padding.cpp
	@mkdir -p build/.objs/test_func/linux/x86_64/coverage/src/vpu/test
	$(VV)$(test_func_CXX) -c $(test_func_CXXFLAGS) -o build/.objs/test_func/linux/x86_64/coverage/src/vpu/test/test_data_padding.cpp.o src/vpu/test/test_data_padding.cpp

tlu_cfg_reg: build/linux/x86_64/coverage/libtlu_cfg_reg.a
build/linux/x86_64/coverage/libtlu_cfg_reg.a: build/linux/x86_64/coverage/libcommon.a
	@echo linking.coverage libtlu_cfg_reg.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(tlu_cfg_reg_AR) $(tlu_cfg_reg_ARFLAGS) build/linux/x86_64/coverage/libtlu_cfg_reg.a

test_npu_macunit: build/linux/x86_64/coverage/test_npu_macunit
build/linux/x86_64/coverage/test_npu_macunit: build/linux/x86_64/coverage/libac_types.a build/.objs/test_npu_macunit/linux/x86_64/coverage/ac_types-master/test/test_npu_macunit.cpp.o build/.objs/test_npu_macunit/linux/x86_64/coverage/ac_types-master/npu_datagen.cpp.o
	@echo linking.coverage test_npu_macunit
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_npu_macunit_LD) -o build/linux/x86_64/coverage/test_npu_macunit build/.objs/test_npu_macunit/linux/x86_64/coverage/ac_types-master/test/test_npu_macunit.cpp.o build/.objs/test_npu_macunit/linux/x86_64/coverage/ac_types-master/npu_datagen.cpp.o $(test_npu_macunit_LDFLAGS)

build/.objs/test_npu_macunit/linux/x86_64/coverage/ac_types-master/test/test_npu_macunit.cpp.o: ac_types-master/test/test_npu_macunit.cpp
	@echo ccache compiling.coverage ac_types-master/test/test_npu_macunit.cpp
	@mkdir -p build/.objs/test_npu_macunit/linux/x86_64/coverage/ac_types-master/test
	$(VV)$(test_npu_macunit_CXX) -c $(test_npu_macunit_CXXFLAGS) -o build/.objs/test_npu_macunit/linux/x86_64/coverage/ac_types-master/test/test_npu_macunit.cpp.o ac_types-master/test/test_npu_macunit.cpp

build/.objs/test_npu_macunit/linux/x86_64/coverage/ac_types-master/npu_datagen.cpp.o: ac_types-master/npu_datagen.cpp
	@echo ccache compiling.coverage ac_types-master/npu_datagen.cpp
	@mkdir -p build/.objs/test_npu_macunit/linux/x86_64/coverage/ac_types-master
	$(VV)$(test_npu_macunit_CXX) -c $(test_npu_macunit_CXXFLAGS) -o build/.objs/test_npu_macunit/linux/x86_64/coverage/ac_types-master/npu_datagen.cpp.o ac_types-master/npu_datagen.cpp

npu_datagen: build/linux/x86_64/coverage/libnpu_datagen.a
build/linux/x86_64/coverage/libnpu_datagen.a: build/linux/x86_64/coverage/libac_types.a build/.objs/npu_datagen/linux/x86_64/coverage/ac_types-master/npu_datagen.cpp.o
	@echo linking.coverage libnpu_datagen.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(npu_datagen_AR) $(npu_datagen_ARFLAGS) build/linux/x86_64/coverage/libnpu_datagen.a build/.objs/npu_datagen/linux/x86_64/coverage/ac_types-master/npu_datagen.cpp.o

build/.objs/npu_datagen/linux/x86_64/coverage/ac_types-master/npu_datagen.cpp.o: ac_types-master/npu_datagen.cpp
	@echo ccache compiling.coverage ac_types-master/npu_datagen.cpp
	@mkdir -p build/.objs/npu_datagen/linux/x86_64/coverage/ac_types-master
	$(VV)$(npu_datagen_CXX) -c $(npu_datagen_CXXFLAGS) -o build/.objs/npu_datagen/linux/x86_64/coverage/ac_types-master/npu_datagen.cpp.o ac_types-master/npu_datagen.cpp

tlu_memory: build/linux/x86_64/coverage/libtlu_memory.a
build/linux/x86_64/coverage/libtlu_memory.a: build/linux/x86_64/coverage/libcommon.a build/.objs/tlu_memory/linux/x86_64/coverage/src/tlu/test_memory/mem.cpp.o
	@echo linking.coverage libtlu_memory.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(tlu_memory_AR) $(tlu_memory_ARFLAGS) build/linux/x86_64/coverage/libtlu_memory.a build/.objs/tlu_memory/linux/x86_64/coverage/src/tlu/test_memory/mem.cpp.o

build/.objs/tlu_memory/linux/x86_64/coverage/src/tlu/test_memory/mem.cpp.o: src/tlu/test_memory/mem.cpp
	@echo ccache compiling.coverage src/tlu/test_memory/mem.cpp
	@mkdir -p build/.objs/tlu_memory/linux/x86_64/coverage/src/tlu/test_memory
	$(VV)$(tlu_memory_CXX) -c $(tlu_memory_CXXFLAGS) -o build/.objs/tlu_memory/linux/x86_64/coverage/src/tlu/test_memory/mem.cpp.o src/tlu/test_memory/mem.cpp

cmd_encode: build/linux/x86_64/coverage/libcmd_encode.a
build/linux/x86_64/coverage/libcmd_encode.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libsystemc_logger.a build/.objs/cmd_encode/linux/x86_64/coverage/src/cmd_decoder/cmd_encode.cpp.o
	@echo linking.coverage libcmd_encode.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(cmd_encode_AR) $(cmd_encode_ARFLAGS) build/linux/x86_64/coverage/libcmd_encode.a build/.objs/cmd_encode/linux/x86_64/coverage/src/cmd_decoder/cmd_encode.cpp.o

build/.objs/cmd_encode/linux/x86_64/coverage/src/cmd_decoder/cmd_encode.cpp.o: src/cmd_decoder/cmd_encode.cpp
	@echo ccache compiling.coverage src/cmd_decoder/cmd_encode.cpp
	@mkdir -p build/.objs/cmd_encode/linux/x86_64/coverage/src/cmd_decoder
	$(VV)$(cmd_encode_CXX) -c $(cmd_encode_CXXFLAGS) -o build/.objs/cmd_encode/linux/x86_64/coverage/src/cmd_decoder/cmd_encode.cpp.o src/cmd_decoder/cmd_encode.cpp

tsu_desc_gen: build/linux/x86_64/coverage/libtsu_desc_gen.a
build/linux/x86_64/coverage/libtsu_desc_gen.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libtsu_cfg_reg.a build/.objs/tsu_desc_gen/linux/x86_64/coverage/src/tsu/tsu_desc_gen/tsu_desc_gen.cpp.o
	@echo linking.coverage libtsu_desc_gen.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(tsu_desc_gen_AR) $(tsu_desc_gen_ARFLAGS) build/linux/x86_64/coverage/libtsu_desc_gen.a build/.objs/tsu_desc_gen/linux/x86_64/coverage/src/tsu/tsu_desc_gen/tsu_desc_gen.cpp.o

build/.objs/tsu_desc_gen/linux/x86_64/coverage/src/tsu/tsu_desc_gen/tsu_desc_gen.cpp.o: src/tsu/tsu_desc_gen/tsu_desc_gen.cpp
	@echo ccache compiling.coverage src/tsu/tsu_desc_gen/tsu_desc_gen.cpp
	@mkdir -p build/.objs/tsu_desc_gen/linux/x86_64/coverage/src/tsu/tsu_desc_gen
	$(VV)$(tsu_desc_gen_CXX) -c $(tsu_desc_gen_CXXFLAGS) -o build/.objs/tsu_desc_gen/linux/x86_64/coverage/src/tsu/tsu_desc_gen/tsu_desc_gen.cpp.o src/tsu/tsu_desc_gen/tsu_desc_gen.cpp

vpu: build/linux/x86_64/coverage/libvpu.a
build/linux/x86_64/coverage/libvpu.a: build/linux/x86_64/coverage/libvpe.a build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu.cpp.o build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_rd_req_tx.cpp.o build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_rd_ack_rx.cpp.o build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_wr_req_tx.cpp.o
	@echo linking.coverage libvpu.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(vpu_AR) $(vpu_ARFLAGS) build/linux/x86_64/coverage/libvpu.a build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu.cpp.o build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_rd_req_tx.cpp.o build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_rd_ack_rx.cpp.o build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_wr_req_tx.cpp.o

build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu.cpp.o: src/vpu/vpu.cpp
	@echo ccache compiling.coverage src/vpu/vpu.cpp
	@mkdir -p build/.objs/vpu/linux/x86_64/coverage/src/vpu
	$(VV)$(vpu_CXX) -c $(vpu_CXXFLAGS) -o build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu.cpp.o src/vpu/vpu.cpp

build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_rd_req_tx.cpp.o: src/vpu/vpu_rd_req_tx.cpp
	@echo ccache compiling.coverage src/vpu/vpu_rd_req_tx.cpp
	@mkdir -p build/.objs/vpu/linux/x86_64/coverage/src/vpu
	$(VV)$(vpu_CXX) -c $(vpu_CXXFLAGS) -o build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_rd_req_tx.cpp.o src/vpu/vpu_rd_req_tx.cpp

build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_rd_ack_rx.cpp.o: src/vpu/vpu_rd_ack_rx.cpp
	@echo ccache compiling.coverage src/vpu/vpu_rd_ack_rx.cpp
	@mkdir -p build/.objs/vpu/linux/x86_64/coverage/src/vpu
	$(VV)$(vpu_CXX) -c $(vpu_CXXFLAGS) -o build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_rd_ack_rx.cpp.o src/vpu/vpu_rd_ack_rx.cpp

build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_wr_req_tx.cpp.o: src/vpu/vpu_wr_req_tx.cpp
	@echo ccache compiling.coverage src/vpu/vpu_wr_req_tx.cpp
	@mkdir -p build/.objs/vpu/linux/x86_64/coverage/src/vpu
	$(VV)$(vpu_CXX) -c $(vpu_CXXFLAGS) -o build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_wr_req_tx.cpp.o src/vpu/vpu_wr_req_tx.cpp

test_reg: build/linux/x86_64/coverage/test_reg
build/linux/x86_64/coverage/test_reg: build/linux/x86_64/coverage/libtsu_cfg_reg.a build/.objs/test_reg/linux/x86_64/coverage/src/tsu/tsu_cfg_reg/tsu_cfg_reg.cpp.o
	@echo linking.coverage test_reg
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_reg_LD) -o build/linux/x86_64/coverage/test_reg build/.objs/test_reg/linux/x86_64/coverage/src/tsu/tsu_cfg_reg/tsu_cfg_reg.cpp.o $(test_reg_LDFLAGS)

build/.objs/test_reg/linux/x86_64/coverage/src/tsu/tsu_cfg_reg/tsu_cfg_reg.cpp.o: src/tsu/tsu_cfg_reg/tsu_cfg_reg.cpp
	@echo ccache compiling.coverage src/tsu/tsu_cfg_reg/tsu_cfg_reg.cpp
	@mkdir -p build/.objs/test_reg/linux/x86_64/coverage/src/tsu/tsu_cfg_reg
	$(VV)$(test_reg_CXX) -c $(test_reg_CXXFLAGS) -o build/.objs/test_reg/linux/x86_64/coverage/src/tsu/tsu_cfg_reg/tsu_cfg_reg.cpp.o src/tsu/tsu_cfg_reg/tsu_cfg_reg.cpp

dcim_macro_cpp: build/linux/x86_64/coverage/libdcim_macro_cpp.a
build/linux/x86_64/coverage/libdcim_macro_cpp.a: build/linux/x86_64/coverage/libdcim_macro.a build/.objs/dcim_macro_cpp/linux/x86_64/coverage/src/cim_cluster/src/dcim_array.cpp.o
	@echo linking.coverage libdcim_macro_cpp.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(dcim_macro_cpp_AR) $(dcim_macro_cpp_ARFLAGS) build/linux/x86_64/coverage/libdcim_macro_cpp.a build/.objs/dcim_macro_cpp/linux/x86_64/coverage/src/cim_cluster/src/dcim_array.cpp.o

build/.objs/dcim_macro_cpp/linux/x86_64/coverage/src/cim_cluster/src/dcim_array.cpp.o: src/cim_cluster/src/dcim_array.cpp
	@echo ccache compiling.coverage src/cim_cluster/src/dcim_array.cpp
	@mkdir -p build/.objs/dcim_macro_cpp/linux/x86_64/coverage/src/cim_cluster/src
	$(VV)$(dcim_macro_cpp_CXX) -c $(dcim_macro_cpp_CXXFLAGS) -o build/.objs/dcim_macro_cpp/linux/x86_64/coverage/src/cim_cluster/src/dcim_array.cpp.o src/cim_cluster/src/dcim_array.cpp

test_dcim_cluster: build/linux/x86_64/coverage/test_dcim_cluster
build/linux/x86_64/coverage/test_dcim_cluster: build/linux/x86_64/coverage/libdcim_cluster.a build/.objs/test_dcim_cluster/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_cluster.cpp.o
	@echo linking.coverage test_dcim_cluster
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_dcim_cluster_LD) -o build/linux/x86_64/coverage/test_dcim_cluster build/.objs/test_dcim_cluster/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_cluster.cpp.o $(test_dcim_cluster_LDFLAGS)

build/.objs/test_dcim_cluster/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_cluster.cpp.o: src/cim_cluster/test/test_dcim_cluster.cpp
	@echo ccache compiling.coverage src/cim_cluster/test/test_dcim_cluster.cpp
	@mkdir -p build/.objs/test_dcim_cluster/linux/x86_64/coverage/src/cim_cluster/test
	$(VV)$(test_dcim_cluster_CXX) -c $(test_dcim_cluster_CXXFLAGS) -o build/.objs/test_dcim_cluster/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_cluster.cpp.o src/cim_cluster/test/test_dcim_cluster.cpp

scoreboard: build/linux/x86_64/coverage/libscoreboard.a
build/linux/x86_64/coverage/libscoreboard.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libsystemc_logger.a build/.objs/scoreboard/linux/x86_64/coverage/src/scoreboard/scoreboard.cpp.o
	@echo linking.coverage libscoreboard.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(scoreboard_AR) $(scoreboard_ARFLAGS) build/linux/x86_64/coverage/libscoreboard.a build/.objs/scoreboard/linux/x86_64/coverage/src/scoreboard/scoreboard.cpp.o

build/.objs/scoreboard/linux/x86_64/coverage/src/scoreboard/scoreboard.cpp.o: src/scoreboard/scoreboard.cpp
	@echo ccache compiling.coverage src/scoreboard/scoreboard.cpp
	@mkdir -p build/.objs/scoreboard/linux/x86_64/coverage/src/scoreboard
	$(VV)$(scoreboard_CXX) -c $(scoreboard_CXXFLAGS) -o build/.objs/scoreboard/linux/x86_64/coverage/src/scoreboard/scoreboard.cpp.o src/scoreboard/scoreboard.cpp

instruction_generator: build/linux/x86_64/coverage/libinstruction_generator.a
build/linux/x86_64/coverage/libinstruction_generator.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libcmd_encode.a build/linux/x86_64/coverage/libsystemc_logger.a build/.objs/instruction_generator/linux/x86_64/coverage/src/cmd_decoder/instruction_generator.cpp.o
	@echo linking.coverage libinstruction_generator.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(instruction_generator_AR) $(instruction_generator_ARFLAGS) build/linux/x86_64/coverage/libinstruction_generator.a build/.objs/instruction_generator/linux/x86_64/coverage/src/cmd_decoder/instruction_generator.cpp.o

build/.objs/instruction_generator/linux/x86_64/coverage/src/cmd_decoder/instruction_generator.cpp.o: src/cmd_decoder/instruction_generator.cpp
	@echo ccache compiling.coverage src/cmd_decoder/instruction_generator.cpp
	@mkdir -p build/.objs/instruction_generator/linux/x86_64/coverage/src/cmd_decoder
	$(VV)$(instruction_generator_CXX) -c $(instruction_generator_CXXFLAGS) -o build/.objs/instruction_generator/linux/x86_64/coverage/src/cmd_decoder/instruction_generator.cpp.o src/cmd_decoder/instruction_generator.cpp

dcim_engine_cpp: build/linux/x86_64/coverage/libdcim_engine_cpp.a
build/linux/x86_64/coverage/libdcim_engine_cpp.a: build/linux/x86_64/coverage/libdcim_macro_cpp.a build/.objs/dcim_engine_cpp/linux/x86_64/coverage/src/cim_cluster/src/dcim_engine.cpp.o
	@echo linking.coverage libdcim_engine_cpp.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(dcim_engine_cpp_AR) $(dcim_engine_cpp_ARFLAGS) build/linux/x86_64/coverage/libdcim_engine_cpp.a build/.objs/dcim_engine_cpp/linux/x86_64/coverage/src/cim_cluster/src/dcim_engine.cpp.o

build/.objs/dcim_engine_cpp/linux/x86_64/coverage/src/cim_cluster/src/dcim_engine.cpp.o: src/cim_cluster/src/dcim_engine.cpp
	@echo ccache compiling.coverage src/cim_cluster/src/dcim_engine.cpp
	@mkdir -p build/.objs/dcim_engine_cpp/linux/x86_64/coverage/src/cim_cluster/src
	$(VV)$(dcim_engine_cpp_CXX) -c $(dcim_engine_cpp_CXXFLAGS) -o build/.objs/dcim_engine_cpp/linux/x86_64/coverage/src/cim_cluster/src/dcim_engine.cpp.o src/cim_cluster/src/dcim_engine.cpp

test_scoreboard: build/linux/x86_64/coverage/test_scoreboard
build/linux/x86_64/coverage/test_scoreboard: build/linux/x86_64/coverage/libscoreboard.a build/.objs/test_scoreboard/linux/x86_64/coverage/src/scoreboard/test_sbd.cpp.o
	@echo linking.coverage test_scoreboard
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_scoreboard_LD) -o build/linux/x86_64/coverage/test_scoreboard build/.objs/test_scoreboard/linux/x86_64/coverage/src/scoreboard/test_sbd.cpp.o $(test_scoreboard_LDFLAGS)

build/.objs/test_scoreboard/linux/x86_64/coverage/src/scoreboard/test_sbd.cpp.o: src/scoreboard/test_sbd.cpp
	@echo ccache compiling.coverage src/scoreboard/test_sbd.cpp
	@mkdir -p build/.objs/test_scoreboard/linux/x86_64/coverage/src/scoreboard
	$(VV)$(test_scoreboard_CXX) -c $(test_scoreboard_CXXFLAGS) -o build/.objs/test_scoreboard/linux/x86_64/coverage/src/scoreboard/test_sbd.cpp.o src/scoreboard/test_sbd.cpp

test_dcim_engine: build/linux/x86_64/coverage/test_dcim_engine
build/linux/x86_64/coverage/test_dcim_engine: build/linux/x86_64/coverage/libdcim_engine_cpp.a build/.objs/test_dcim_engine/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_engine.cpp.o
	@echo linking.coverage test_dcim_engine
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_dcim_engine_LD) -o build/linux/x86_64/coverage/test_dcim_engine build/.objs/test_dcim_engine/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_engine.cpp.o $(test_dcim_engine_LDFLAGS)

build/.objs/test_dcim_engine/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_engine.cpp.o: src/cim_cluster/test/test_dcim_engine.cpp
	@echo ccache compiling.coverage src/cim_cluster/test/test_dcim_engine.cpp
	@mkdir -p build/.objs/test_dcim_engine/linux/x86_64/coverage/src/cim_cluster/test
	$(VV)$(test_dcim_engine_CXX) -c $(test_dcim_engine_CXXFLAGS) -o build/.objs/test_dcim_engine/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_engine.cpp.o src/cim_cluster/test/test_dcim_engine.cpp

test_mpu: build/linux/x86_64/coverage/test_mpu
build/linux/x86_64/coverage/test_mpu: build/linux/x86_64/coverage/libfeat_mpu.a build/.objs/test_mpu/linux/x86_64/coverage/src/feat_mpu/test/test_mpu.cpp.o
	@echo linking.coverage test_mpu
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_mpu_LD) -o build/linux/x86_64/coverage/test_mpu build/.objs/test_mpu/linux/x86_64/coverage/src/feat_mpu/test/test_mpu.cpp.o $(test_mpu_LDFLAGS)

build/.objs/test_mpu/linux/x86_64/coverage/src/feat_mpu/test/test_mpu.cpp.o: src/feat_mpu/test/test_mpu.cpp
	@echo ccache compiling.coverage src/feat_mpu/test/test_mpu.cpp
	@mkdir -p build/.objs/test_mpu/linux/x86_64/coverage/src/feat_mpu/test
	$(VV)$(test_mpu_CXX) -c $(test_mpu_CXXFLAGS) -o build/.objs/test_mpu/linux/x86_64/coverage/src/feat_mpu/test/test_mpu.cpp.o src/feat_mpu/test/test_mpu.cpp

cmd_decoder_op: build/linux/x86_64/coverage/libcmd_decoder_op.a
build/linux/x86_64/coverage/libcmd_decoder_op.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libsystemc_logger.a build/.objs/cmd_decoder_op/linux/x86_64/coverage/src/cmd_decoder/cmd_decode.cpp.o
	@echo linking.coverage libcmd_decoder_op.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(cmd_decoder_op_AR) $(cmd_decoder_op_ARFLAGS) build/linux/x86_64/coverage/libcmd_decoder_op.a build/.objs/cmd_decoder_op/linux/x86_64/coverage/src/cmd_decoder/cmd_decode.cpp.o

build/.objs/cmd_decoder_op/linux/x86_64/coverage/src/cmd_decoder/cmd_decode.cpp.o: src/cmd_decoder/cmd_decode.cpp
	@echo ccache compiling.coverage src/cmd_decoder/cmd_decode.cpp
	@mkdir -p build/.objs/cmd_decoder_op/linux/x86_64/coverage/src/cmd_decoder
	$(VV)$(cmd_decoder_op_CXX) -c $(cmd_decoder_op_CXXFLAGS) -o build/.objs/cmd_decoder_op/linux/x86_64/coverage/src/cmd_decoder/cmd_decode.cpp.o src/cmd_decoder/cmd_decode.cpp

test_vpu: build/linux/x86_64/coverage/test_vpu
build/linux/x86_64/coverage/test_vpu: build/linux/x86_64/coverage/libvpu.a build/linux/x86_64/coverage/liblocal_mem.a build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test.cpp.o build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test_generator.cpp.o build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test_executor.cpp.o build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/vpe/test/test_case_generator.cpp.o
	@echo linking.coverage test_vpu
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_vpu_LD) -o build/linux/x86_64/coverage/test_vpu build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test.cpp.o build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test_generator.cpp.o build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test_executor.cpp.o build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/vpe/test/test_case_generator.cpp.o $(test_vpu_LDFLAGS)

build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test.cpp.o: src/vpu/test/vpu_test.cpp
	@echo ccache compiling.coverage src/vpu/test/vpu_test.cpp
	@mkdir -p build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test
	$(VV)$(test_vpu_CXX) -c $(test_vpu_CXXFLAGS) -o build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test.cpp.o src/vpu/test/vpu_test.cpp

build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test_generator.cpp.o: src/vpu/test/vpu_test_generator.cpp
	@echo ccache compiling.coverage src/vpu/test/vpu_test_generator.cpp
	@mkdir -p build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test
	$(VV)$(test_vpu_CXX) -c $(test_vpu_CXXFLAGS) -o build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test_generator.cpp.o src/vpu/test/vpu_test_generator.cpp

build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test_executor.cpp.o: src/vpu/test/vpu_test_executor.cpp
	@echo ccache compiling.coverage src/vpu/test/vpu_test_executor.cpp
	@mkdir -p build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test
	$(VV)$(test_vpu_CXX) -c $(test_vpu_CXXFLAGS) -o build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test_executor.cpp.o src/vpu/test/vpu_test_executor.cpp

build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/vpe/test/test_case_generator.cpp.o: src/vpu/vpe/test/test_case_generator.cpp
	@echo ccache compiling.coverage src/vpu/vpe/test/test_case_generator.cpp
	@mkdir -p build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/vpe/test
	$(VV)$(test_vpu_CXX) -c $(test_vpu_CXXFLAGS) -o build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/vpe/test/test_case_generator.cpp.o src/vpu/vpe/test/test_case_generator.cpp

test_cmd_scheduler: build/linux/x86_64/coverage/libtest_cmd_scheduler.a
build/linux/x86_64/coverage/libtest_cmd_scheduler.a: build/linux/x86_64/coverage/libcmd_scheduler.a build/.objs/test_cmd_scheduler/linux/x86_64/coverage/src/cmd_scheduler/test/test_cmd_scheduler.cpp.o
	@echo linking.coverage libtest_cmd_scheduler.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_cmd_scheduler_AR) $(test_cmd_scheduler_ARFLAGS) build/linux/x86_64/coverage/libtest_cmd_scheduler.a build/.objs/test_cmd_scheduler/linux/x86_64/coverage/src/cmd_scheduler/test/test_cmd_scheduler.cpp.o

build/.objs/test_cmd_scheduler/linux/x86_64/coverage/src/cmd_scheduler/test/test_cmd_scheduler.cpp.o: src/cmd_scheduler/test/test_cmd_scheduler.cpp
	@echo ccache compiling.coverage src/cmd_scheduler/test/test_cmd_scheduler.cpp
	@mkdir -p build/.objs/test_cmd_scheduler/linux/x86_64/coverage/src/cmd_scheduler/test
	$(VV)$(test_cmd_scheduler_CXX) -c $(test_cmd_scheduler_CXXFLAGS) -o build/.objs/test_cmd_scheduler/linux/x86_64/coverage/src/cmd_scheduler/test/test_cmd_scheduler.cpp.o src/cmd_scheduler/test/test_cmd_scheduler.cpp

cmd_scheduler: build/linux/x86_64/coverage/libcmd_scheduler.a
build/linux/x86_64/coverage/libcmd_scheduler.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libcmd_dispatch.a build/linux/x86_64/coverage/librsp_rob.a build/linux/x86_64/coverage/libscoreboard.a build/linux/x86_64/coverage/libsystemc_logger.a build/.objs/cmd_scheduler/linux/x86_64/coverage/src/cmd_scheduler/cmd_scheduler.cpp.o
	@echo linking.coverage libcmd_scheduler.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(cmd_scheduler_AR) $(cmd_scheduler_ARFLAGS) build/linux/x86_64/coverage/libcmd_scheduler.a build/.objs/cmd_scheduler/linux/x86_64/coverage/src/cmd_scheduler/cmd_scheduler.cpp.o

build/.objs/cmd_scheduler/linux/x86_64/coverage/src/cmd_scheduler/cmd_scheduler.cpp.o: src/cmd_scheduler/cmd_scheduler.cpp
	@echo ccache compiling.coverage src/cmd_scheduler/cmd_scheduler.cpp
	@mkdir -p build/.objs/cmd_scheduler/linux/x86_64/coverage/src/cmd_scheduler
	$(VV)$(cmd_scheduler_CXX) -c $(cmd_scheduler_CXXFLAGS) -o build/.objs/cmd_scheduler/linux/x86_64/coverage/src/cmd_scheduler/cmd_scheduler.cpp.o src/cmd_scheduler/cmd_scheduler.cpp

tlu_desc_gen: build/linux/x86_64/coverage/libtlu_desc_gen.a
build/linux/x86_64/coverage/libtlu_desc_gen.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libtlu_cfg_reg.a build/.objs/tlu_desc_gen/linux/x86_64/coverage/src/tlu/tlu_desc_gen/tlu_desc_gen.cpp.o
	@echo linking.coverage libtlu_desc_gen.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(tlu_desc_gen_AR) $(tlu_desc_gen_ARFLAGS) build/linux/x86_64/coverage/libtlu_desc_gen.a build/.objs/tlu_desc_gen/linux/x86_64/coverage/src/tlu/tlu_desc_gen/tlu_desc_gen.cpp.o

build/.objs/tlu_desc_gen/linux/x86_64/coverage/src/tlu/tlu_desc_gen/tlu_desc_gen.cpp.o: src/tlu/tlu_desc_gen/tlu_desc_gen.cpp
	@echo ccache compiling.coverage src/tlu/tlu_desc_gen/tlu_desc_gen.cpp
	@mkdir -p build/.objs/tlu_desc_gen/linux/x86_64/coverage/src/tlu/tlu_desc_gen
	$(VV)$(tlu_desc_gen_CXX) -c $(tlu_desc_gen_CXXFLAGS) -o build/.objs/tlu_desc_gen/linux/x86_64/coverage/src/tlu/tlu_desc_gen/tlu_desc_gen.cpp.o src/tlu/tlu_desc_gen/tlu_desc_gen.cpp

cmd_decoder: build/linux/x86_64/coverage/libcmd_decoder.a
build/linux/x86_64/coverage/libcmd_decoder.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libsystemc_logger.a build/.objs/cmd_decoder/linux/x86_64/coverage/src/cmd_scheduler/cmd_decoder.cpp.o
	@echo linking.coverage libcmd_decoder.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(cmd_decoder_AR) $(cmd_decoder_ARFLAGS) build/linux/x86_64/coverage/libcmd_decoder.a build/.objs/cmd_decoder/linux/x86_64/coverage/src/cmd_scheduler/cmd_decoder.cpp.o

build/.objs/cmd_decoder/linux/x86_64/coverage/src/cmd_scheduler/cmd_decoder.cpp.o: src/cmd_scheduler/cmd_decoder.cpp
	@echo ccache compiling.coverage src/cmd_scheduler/cmd_decoder.cpp
	@mkdir -p build/.objs/cmd_decoder/linux/x86_64/coverage/src/cmd_scheduler
	$(VV)$(cmd_decoder_CXX) -c $(cmd_decoder_CXXFLAGS) -o build/.objs/cmd_decoder/linux/x86_64/coverage/src/cmd_scheduler/cmd_decoder.cpp.o src/cmd_scheduler/cmd_decoder.cpp

dcim_macro: build/linux/x86_64/coverage/libdcim_macro.a
build/linux/x86_64/coverage/libdcim_macro.a: build/linux/x86_64/coverage/libcommon.a build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_extract.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_compute.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_macro.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/data_align.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_storage.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_matrix_utils.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/fmt754_conv.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/bit_acc.c.o
	@echo linking.coverage libdcim_macro.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(dcim_macro_AR) $(dcim_macro_ARFLAGS) build/linux/x86_64/coverage/libdcim_macro.a build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_extract.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_compute.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_macro.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/data_align.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_storage.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_matrix_utils.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/fmt754_conv.c.o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/bit_acc.c.o

build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_extract.c.o: src/cim_cluster/src/dcim_extract.c
	@echo ccache compiling.coverage src/cim_cluster/src/dcim_extract.c
	@mkdir -p build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src
	$(VV)$(dcim_macro_CC) -c $(dcim_macro_CCFLAGS) -o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_extract.c.o src/cim_cluster/src/dcim_extract.c

build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_compute.c.o: src/cim_cluster/src/dcim_compute.c
	@echo ccache compiling.coverage src/cim_cluster/src/dcim_compute.c
	@mkdir -p build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src
	$(VV)$(dcim_macro_CC) -c $(dcim_macro_CCFLAGS) -o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_compute.c.o src/cim_cluster/src/dcim_compute.c

build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_macro.c.o: src/cim_cluster/src/dcim_macro.c
	@echo ccache compiling.coverage src/cim_cluster/src/dcim_macro.c
	@mkdir -p build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src
	$(VV)$(dcim_macro_CC) -c $(dcim_macro_CCFLAGS) -o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_macro.c.o src/cim_cluster/src/dcim_macro.c

build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/data_align.c.o: src/cim_cluster/src/data_align.c
	@echo ccache compiling.coverage src/cim_cluster/src/data_align.c
	@mkdir -p build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src
	$(VV)$(dcim_macro_CC) -c $(dcim_macro_CCFLAGS) -o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/data_align.c.o src/cim_cluster/src/data_align.c

build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_storage.c.o: src/cim_cluster/src/dcim_storage.c
	@echo ccache compiling.coverage src/cim_cluster/src/dcim_storage.c
	@mkdir -p build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src
	$(VV)$(dcim_macro_CC) -c $(dcim_macro_CCFLAGS) -o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_storage.c.o src/cim_cluster/src/dcim_storage.c

build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_matrix_utils.c.o: src/cim_cluster/src/dcim_matrix_utils.c
	@echo ccache compiling.coverage src/cim_cluster/src/dcim_matrix_utils.c
	@mkdir -p build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src
	$(VV)$(dcim_macro_CC) -c $(dcim_macro_CCFLAGS) -o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_matrix_utils.c.o src/cim_cluster/src/dcim_matrix_utils.c

build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/fmt754_conv.c.o: src/cim_cluster/src/fmt754_conv.c
	@echo ccache compiling.coverage src/cim_cluster/src/fmt754_conv.c
	@mkdir -p build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src
	$(VV)$(dcim_macro_CC) -c $(dcim_macro_CCFLAGS) -o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/fmt754_conv.c.o src/cim_cluster/src/fmt754_conv.c

build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/bit_acc.c.o: src/cim_cluster/src/bit_acc.c
	@echo ccache compiling.coverage src/cim_cluster/src/bit_acc.c
	@mkdir -p build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src
	$(VV)$(dcim_macro_CC) -c $(dcim_macro_CCFLAGS) -o build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/bit_acc.c.o src/cim_cluster/src/bit_acc.c

ac_types: build/linux/x86_64/coverage/libac_types.a
build/linux/x86_64/coverage/libac_types.a:
	@echo linking.coverage libac_types.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(ac_types_AR) $(ac_types_ARFLAGS) build/linux/x86_64/coverage/libac_types.a

test_tsu: build/linux/x86_64/coverage/test_tsu
build/linux/x86_64/coverage/test_tsu: build/linux/x86_64/coverage/libtsu_desc_gen.a build/linux/x86_64/coverage/libtsu_memory.a build/.objs/test_tsu/linux/x86_64/coverage/src/tsu/test/test_tsu.cpp.o
	@echo linking.coverage test_tsu
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_tsu_LD) -o build/linux/x86_64/coverage/test_tsu build/.objs/test_tsu/linux/x86_64/coverage/src/tsu/test/test_tsu.cpp.o $(test_tsu_LDFLAGS)

build/.objs/test_tsu/linux/x86_64/coverage/src/tsu/test/test_tsu.cpp.o: src/tsu/test/test_tsu.cpp
	@echo ccache compiling.coverage src/tsu/test/test_tsu.cpp
	@mkdir -p build/.objs/test_tsu/linux/x86_64/coverage/src/tsu/test
	$(VV)$(test_tsu_CXX) -c $(test_tsu_CXXFLAGS) -o build/.objs/test_tsu/linux/x86_64/coverage/src/tsu/test/test_tsu.cpp.o src/tsu/test/test_tsu.cpp

test_lmem: build/linux/x86_64/coverage/test_lmem
build/linux/x86_64/coverage/test_lmem: build/linux/x86_64/coverage/liblocal_mem.a build/.objs/test_lmem/linux/x86_64/coverage/src/local_mem/test/test_lmem.cpp.o
	@echo linking.coverage test_lmem
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_lmem_LD) -o build/linux/x86_64/coverage/test_lmem build/.objs/test_lmem/linux/x86_64/coverage/src/local_mem/test/test_lmem.cpp.o $(test_lmem_LDFLAGS)

build/.objs/test_lmem/linux/x86_64/coverage/src/local_mem/test/test_lmem.cpp.o: src/local_mem/test/test_lmem.cpp
	@echo ccache compiling.coverage src/local_mem/test/test_lmem.cpp
	@mkdir -p build/.objs/test_lmem/linux/x86_64/coverage/src/local_mem/test
	$(VV)$(test_lmem_CXX) -c $(test_lmem_CXXFLAGS) -o build/.objs/test_lmem/linux/x86_64/coverage/src/local_mem/test/test_lmem.cpp.o src/local_mem/test/test_lmem.cpp

tsu_cfg_reg: build/linux/x86_64/coverage/libtsu_cfg_reg.a
build/linux/x86_64/coverage/libtsu_cfg_reg.a: build/linux/x86_64/coverage/libcommon.a
	@echo linking.coverage libtsu_cfg_reg.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(tsu_cfg_reg_AR) $(tsu_cfg_reg_ARFLAGS) build/linux/x86_64/coverage/libtsu_cfg_reg.a

test_rsp_rob: build/linux/x86_64/coverage/test_rsp_rob
build/linux/x86_64/coverage/test_rsp_rob: build/linux/x86_64/coverage/librsp_rob.a build/.objs/test_rsp_rob/linux/x86_64/coverage/src/cmd_scheduler/test/test_rsp_rob.cpp.o
	@echo linking.coverage test_rsp_rob
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_rsp_rob_LD) -o build/linux/x86_64/coverage/test_rsp_rob build/.objs/test_rsp_rob/linux/x86_64/coverage/src/cmd_scheduler/test/test_rsp_rob.cpp.o $(test_rsp_rob_LDFLAGS)

build/.objs/test_rsp_rob/linux/x86_64/coverage/src/cmd_scheduler/test/test_rsp_rob.cpp.o: src/cmd_scheduler/test/test_rsp_rob.cpp
	@echo ccache compiling.coverage src/cmd_scheduler/test/test_rsp_rob.cpp
	@mkdir -p build/.objs/test_rsp_rob/linux/x86_64/coverage/src/cmd_scheduler/test
	$(VV)$(test_rsp_rob_CXX) -c $(test_rsp_rob_CXXFLAGS) -o build/.objs/test_rsp_rob/linux/x86_64/coverage/src/cmd_scheduler/test/test_rsp_rob.cpp.o src/cmd_scheduler/test/test_rsp_rob.cpp

dev_tsu_tlu: build/linux/x86_64/coverage/libdev_tsu_tlu.a
build/linux/x86_64/coverage/libdev_tsu_tlu.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libutils.a build/linux/x86_64/coverage/liblocal_mem.a build/linux/x86_64/coverage/libsystemc_logger.a build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/global_memory.cpp.o build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/tsu.cpp.o build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/tlu.cpp.o
	@echo linking.coverage libdev_tsu_tlu.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(dev_tsu_tlu_AR) $(dev_tsu_tlu_ARFLAGS) build/linux/x86_64/coverage/libdev_tsu_tlu.a build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/global_memory.cpp.o build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/tsu.cpp.o build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/tlu.cpp.o

build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/global_memory.cpp.o: src/dev_tsu_tlu/global_memory.cpp
	@echo ccache compiling.coverage src/dev_tsu_tlu/global_memory.cpp
	@mkdir -p build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu
	$(VV)$(dev_tsu_tlu_CXX) -c $(dev_tsu_tlu_CXXFLAGS) -o build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/global_memory.cpp.o src/dev_tsu_tlu/global_memory.cpp

build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/tsu.cpp.o: src/dev_tsu_tlu/tsu.cpp
	@echo ccache compiling.coverage src/dev_tsu_tlu/tsu.cpp
	@mkdir -p build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu
	$(VV)$(dev_tsu_tlu_CXX) -c $(dev_tsu_tlu_CXXFLAGS) -o build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/tsu.cpp.o src/dev_tsu_tlu/tsu.cpp

build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/tlu.cpp.o: src/dev_tsu_tlu/tlu.cpp
	@echo ccache compiling.coverage src/dev_tsu_tlu/tlu.cpp
	@mkdir -p build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu
	$(VV)$(dev_tsu_tlu_CXX) -c $(dev_tsu_tlu_CXXFLAGS) -o build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/tlu.cpp.o src/dev_tsu_tlu/tlu.cpp

test_dev_tsu_tlu: build/linux/x86_64/coverage/test_dev_tsu_tlu
build/linux/x86_64/coverage/test_dev_tsu_tlu: build/linux/x86_64/coverage/libdev_tsu_tlu.a build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_auto_test_driver.cpp.o build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_test_generator.cpp.o build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_test_bench.cpp.o
	@echo linking.coverage test_dev_tsu_tlu
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_dev_tsu_tlu_LD) -o build/linux/x86_64/coverage/test_dev_tsu_tlu build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_auto_test_driver.cpp.o build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_test_generator.cpp.o build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_test_bench.cpp.o $(test_dev_tsu_tlu_LDFLAGS)

build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_auto_test_driver.cpp.o: src/dev_tsu_tlu/test/tsu_tlu_auto_test_driver.cpp
	@echo ccache compiling.coverage src/dev_tsu_tlu/test/tsu_tlu_auto_test_driver.cpp
	@mkdir -p build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test
	$(VV)$(test_dev_tsu_tlu_CXX) -c $(test_dev_tsu_tlu_CXXFLAGS) -o build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_auto_test_driver.cpp.o src/dev_tsu_tlu/test/tsu_tlu_auto_test_driver.cpp

build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_test_generator.cpp.o: src/dev_tsu_tlu/test/tsu_tlu_test_generator.cpp
	@echo ccache compiling.coverage src/dev_tsu_tlu/test/tsu_tlu_test_generator.cpp
	@mkdir -p build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test
	$(VV)$(test_dev_tsu_tlu_CXX) -c $(test_dev_tsu_tlu_CXXFLAGS) -o build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_test_generator.cpp.o src/dev_tsu_tlu/test/tsu_tlu_test_generator.cpp

build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_test_bench.cpp.o: src/dev_tsu_tlu/test/tsu_tlu_test_bench.cpp
	@echo ccache compiling.coverage src/dev_tsu_tlu/test/tsu_tlu_test_bench.cpp
	@mkdir -p build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test
	$(VV)$(test_dev_tsu_tlu_CXX) -c $(test_dev_tsu_tlu_CXXFLAGS) -o build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_test_bench.cpp.o src/dev_tsu_tlu/test/tsu_tlu_test_bench.cpp

vpe: build/linux/x86_64/coverage/libvpe.a
build/linux/x86_64/coverage/libvpe.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libdcim_macro.a build/linux/x86_64/coverage/libsystemc_logger.a build/linux/x86_64/coverage/libutils.a build/.objs/vpe/linux/x86_64/coverage/src/vpu/vpe/vpe.cpp.o build/.objs/vpe/linux/x86_64/coverage/src/vpu/vpe/vpe_arithmetic.cpp.o
	@echo linking.coverage libvpe.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(vpe_AR) $(vpe_ARFLAGS) build/linux/x86_64/coverage/libvpe.a build/.objs/vpe/linux/x86_64/coverage/src/vpu/vpe/vpe.cpp.o build/.objs/vpe/linux/x86_64/coverage/src/vpu/vpe/vpe_arithmetic.cpp.o

build/.objs/vpe/linux/x86_64/coverage/src/vpu/vpe/vpe.cpp.o: src/vpu/vpe/vpe.cpp
	@echo ccache compiling.coverage src/vpu/vpe/vpe.cpp
	@mkdir -p build/.objs/vpe/linux/x86_64/coverage/src/vpu/vpe
	$(VV)$(vpe_CXX) -c $(vpe_CXXFLAGS) -o build/.objs/vpe/linux/x86_64/coverage/src/vpu/vpe/vpe.cpp.o src/vpu/vpe/vpe.cpp

build/.objs/vpe/linux/x86_64/coverage/src/vpu/vpe/vpe_arithmetic.cpp.o: src/vpu/vpe/vpe_arithmetic.cpp
	@echo ccache compiling.coverage src/vpu/vpe/vpe_arithmetic.cpp
	@mkdir -p build/.objs/vpe/linux/x86_64/coverage/src/vpu/vpe
	$(VV)$(vpe_CXX) -c $(vpe_CXXFLAGS) -o build/.objs/vpe/linux/x86_64/coverage/src/vpu/vpe/vpe_arithmetic.cpp.o src/vpu/vpe/vpe_arithmetic.cpp

utils: build/linux/x86_64/coverage/libutils.a
build/linux/x86_64/coverage/libutils.a: build/linux/x86_64/coverage/libac_types.a
	@echo linking.coverage libutils.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(utils_AR) $(utils_ARFLAGS) build/linux/x86_64/coverage/libutils.a

test_storage_compute: build/linux/x86_64/coverage/test_storage_compute
build/linux/x86_64/coverage/test_storage_compute: build/linux/x86_64/coverage/libdcim_macro.a build/.objs/test_storage_compute/linux/x86_64/coverage/src/cim_cluster/test/test_storage_compute.cpp.o
	@echo linking.coverage test_storage_compute
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(test_storage_compute_LD) -o build/linux/x86_64/coverage/test_storage_compute build/.objs/test_storage_compute/linux/x86_64/coverage/src/cim_cluster/test/test_storage_compute.cpp.o $(test_storage_compute_LDFLAGS)

build/.objs/test_storage_compute/linux/x86_64/coverage/src/cim_cluster/test/test_storage_compute.cpp.o: src/cim_cluster/test/test_storage_compute.cpp
	@echo ccache compiling.coverage src/cim_cluster/test/test_storage_compute.cpp
	@mkdir -p build/.objs/test_storage_compute/linux/x86_64/coverage/src/cim_cluster/test
	$(VV)$(test_storage_compute_CXX) -c $(test_storage_compute_CXXFLAGS) -o build/.objs/test_storage_compute/linux/x86_64/coverage/src/cim_cluster/test/test_storage_compute.cpp.o src/cim_cluster/test/test_storage_compute.cpp

local_mem: build/linux/x86_64/coverage/liblocal_mem.a
build/linux/x86_64/coverage/liblocal_mem.a: build/linux/x86_64/coverage/libcommon.a build/linux/x86_64/coverage/libdcim_cluster.a build/.objs/local_mem/linux/x86_64/coverage/src/local_mem/local_mem.cpp.o
	@echo linking.coverage liblocal_mem.a
	@mkdir -p build/linux/x86_64/coverage
	$(VV)$(local_mem_AR) $(local_mem_ARFLAGS) build/linux/x86_64/coverage/liblocal_mem.a build/.objs/local_mem/linux/x86_64/coverage/src/local_mem/local_mem.cpp.o

build/.objs/local_mem/linux/x86_64/coverage/src/local_mem/local_mem.cpp.o: src/local_mem/local_mem.cpp
	@echo ccache compiling.coverage src/local_mem/local_mem.cpp
	@mkdir -p build/.objs/local_mem/linux/x86_64/coverage/src/local_mem
	$(VV)$(local_mem_CXX) -c $(local_mem_CXXFLAGS) -o build/.objs/local_mem/linux/x86_64/coverage/src/local_mem/local_mem.cpp.o src/local_mem/local_mem.cpp

clean:  clean_test_npu_datatypes clean_test_float clean_systemc_logger clean_test_dcim_array clean_rsp_rob clean_cmd_dispatch clean_vpe_test clean_feat_mpu clean_test_cmd_dispatch clean_common clean_feat_tmu clean_test_tlu clean_top clean_cmd_test_op clean_dcim_cluster clean_tsu_memory clean_test_func clean_tlu_cfg_reg clean_test_npu_macunit clean_npu_datagen clean_tlu_memory clean_cmd_encode clean_tsu_desc_gen clean_vpu clean_test_reg clean_dcim_macro_cpp clean_test_dcim_cluster clean_scoreboard clean_instruction_generator clean_dcim_engine_cpp clean_test_scoreboard clean_test_dcim_engine clean_test_mpu clean_cmd_decoder_op clean_test_vpu clean_test_cmd_scheduler clean_cmd_scheduler clean_tlu_desc_gen clean_cmd_decoder clean_dcim_macro clean_ac_types clean_test_tsu clean_test_lmem clean_tsu_cfg_reg clean_test_rsp_rob clean_dev_tsu_tlu clean_test_dev_tsu_tlu clean_vpe clean_utils clean_test_storage_compute clean_local_mem

clean_test_npu_datatypes:  clean_ac_types
	@rm -rf build/linux/x86_64/coverage/test_npu_datatypes
	@rm -rf build/linux/x86_64/coverage/test_npu_datatypes.sym
	@rm -rf build/.objs/test_npu_datatypes/linux/x86_64/coverage/ac_types-master/test/npu_datatype_test.cpp.o

clean_test_float:  clean_ac_types clean_dcim_macro
	@rm -rf build/linux/x86_64/coverage/test_float
	@rm -rf build/linux/x86_64/coverage/test_float.sym
	@rm -rf build/.objs/test_float/linux/x86_64/coverage/src/cim_cluster/test/test_float.cpp.o

clean_systemc_logger:  clean_common
	@rm -rf build/linux/x86_64/coverage/libsystemc_logger.a
	@rm -rf build/linux/x86_64/coverage/systemc_logger.sym
	@rm -rf build/.objs/systemc_logger/linux/x86_64/coverage/include/utils/systemc_logger.cpp.o

clean_test_dcim_array:  clean_dcim_macro_cpp
	@rm -rf build/linux/x86_64/coverage/test_dcim_array
	@rm -rf build/linux/x86_64/coverage/test_dcim_array.sym
	@rm -rf build/.objs/test_dcim_array/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_array.cpp.o

clean_rsp_rob:  clean_common clean_systemc_logger
	@rm -rf build/linux/x86_64/coverage/librsp_rob.a
	@rm -rf build/linux/x86_64/coverage/rsp_rob.sym
	@rm -rf build/.objs/rsp_rob/linux/x86_64/coverage/src/cmd_scheduler/rsp_rob.cpp.o

clean_cmd_dispatch:  clean_common clean_cmd_decoder clean_scoreboard clean_systemc_logger
	@rm -rf build/linux/x86_64/coverage/libcmd_dispatch.a
	@rm -rf build/linux/x86_64/coverage/cmd_dispatch.sym
	@rm -rf build/.objs/cmd_dispatch/linux/x86_64/coverage/src/cmd_scheduler/cmd_dispatch.cpp.o

clean_vpe_test:  clean_vpe clean_common clean_utils
	@rm -rf build/linux/x86_64/coverage/vpe_test
	@rm -rf build/linux/x86_64/coverage/vpe_test.sym
	@rm -rf build/.objs/vpe_test/linux/x86_64/coverage/src/vpu/vpe/test/vpe_test.cpp.o
	@rm -rf build/.objs/vpe_test/linux/x86_64/coverage/src/vpu/vpe/test/test_case_generator.cpp.o

clean_feat_mpu:  clean_dcim_cluster clean_local_mem clean_utils
	@rm -rf build/linux/x86_64/coverage/libfeat_mpu.a
	@rm -rf build/linux/x86_64/coverage/feat_mpu.sym
	@rm -rf build/.objs/feat_mpu/linux/x86_64/coverage/src/feat_mpu/feat_mpu.cpp.o

clean_test_cmd_dispatch:  clean_cmd_dispatch
	@rm -rf build/linux/x86_64/coverage/test_cmd_dispatch
	@rm -rf build/linux/x86_64/coverage/test_cmd_dispatch.sym
	@rm -rf build/.objs/test_cmd_dispatch/linux/x86_64/coverage/src/cmd_scheduler/test/test_cmd_dispatch.cpp.o

clean_common: 
	@rm -rf build/linux/x86_64/coverage/libcommon.a
	@rm -rf build/linux/x86_64/coverage/common.sym

clean_feat_tmu:  clean_common clean_local_mem
	@rm -rf build/linux/x86_64/coverage/libfeat_tmu.a
	@rm -rf build/linux/x86_64/coverage/feat_tmu.sym
	@rm -rf build/.objs/feat_tmu/linux/x86_64/coverage/src/feat_tmu/feat_tmu.cpp.o

clean_test_tlu:  clean_tlu_desc_gen clean_tlu_memory
	@rm -rf build/linux/x86_64/coverage/test_tlu
	@rm -rf build/linux/x86_64/coverage/test_tlu.sym
	@rm -rf build/.objs/test_tlu/linux/x86_64/coverage/src/tlu/test/test_tlu.cpp.o

clean_top:  clean_cmd_scheduler clean_test_cmd_scheduler
	@rm -rf build/linux/x86_64/coverage/top
	@rm -rf build/linux/x86_64/coverage/top.sym
	@rm -rf build/.objs/top/linux/x86_64/coverage/src/cmd_scheduler/test/top.cpp.o

clean_cmd_test_op:  clean_cmd_decoder_op clean_cmd_encode clean_instruction_generator
	@rm -rf build/linux/x86_64/coverage/cmd_test_op
	@rm -rf build/linux/x86_64/coverage/cmd_test_op.sym
	@rm -rf build/.objs/cmd_test_op/linux/x86_64/coverage/src/cmd_decoder/cmd_test.cpp.o

clean_dcim_cluster:  clean_dcim_engine_cpp
	@rm -rf build/linux/x86_64/coverage/libdcim_cluster.a
	@rm -rf build/linux/x86_64/coverage/dcim_cluster.sym
	@rm -rf build/.objs/dcim_cluster/linux/x86_64/coverage/src/cim_cluster/src/dcim_cluster.cpp.o

clean_tsu_memory:  clean_common
	@rm -rf build/linux/x86_64/coverage/libtsu_memory.a
	@rm -rf build/linux/x86_64/coverage/tsu_memory.sym
	@rm -rf build/.objs/tsu_memory/linux/x86_64/coverage/src/tsu/test_memory/mem.cpp.o

clean_test_func:  clean_vpu
	@rm -rf build/linux/x86_64/coverage/test_func
	@rm -rf build/linux/x86_64/coverage/test_func.sym
	@rm -rf build/.objs/test_func/linux/x86_64/coverage/src/vpu/test/test_data_padding.cpp.o

clean_tlu_cfg_reg:  clean_common
	@rm -rf build/linux/x86_64/coverage/libtlu_cfg_reg.a
	@rm -rf build/linux/x86_64/coverage/tlu_cfg_reg.sym

clean_test_npu_macunit:  clean_ac_types
	@rm -rf build/linux/x86_64/coverage/test_npu_macunit
	@rm -rf build/linux/x86_64/coverage/test_npu_macunit.sym
	@rm -rf build/.objs/test_npu_macunit/linux/x86_64/coverage/ac_types-master/test/test_npu_macunit.cpp.o
	@rm -rf build/.objs/test_npu_macunit/linux/x86_64/coverage/ac_types-master/npu_datagen.cpp.o

clean_npu_datagen:  clean_ac_types
	@rm -rf build/linux/x86_64/coverage/libnpu_datagen.a
	@rm -rf build/linux/x86_64/coverage/npu_datagen.sym
	@rm -rf build/.objs/npu_datagen/linux/x86_64/coverage/ac_types-master/npu_datagen.cpp.o

clean_tlu_memory:  clean_common
	@rm -rf build/linux/x86_64/coverage/libtlu_memory.a
	@rm -rf build/linux/x86_64/coverage/tlu_memory.sym
	@rm -rf build/.objs/tlu_memory/linux/x86_64/coverage/src/tlu/test_memory/mem.cpp.o

clean_cmd_encode:  clean_common clean_systemc_logger
	@rm -rf build/linux/x86_64/coverage/libcmd_encode.a
	@rm -rf build/linux/x86_64/coverage/cmd_encode.sym
	@rm -rf build/.objs/cmd_encode/linux/x86_64/coverage/src/cmd_decoder/cmd_encode.cpp.o

clean_tsu_desc_gen:  clean_common clean_tsu_cfg_reg
	@rm -rf build/linux/x86_64/coverage/libtsu_desc_gen.a
	@rm -rf build/linux/x86_64/coverage/tsu_desc_gen.sym
	@rm -rf build/.objs/tsu_desc_gen/linux/x86_64/coverage/src/tsu/tsu_desc_gen/tsu_desc_gen.cpp.o

clean_vpu:  clean_vpe
	@rm -rf build/linux/x86_64/coverage/libvpu.a
	@rm -rf build/linux/x86_64/coverage/vpu.sym
	@rm -rf build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu.cpp.o
	@rm -rf build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_rd_req_tx.cpp.o
	@rm -rf build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_rd_ack_rx.cpp.o
	@rm -rf build/.objs/vpu/linux/x86_64/coverage/src/vpu/vpu_wr_req_tx.cpp.o

clean_test_reg:  clean_tsu_cfg_reg
	@rm -rf build/linux/x86_64/coverage/test_reg
	@rm -rf build/linux/x86_64/coverage/test_reg.sym
	@rm -rf build/.objs/test_reg/linux/x86_64/coverage/src/tsu/tsu_cfg_reg/tsu_cfg_reg.cpp.o

clean_dcim_macro_cpp:  clean_dcim_macro
	@rm -rf build/linux/x86_64/coverage/libdcim_macro_cpp.a
	@rm -rf build/linux/x86_64/coverage/dcim_macro_cpp.sym
	@rm -rf build/.objs/dcim_macro_cpp/linux/x86_64/coverage/src/cim_cluster/src/dcim_array.cpp.o

clean_test_dcim_cluster:  clean_dcim_cluster
	@rm -rf build/linux/x86_64/coverage/test_dcim_cluster
	@rm -rf build/linux/x86_64/coverage/test_dcim_cluster.sym
	@rm -rf build/.objs/test_dcim_cluster/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_cluster.cpp.o

clean_scoreboard:  clean_common clean_systemc_logger
	@rm -rf build/linux/x86_64/coverage/libscoreboard.a
	@rm -rf build/linux/x86_64/coverage/scoreboard.sym
	@rm -rf build/.objs/scoreboard/linux/x86_64/coverage/src/scoreboard/scoreboard.cpp.o

clean_instruction_generator:  clean_common clean_cmd_encode clean_systemc_logger
	@rm -rf build/linux/x86_64/coverage/libinstruction_generator.a
	@rm -rf build/linux/x86_64/coverage/instruction_generator.sym
	@rm -rf build/.objs/instruction_generator/linux/x86_64/coverage/src/cmd_decoder/instruction_generator.cpp.o

clean_dcim_engine_cpp:  clean_dcim_macro_cpp
	@rm -rf build/linux/x86_64/coverage/libdcim_engine_cpp.a
	@rm -rf build/linux/x86_64/coverage/dcim_engine_cpp.sym
	@rm -rf build/.objs/dcim_engine_cpp/linux/x86_64/coverage/src/cim_cluster/src/dcim_engine.cpp.o

clean_test_scoreboard:  clean_scoreboard
	@rm -rf build/linux/x86_64/coverage/test_scoreboard
	@rm -rf build/linux/x86_64/coverage/test_scoreboard.sym
	@rm -rf build/.objs/test_scoreboard/linux/x86_64/coverage/src/scoreboard/test_sbd.cpp.o

clean_test_dcim_engine:  clean_dcim_engine_cpp
	@rm -rf build/linux/x86_64/coverage/test_dcim_engine
	@rm -rf build/linux/x86_64/coverage/test_dcim_engine.sym
	@rm -rf build/.objs/test_dcim_engine/linux/x86_64/coverage/src/cim_cluster/test/test_dcim_engine.cpp.o

clean_test_mpu:  clean_feat_mpu
	@rm -rf build/linux/x86_64/coverage/test_mpu
	@rm -rf build/linux/x86_64/coverage/test_mpu.sym
	@rm -rf build/.objs/test_mpu/linux/x86_64/coverage/src/feat_mpu/test/test_mpu.cpp.o

clean_cmd_decoder_op:  clean_common clean_systemc_logger
	@rm -rf build/linux/x86_64/coverage/libcmd_decoder_op.a
	@rm -rf build/linux/x86_64/coverage/cmd_decoder_op.sym
	@rm -rf build/.objs/cmd_decoder_op/linux/x86_64/coverage/src/cmd_decoder/cmd_decode.cpp.o

clean_test_vpu:  clean_vpu clean_local_mem
	@rm -rf build/linux/x86_64/coverage/test_vpu
	@rm -rf build/linux/x86_64/coverage/test_vpu.sym
	@rm -rf build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test.cpp.o
	@rm -rf build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test_generator.cpp.o
	@rm -rf build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/test/vpu_test_executor.cpp.o
	@rm -rf build/.objs/test_vpu/linux/x86_64/coverage/src/vpu/vpe/test/test_case_generator.cpp.o

clean_test_cmd_scheduler:  clean_cmd_scheduler
	@rm -rf build/linux/x86_64/coverage/libtest_cmd_scheduler.a
	@rm -rf build/linux/x86_64/coverage/test_cmd_scheduler.sym
	@rm -rf build/.objs/test_cmd_scheduler/linux/x86_64/coverage/src/cmd_scheduler/test/test_cmd_scheduler.cpp.o

clean_cmd_scheduler:  clean_common clean_cmd_dispatch clean_rsp_rob clean_scoreboard clean_systemc_logger
	@rm -rf build/linux/x86_64/coverage/libcmd_scheduler.a
	@rm -rf build/linux/x86_64/coverage/cmd_scheduler.sym
	@rm -rf build/.objs/cmd_scheduler/linux/x86_64/coverage/src/cmd_scheduler/cmd_scheduler.cpp.o

clean_tlu_desc_gen:  clean_common clean_tlu_cfg_reg
	@rm -rf build/linux/x86_64/coverage/libtlu_desc_gen.a
	@rm -rf build/linux/x86_64/coverage/tlu_desc_gen.sym
	@rm -rf build/.objs/tlu_desc_gen/linux/x86_64/coverage/src/tlu/tlu_desc_gen/tlu_desc_gen.cpp.o

clean_cmd_decoder:  clean_common clean_systemc_logger
	@rm -rf build/linux/x86_64/coverage/libcmd_decoder.a
	@rm -rf build/linux/x86_64/coverage/cmd_decoder.sym
	@rm -rf build/.objs/cmd_decoder/linux/x86_64/coverage/src/cmd_scheduler/cmd_decoder.cpp.o

clean_dcim_macro:  clean_common
	@rm -rf build/linux/x86_64/coverage/libdcim_macro.a
	@rm -rf build/linux/x86_64/coverage/dcim_macro.sym
	@rm -rf build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_extract.c.o
	@rm -rf build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_compute.c.o
	@rm -rf build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_macro.c.o
	@rm -rf build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/data_align.c.o
	@rm -rf build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_storage.c.o
	@rm -rf build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/dcim_matrix_utils.c.o
	@rm -rf build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/fmt754_conv.c.o
	@rm -rf build/.objs/dcim_macro/linux/x86_64/coverage/src/cim_cluster/src/bit_acc.c.o

clean_ac_types: 
	@rm -rf build/linux/x86_64/coverage/libac_types.a
	@rm -rf build/linux/x86_64/coverage/ac_types.sym

clean_test_tsu:  clean_tsu_desc_gen clean_tsu_memory
	@rm -rf build/linux/x86_64/coverage/test_tsu
	@rm -rf build/linux/x86_64/coverage/test_tsu.sym
	@rm -rf build/.objs/test_tsu/linux/x86_64/coverage/src/tsu/test/test_tsu.cpp.o

clean_test_lmem:  clean_local_mem
	@rm -rf build/linux/x86_64/coverage/test_lmem
	@rm -rf build/linux/x86_64/coverage/test_lmem.sym
	@rm -rf build/.objs/test_lmem/linux/x86_64/coverage/src/local_mem/test/test_lmem.cpp.o

clean_tsu_cfg_reg:  clean_common
	@rm -rf build/linux/x86_64/coverage/libtsu_cfg_reg.a
	@rm -rf build/linux/x86_64/coverage/tsu_cfg_reg.sym

clean_test_rsp_rob:  clean_rsp_rob
	@rm -rf build/linux/x86_64/coverage/test_rsp_rob
	@rm -rf build/linux/x86_64/coverage/test_rsp_rob.sym
	@rm -rf build/.objs/test_rsp_rob/linux/x86_64/coverage/src/cmd_scheduler/test/test_rsp_rob.cpp.o

clean_dev_tsu_tlu:  clean_common clean_utils clean_local_mem clean_systemc_logger
	@rm -rf build/linux/x86_64/coverage/libdev_tsu_tlu.a
	@rm -rf build/linux/x86_64/coverage/dev_tsu_tlu.sym
	@rm -rf build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/global_memory.cpp.o
	@rm -rf build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/tsu.cpp.o
	@rm -rf build/.objs/dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/tlu.cpp.o

clean_test_dev_tsu_tlu:  clean_dev_tsu_tlu
	@rm -rf build/linux/x86_64/coverage/test_dev_tsu_tlu
	@rm -rf build/linux/x86_64/coverage/test_dev_tsu_tlu.sym
	@rm -rf build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_auto_test_driver.cpp.o
	@rm -rf build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_test_generator.cpp.o
	@rm -rf build/.objs/test_dev_tsu_tlu/linux/x86_64/coverage/src/dev_tsu_tlu/test/tsu_tlu_test_bench.cpp.o

clean_vpe:  clean_common clean_dcim_macro clean_systemc_logger clean_utils
	@rm -rf build/linux/x86_64/coverage/libvpe.a
	@rm -rf build/linux/x86_64/coverage/vpe.sym
	@rm -rf build/.objs/vpe/linux/x86_64/coverage/src/vpu/vpe/vpe.cpp.o
	@rm -rf build/.objs/vpe/linux/x86_64/coverage/src/vpu/vpe/vpe_arithmetic.cpp.o

clean_utils:  clean_ac_types
	@rm -rf build/linux/x86_64/coverage/libutils.a
	@rm -rf build/linux/x86_64/coverage/utils.sym

clean_test_storage_compute:  clean_dcim_macro
	@rm -rf build/linux/x86_64/coverage/test_storage_compute
	@rm -rf build/linux/x86_64/coverage/test_storage_compute.sym
	@rm -rf build/.objs/test_storage_compute/linux/x86_64/coverage/src/cim_cluster/test/test_storage_compute.cpp.o

clean_local_mem:  clean_common clean_dcim_cluster
	@rm -rf build/linux/x86_64/coverage/liblocal_mem.a
	@rm -rf build/linux/x86_64/coverage/local_mem.sym
	@rm -rf build/.objs/local_mem/linux/x86_64/coverage/src/local_mem/local_mem.cpp.o

