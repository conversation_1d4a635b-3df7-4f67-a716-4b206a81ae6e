name: Code Quality Checks

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main ]

# Add permissions configuration
permissions:
  actions: read
  checks: write
  contents: read

jobs:
  code_quality:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # Use the composite action for xmake setup
      - name: Setup xmake environment
        uses: ./.github/actions/setup-xmake
        with:
          xmake-version: '2.9.8'
          mode: 'quality'

      - name: Install clang tools
        run: |
          sudo apt-get update
          sudo apt-get install -y clang-format-18 clang-tidy-18 cppcheck

      - name: Check code formatting
        run: |
          # 检查C++代码格式
          find . -name "*.cpp" -o -name "*.c" -o -name "*.h" -o -name "*.hpp" | xargs clang-format-18 -i
