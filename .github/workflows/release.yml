name: Release

on:
  push:
    tags:
      - 'v*.*.*'  # 匹配版本标签，如 v1.0.0
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version'
        required: true
        default: 'v0.1.0'

env:
  SYSTEMC_VERSION: "3.0.0"
  SYSTEMC_HOME: "/usr/local/systemc-3.0.0"

jobs:
  create_release:
    runs-on: ubuntu-latest
    
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
      version: ${{ steps.get_version.outputs.version }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Get version
        id: get_version
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
          else
            echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
          fi

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.get_version.outputs.version }}
          release_name: NPU SystemC ${{ steps.get_version.outputs.version }}
          body: |
            ## NPU SystemC Release ${{ steps.get_version.outputs.version }}
            
            ### Features
            - SystemC-based NPU simulation framework
            - Modular architecture with multiple processing units
            - Comprehensive test suite
            
            ### Modules Included
            - CIM Cluster (Compute-in-Memory)
            - Command Scheduler/Decoder
            - TSU/TLU (Tile Scheduling/Loading Units)
            - VPU (Vector Processing Unit)
            - Memory subsystems
            
            ### Build Requirements
            - SystemC 3.0.0+
            - xmake 2.9.8+
            - C++17 compiler
            
            See README.md for detailed build instructions.
          draft: false
          prerelease: false

  build_release:  # Simplified: only package source files
    needs: create_release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # Use the composite action for xmake setup
      - name: Setup xmake environment
        uses: ./.github/actions/setup-xmake
        with:
          xmake-version: '2.9.8'
          mode: 'release'

      # Use the composite action for SystemC setup
      - name: Setup SystemC
        uses: ./.github/actions/setup-systemc
        with:
          systemc-version: ${{ env.SYSTEMC_VERSION }}
          systemc-home: ${{ env.SYSTEMC_HOME }}

      - name: Package source files
        run: |
          tar czf npu-sc-${{ needs.create_release.outputs.version }}-source.tar.gz \
            --exclude='.github' \
            --exclude='build*' \
            --exclude='*.o' \
            --exclude='*.so' \
            --exclude='*.a' \
            --exclude='*.lo' \
            --exclude='*.la' \
            --exclude='*.dylib' \
            --exclude='*.dll' \
            --exclude='*.exe' \
            --exclude='*.obj' \
            --exclude='*.lib' \
            --exclude='*.exp' \
            --exclude='*.pdb' \
            --exclude='.git' \
            --exclude='.vscode' \
            --exclude='.idea' \
            --exclude='*.log' \
            --exclude='*.tmp' \
            --exclude='*.bak' \
            --exclude='*.swp' \
            --exclude='*.swo' \
            --exclude='*.pyc' \
            --exclude='__pycache__' \
            --exclude='*.gcda' \
            --exclude='*.gcno' \
            --exclude='*.gcov' \
            src include docs img ac_types-master xmake.lua LICENSE README.md insert-variables.sh run_coverage.sh

      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create_release.outputs.upload_url }}
          asset_path: ./npu-sc-${{ needs.create_release.outputs.version }}-source.tar.gz
          asset_name: npu-sc-${{ needs.create_release.outputs.version }}-source.tar.gz
          asset_content_type: application/gzip

  build_documentation:
    needs: create_release
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # Use the composite action for xmake setup
      - name: Setup xmake environment
        uses: ./.github/actions/setup-xmake
        with:
          xmake-version: '2.9.8'
          mode: 'docs'

      # Use the composite action for SystemC setup
      - name: Setup SystemC
        uses: ./.github/actions/setup-systemc
        with:
          systemc-version: ${{ env.SYSTEMC_VERSION }}
          systemc-home: ${{ env.SYSTEMC_HOME }}

      - name: Setup Python for documentation
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'

      - name: Install documentation tools
        run: |
          pip install sphinx breathe doxygen

      - name: Generate API documentation
        run: |
          # 创建基础文档结构
          mkdir -p docs/build
          
          # 生成简单的文档
          cat > docs/build/README.html << 'EOF'
          <!DOCTYPE html>
          <html>
          <head>
              <title>NPU SystemC Documentation</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 40px; }
                  h1 { color: #2c3e50; }
                  h2 { color: #34495e; }
                  code { background-color: #f4f4f4; padding: 2px 4px; }
              </style>
          </head>
          <body>
              <h1>NPU SystemC Framework</h1>
              <h2>Overview</h2>
              <p>A SystemC-based Neural Processing Unit simulation framework.</p>
              
              <h2>Modules</h2>
              <ul>
                  <li><strong>CIM Cluster</strong> - Compute-in-Memory processing units</li>
                  <li><strong>Command Scheduler</strong> - Command scheduling and dispatch</li>
                  <li><strong>Command Decoder</strong> - Instruction decoding</li>
                  <li><strong>TSU/TLU</strong> - Tile Scheduling and Loading Units</li>
                  <li><strong>VPU</strong> - Vector Processing Unit</li>
                  <li><strong>Memory Subsystems</strong> - Local memory management</li>
              </ul>
              
              <h2>Build Instructions</h2>
              <pre><code>xmake f -m release -c
          xmake build --all
          xmake test --all --yes</code></pre>
              
              <h2>Requirements</h2>
              <ul>
                  <li>SystemC 3.0.0+</li>
                  <li>xmake 2.9.8+</li>
                  <li>C++17 compatible compiler</li>
              </ul>
          </body>
          </html>
          EOF
          
          # 压缩文档
          tar -czf npu-sc-docs-${{ needs.create_release.outputs.version }}.tar.gz -C docs/build .

      - name: Upload Documentation
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create_release.outputs.upload_url }}
          asset_path: ./npu-sc-docs-${{ needs.create_release.outputs.version }}.tar.gz
          asset_name: npu-sc-docs-${{ needs.create_release.outputs.version }}.tar.gz
          asset_content_type: application/gzip 