name: NPU SystemC CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  SYSTEMC_VERSION: "3.0.0"
  SYSTEMC_HOME: "/usr/local/systemc-3.0.0"

jobs:
  build_and_test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        mode: [debug, release]
        
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # dorny/paths-filter需要完整历史记录

      # Use the composite action for xmake setup
      - name: Setup xmake environment
        uses: ./.github/actions/setup-xmake
        with:
          xmake-version: '2.9.8'
          mode: ${{ matrix.mode }}

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y clang-18 libc++-18-dev libc++abi-18-dev

      # Use the composite action for SystemC setup
      - name: Setup SystemC
        uses: ./.github/actions/setup-systemc
        with:
          systemc-version: ${{ env.SYSTEMC_VERSION }}
          systemc-home: ${{ env.SYSTEMC_HOME }}

      - name: Configure xmake
        run: |
          xmake f -m ${{ matrix.mode }} -c -y

      - name: Check changed files
        id: files_changed_filter
        uses: dorny/paths-filter@v3
        with:
          base: ${{ github.base_ref || github.ref }}
          filters: |
            core_common:
              - 'include/**'
              - 'ac_types-master/**'
            cim_cluster:
              - 'src/cim_cluster/**'
            cmd_scheduler:
              - 'src/cmd_scheduler/**'
            cmd_decoder:
              - 'src/cmd_decoder/**'
            dev_tsu_tlu:
              - 'src/dev_tsu_tlu/**'
            vpu:
              - 'src/vpu/**'
            scoreboard:
              - 'src/scoreboard/**'
            local_mem:
              - 'src/local_mem/**'
            feat_mpu:
              - 'src/feat_mpu/**'
            feat_tmu:
              - 'src/feat_tmu/**'
            tsu:
              - 'src/tsu/**'
            tlu:
              - 'src/tlu/**'


     

      # Build and test each module conditionally based on dependencies
      
      # Level 1: Modules that only depend on core_common
      - name: Build and test CIM Cluster
        continue-on-error: true
        if: |
          steps.files_changed_filter.outputs.cim_cluster == 'true' ||
          steps.files_changed_filter.outputs.core_common == 'true'
        run: |
          xmake test -g "cim_cluster" || true

      - name: Build and test Scoreboard
        continue-on-error: true
        if: |
          steps.files_changed_filter.outputs.scoreboard == 'true' ||
          steps.files_changed_filter.outputs.core_common == 'true'
        run: |
          xmake test -g "scoreboard" || true

      - name: Build and test CMD Decoder
        continue-on-error: true
        if: |
          steps.files_changed_filter.outputs.cmd_decoder == 'true' ||
          steps.files_changed_filter.outputs.core_common == 'true'
        run: |
          xmake test -g "cmd_decoder" || true

      # Level 2: Modules that depend on Level 1 modules
      - name: Build and test Local Memory
        continue-on-error: true
        if: |
          steps.files_changed_filter.outputs.local_mem == 'true' ||
          steps.files_changed_filter.outputs.cim_cluster == 'true' ||
          steps.files_changed_filter.outputs.core_common == 'true'
        run: |
          xmake test -g "local_mem" || true

      - name: Build and test CMD Scheduler
        continue-on-error: true
        if: |
          steps.files_changed_filter.outputs.cmd_scheduler == 'true' ||
          steps.files_changed_filter.outputs.scoreboard == 'true' ||
          steps.files_changed_filter.outputs.core_common == 'true'
        run: |
          xmake test -g "cmd_scheduler" || true

      # Level 3: Modules that depend on Level 2 modules
      - name: Build and test VPU
        continue-on-error: true
        if: |
          steps.files_changed_filter.outputs.vpu == 'true' ||
          steps.files_changed_filter.outputs.local_mem == 'true' ||
          steps.files_changed_filter.outputs.cim_cluster == 'true' ||
          steps.files_changed_filter.outputs.core_common == 'true'
        run: |
          xmake test -g "vpu" || true

      - name: Build and test Device TSU TLU
        continue-on-error: true
        if: |
          steps.files_changed_filter.outputs.dev_tsu_tlu == 'true' ||
          steps.files_changed_filter.outputs.local_mem == 'true' ||
          steps.files_changed_filter.outputs.cim_cluster == 'true' ||
          steps.files_changed_filter.outputs.core_common == 'true'
        run: |
          xmake test -g "dev_tsu_tlu" || true

      - name: Build and test Feature MPU
        continue-on-error: true
        if: |
          steps.files_changed_filter.outputs.feat_mpu == 'true' ||
          steps.files_changed_filter.outputs.local_mem == 'true' ||
          steps.files_changed_filter.outputs.cim_cluster == 'true' ||
          steps.files_changed_filter.outputs.core_common == 'true'
        run: |
          xmake test -g "feat_mpu" || true

      # Special modules - TSU and TLU
      - name: Build and test TSU modules
        continue-on-error: true
        if: |
          steps.files_changed_filter.outputs.tsu == 'true' ||
          steps.files_changed_filter.outputs.core_common == 'true'
        run: |
          xmake test -g "tsu" || true

      - name: Build and test TLU modules  
        continue-on-error: true
        if: |
          steps.files_changed_filter.outputs.tlu == 'true' ||
          steps.files_changed_filter.outputs.core_common == 'true'
        run: |
          xmake test -g "tlu" || true

      # Feature TMU (currently empty)
      - name: Build Feature TMU
        if: |
          steps.files_changed_filter.outputs.feat_tmu == 'true' ||
          steps.files_changed_filter.outputs.core_common == 'true'
        run: |
          xmake test -g "feat_tmu" 
