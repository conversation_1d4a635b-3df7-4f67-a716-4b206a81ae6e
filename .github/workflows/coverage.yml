name: Code Coverage Analysis

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:  # 允许手动触发

env:
  SYSTEMC_VERSION: "3.0.0"
  SYSTEMC_HOME: "/usr/local/systemc-3.0.0"

# Add permissions for codecov and artifacts
permissions:
  contents: read
  actions: write
  checks: write
  pull-requests: write

jobs:
  coverage:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # 需要完整历史记录用于覆盖率分析

      # Use the composite action for xmake setup
      - name: Setup xmake environment
        uses: ./.github/actions/setup-xmake
        with:
          xmake-version: '2.9.8'
          mode: 'coverage'

      # Install coverage tools and dependencies
      - name: Install coverage dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            clang-18 \
            libc++-18-dev \
            libc++abi-18-dev \
            lcov \
            gcovr \
            build-essential

      # Use the composite action for SystemC setup
      - name: Setup SystemC
        uses: ./.github/actions/setup-systemc
        with:
          systemc-version: ${{ env.SYSTEMC_VERSION }}
          systemc-home: ${{ env.SYSTEMC_HOME }}

      - name: Configure xmake
        run: |
          xmake f -m coverage -c -y

      # Make coverage script executable
      - name: Make coverage script executable
        run: |
          chmod +x scripts/coverage_workaround.sh

      # Execute the coverage workaround script
      - name: Run coverage analysis
        run: |
          echo "=== Starting Coverage Analysis ==="
          ./scripts/coverage_workaround.sh
          echo "=== Coverage Analysis Complete ==="

      # Upload coverage reports to Codecov
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}  # 需要在GitHub Secrets中设置
          files: |
            coverage_reports/combined_coverage.info
            coverage_reports/*_codecov.info
          directory: coverage_reports/
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false  # 不因上传失败而中断CI
          verbose: true

      # Upload HTML coverage reports as artifacts
      - name: Upload HTML coverage reports
        uses: actions/upload-artifact@v4
        with:
          name: coverage-html-reports
          path: |
            coverage_reports/*/html/
            coverage_reports/combined_html/
          retention-days: 30

      # Upload LCOV files as artifacts for manual analysis
      - name: Upload LCOV coverage data
        uses: actions/upload-artifact@v4
        with:
          name: coverage-lcov-data
          path: |
            coverage_reports/*.info
          retention-days: 30

      # Generate coverage summary for PR comments
      - name: Generate coverage summary
        id: coverage_summary
        run: |
          if [ -f "coverage_reports/combined_coverage.info" ]; then
            echo "=== Coverage Summary ===" > coverage_summary.txt
            
            # Extract coverage statistics
            total_lines=$(lcov --summary coverage_reports/combined_coverage.info 2>/dev/null | grep "lines......:" | awk '{print $2}' | cut -d'(' -f1)
            total_functions=$(lcov --summary coverage_reports/combined_coverage.info 2>/dev/null | grep "functions..:" | awk '{print $2}' | cut -d'(' -f1)
            total_branches=$(lcov --summary coverage_reports/combined_coverage.info 2>/dev/null | grep "branches...:" | awk '{print $2}' | cut -d'(' -f1)
            
            echo "📊 **Coverage Summary**" >> coverage_summary.txt
            echo "" >> coverage_summary.txt
            echo "- **Lines:** ${total_lines:-N/A}" >> coverage_summary.txt
            echo "- **Functions:** ${total_functions:-N/A}" >> coverage_summary.txt
            echo "- **Branches:** ${total_branches:-N/A}" >> coverage_summary.txt
            echo "" >> coverage_summary.txt
            
            # List covered modules
            echo "📁 **Modules with Coverage Data:**" >> coverage_summary.txt
            ls coverage_reports/ | grep -E "\.info$" | grep -v combined | sed 's/_codecov\.info$//' | while read module; do
              echo "- $module" >> coverage_summary.txt
            done
            
            echo "" >> coverage_summary.txt
            echo "📈 **Detailed Reports Available in Artifacts**" >> coverage_summary.txt
            echo "- HTML reports: \`coverage-html-reports\`" >> coverage_summary.txt
            echo "- LCOV data: \`coverage-lcov-data\`" >> coverage_summary.txt
            
            # Set output for PR comment
            {
              echo "summary<<EOF"
              cat coverage_summary.txt
              echo "EOF"
            } >> $GITHUB_OUTPUT
          else
            echo "No coverage data generated" >> $GITHUB_OUTPUT
          fi

      # Comment on PR with coverage summary (only for PRs)
      - name: Comment PR with coverage summary
        if: github.event_name == 'pull_request' && steps.coverage_summary.outputs.summary
        uses: actions/github-script@v7
        with:
          script: |
            const summary = `${{ steps.coverage_summary.outputs.summary }}`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: summary
            });

      # Display coverage summary in workflow logs
      - name: Display coverage summary
        run: |
          if [ -f "coverage_summary.txt" ]; then
            echo "=== Coverage Analysis Results ==="
            cat coverage_summary.txt
          else
            echo "No coverage summary available"
          fi
          
          # List generated files for debugging
          echo ""
          echo "=== Generated Coverage Files ==="
          find coverage_reports/ -type f -name "*.info" -o -name "*.html" | head -20 