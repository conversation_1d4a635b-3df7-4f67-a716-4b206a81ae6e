name: 'Setup SystemC'
description: 'Setup SystemC development environment'

inputs:
  systemc-version:
    description: 'SystemC version to install'
    required: false
    default: '3.0.0'
  systemc-home:
    description: 'SystemC installation directory'
    required: false
    default: '/usr/local/systemc-3.0.0'

outputs:
  systemc-path:
    description: 'Path to SystemC installation'
    value: ${{ inputs.systemc-home }}

runs:
  using: "composite"
  steps:
    - name: Install system dependencies
      shell: bash
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential git libtool autoconf automake

    - name: Cache SystemC
      id: cache-systemc
      uses: actions/cache@v4
      with:
        path: |
          ~/systemc-install
        key: systemc-${{ runner.os }}-${{ inputs.systemc-version }}-v1
        restore-keys: |
          systemc-${{ runner.os }}-${{ inputs.systemc-version }}-

    - name: Install SystemC from source
      if: steps.cache-systemc.outputs.cache-hit != 'true'
      shell: bash
      run: |
        SYSTEMC_DIR="systemc-${{ inputs.systemc-version }}"
        INSTALL_DIR="$HOME/systemc-install"
        
        git clone https://github.com/accellera-official/systemc.git
        cd systemc
        ./config/bootstrap
        mkdir objdir
        cd objdir
        ../configure --prefix=${INSTALL_DIR} 'CXXFLAGS=-std=c++17'
        make -j$(nproc)
        make install

    - name: Copy SystemC to system directory
      shell: bash
      run: |
        if [ -d "$HOME/systemc-install" ]; then
          sudo mkdir -p ${{ inputs.systemc-home }}
          sudo cp -r $HOME/systemc-install/* ${{ inputs.systemc-home }}/
          echo "LD_LIBRARY_PATH=${{ inputs.systemc-home }}/lib-linux64:$LD_LIBRARY_PATH" >> $GITHUB_ENV
        fi 