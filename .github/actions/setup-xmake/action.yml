name: 'Setup xmake'
description: 'Setup xmake development environment with caching'

inputs:
  xmake-version:
    description: 'xmake version to install'
    required: false
    default: '2.9.8'
  mode:
    description: 'Build mode (debug, release, etc.)'
    required: false
    default: 'default'

outputs:
  xmake-path:
    description: 'Path to xmake installation'
    value: ${{ steps.xmake-cache.outputs.cache-hit }}

runs:
  using: "composite"
  steps:
    - name: Cache xmake
      id: xmake-cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.local/bin/xmake
          ~/.xmake
          ~/xmake-cache
        key: xmake-${{ runner.os }}-${{ inputs.xmake-version }}-v1

    - name: Setup xmake
      shell: bash
      run: |
        if [ "${{ steps.xmake-cache.outputs.cache-hit }}" == 'true' ]; then
          echo "Restoring xmake from cache..."
          mkdir -p $HOME/.local/bin
          mkdir -p $HOME/.xmake
          if [ -d "$HOME/xmake-cache" ]; then
            cp -r $HOME/xmake-cache/.xmake/* $HOME/.xmake/ || true
            cp -r $HOME/xmake-cache/bin/xmake $HOME/.local/bin/ || true
            chmod +x $HOME/.local/bin/xmake
          fi
        else
          echo "Installing xmake..."
          wget https://xmake.io/shget.text -O - | bash
          
          # 创建缓存目录
          mkdir -p $HOME/xmake-cache/bin
          
          # 复制到缓存目录
          cp -r $HOME/.xmake $HOME/xmake-cache/ || true
          cp -r $HOME/.local/bin/xmake $HOME/xmake-cache/bin/ || true
        fi
        
        # 确保 xmake 在 PATH 中并可执行
        echo "Adding xmake to PATH..."
        echo "$HOME/.local/bin" >> $GITHUB_PATH
        
        # 验证安装
        echo "Verifying xmake installation..."
        $HOME/.local/bin/xmake --version || true
        
        # 如果 xmake 不可用，重新安装
        if ! $HOME/.local/bin/xmake --version &> /dev/null; then
          echo "xmake not found, reinstalling..."
          wget https://xmake.io/shget.text -O - | bash
        fi
        
        # 最终验证
        $HOME/.local/bin/xmake --version
        
        # Configure xmake to automatically confirm installations
        xmake g --policies=run.autobuild
        echo "Configuring xmake to automatically confirm installations..."

    - name: Setup package caching
      uses: xmake-io/github-action-setup-xmake@v1
      with:
        xmake-version: ${{ inputs.xmake-version }}
        # Enable package caching
        package-cache: true
        package-cache-key: ${{ runner.os }}-xmake-packages-${{ hashFiles('**/xmake.lua') }}
        # Enable binary caching
        build-cache: true
        build-cache-key: ${{ runner.os }}-xmake-build-${{ inputs.mode }}-${{ github.sha }} 