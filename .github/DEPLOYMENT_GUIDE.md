# GitHub CI/CD 部署指南

本文档详细说明了如何为NPU SystemC项目启用和配置GitHub Actions CI/CD流程。

## 📋 部署前检查清单

### 1. 仓库设置
- [ ] 确保仓库已推送到GitHub
- [ ] 确保主分支名为 `main` 或根据需要修改工作流中的分支名
- [ ] 检查仓库权限设置，确保Actions可以运行

### 2. Secrets配置
以下secrets需要在GitHub仓库设置中配置：

#### 必需的Secrets
- `GITHUB_TOKEN` - 自动提供，无需手动配置

#### 可选但推荐的Secrets
- `CODECOV_TOKEN` - 用于Codecov覆盖率分析集成（强烈推荐）

#### 可选的Secrets (用于特定功能)
- `SLACK_WEBHOOK_URL` - Slack通知 (如果需要)
- `DISCORD_WEBHOOK_URL` - Discord通知 (如果需要)

### 3. 分支保护规则配置

建议为主分支设置保护规则：

1. 进入 **Settings** → **Branches**
2. 点击 **Add rule**
3. 配置以下选项：
   - Branch name pattern: `main`
   - ✅ Require status checks to pass before merging
   - ✅ Require branches to be up to date before merging
   - 选择状态检查：
     - `build_and_test (debug)`
     - `build_and_test (release)` 
     - `code_quality`
     - `coverage`
   - ✅ Require pull request reviews before merging
   - ✅ Dismiss stale PR approvals when new commits are pushed
   - ✅ Restrict pushes that create files larger than 100 MB

## 🚀 启用步骤

### 步骤 1: 检查文件结构

确保以下文件已正确创建：

```
.github/
├── workflows/
│   ├── ci.yml                    # 主CI/CD流程
│   ├── code-quality.yml          # 代码质量检查
│   ├── coverage.yml              # 覆盖率分析
│   └── release.yml               # 发布流程
└── DEPLOYMENT_GUIDE.md          # 本文档

.clang-format                     # 代码格式配置
README.md                        # 更新的项目说明
```

### 步骤 2: 推送到GitHub

```bash
git add .
git commit -m "feat: add complete GitHub Actions CI/CD pipeline"
git push origin main
```

### 步骤 3: 验证Actions启用

1. 访问GitHub仓库页面
2. 点击 **Actions** 标签页
3. 确认工作流已自动启用
4. 检查是否有运行中的工作流

### 步骤 4: 首次运行验证

首次推送后，Actions会自动运行。预期结果：

- ✅ **CI Pipeline**: 应该成功（可能有部分测试失败，这是正常的）
- ✅ **Code Quality**: 基础检查应该通过
- ⚠️ **Coverage**: 首次运行需要配置Codecov Token
- 📊 **Release**: 仅在创建标签时触发

## 🔧 配置优化

### 1. SystemC路径配置

如果SystemC安装在不同路径，需要修改：

```yaml
# 在所有workflow文件中更新
env:
  SYSTEMC_HOME: "/your/custom/systemc/path"  # 修改此路径
```

### 2. xmake版本固定

建议固定xmake版本以确保一致性：

```yaml
# 在setup-xmake步骤中
with:
  xmake-version: '2.9.8'  # 建议使用固定版本而非'latest'
```

### 3. 缓存策略优化

根据项目大小调整缓存策略：

```yaml
# 对于大项目，可以增加缓存层级
- name: Cache build directory
  uses: actions/cache@v4
  with:
    path: build/
    key: ${{ runner.os }}-build-${{ hashFiles('src/**/*.cpp', 'src/**/*.h') }}
```

### 4. 测试并行度调整

根据项目测试复杂度调整：

```yaml
# 在矩阵策略中添加更多组合
strategy:
  matrix:
    mode: [debug, release]
    compiler: [gcc, clang]  # 如需测试多种编译器
```

## 📊 监控和维护

### 1. 构建状态监控

在README.md中的徽章会显示实时状态：
- 绿色：构建通过
- 红色：构建失败
- 灰色：未运行或跳过

### 2. 失败处理

常见失败原因和解决方案：

#### SystemC构建失败
```bash
# 解决方案：检查SystemC版本和路径
- name: Debug SystemC
  run: |
    ls -la /usr/local/systemc-3.0.0/
    echo $LD_LIBRARY_PATH
```

#### 测试超时
```bash
# 解决方案：增加超时时间
- name: Run tests
  timeout-minutes: 30  # 增加此行
  run: xmake test --all --yes
```

#### 缓存问题
```bash
# 解决方案：清理缓存
# 在GitHub仓库设置 → Actions → Caches 中手动删除
```

### 3. 性能优化

#### 减少构建时间
1. **并行编译**: 确保使用 `make -j$(nproc)`
2. **缓存优化**: 细化缓存键的粒度
3. **条件构建**: 利用 `dorny/paths-filter` 避免不必要的构建

#### 节省资源
1. **跳过重复检查**: 使用条件语句避免重复工作
2. **合理的触发条件**: 避免在文档更改时运行完整构建

## 🔍 故障排除

### 常见问题和解决方案

#### 1. Actions权限问题
```yaml
# 确保在workflow中有正确的权限
permissions:
  contents: read
  actions: write
  checks: write
```

#### 2. 依赖安装失败
```bash
# 添加重试机制
- name: Install dependencies with retry
  run: |
    for i in {1..3}; do
      sudo apt-get update && sudo apt-get install -y build-essential && break
      sleep 10
    done
```

#### 3. 测试环境不一致
```bash
# 使用Docker确保环境一致性（可选）
- name: Setup Docker environment
  run: |
    docker run --rm -v $PWD:/workspace ubuntu:20.04 bash -c "
      apt-get update && apt-get install -y build-essential
      cd /workspace && xmake build
    "
```

## 📈 进阶配置

### 1. 多平台支持

```yaml
strategy:
  matrix:
    os: [ubuntu-latest, ubuntu-20.04, ubuntu-22.04]
    include:
      - os: ubuntu-latest
        platform: modern
      - os: ubuntu-20.04  
        platform: legacy
```

### 2. 性能基准测试

```yaml
- name: Run performance benchmarks
  run: |
    xmake build benchmark_targets
    xmake run benchmark --output=json > benchmark_results.json
    
- name: Upload benchmark results
  uses: actions/upload-artifact@v4
  with:
    name: benchmark-results
    path: benchmark_results.json
```

### 3. 自动发布

配置自动发布需要创建release标签：

```bash
# 创建发布标签
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0
```

## 📞 获取帮助

如果遇到问题：

1. **检查GitHub Actions日志**: 详细错误信息通常在工作流日志中
2. **参考官方文档**: [GitHub Actions文档](https://docs.github.com/en/actions)
3. **社区支持**: [xmake社区](https://github.com/xmake-io/xmake/discussions)
4. **提交Issue**: 在项目仓库中创建issue

---

✅ **部署成功后，你的项目将拥有完整的CI/CD流程，包括自动构建、测试、代码质量检查和发布功能！** 