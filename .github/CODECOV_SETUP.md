# Codecov 覆盖率分析设置指南

本文档说明如何为NPU SystemC项目配置Codecov代码覆盖率分析。

## 📋 设置步骤

### 1. 注册Codecov账户

1. 访问 [codecov.io](https://codecov.io/)
2. 使用GitHub账户登录
3. 授权Codecov访问你的GitHub仓库

### 2. 添加仓库到Codecov

1. 登录后，点击 **Add new repository**
2. 找到你的NPU SystemC项目仓库
3. 点击 **Setup repo** 开始配置

### 3. 获取Codecov Token

1. 在Codecov仓库页面，点击 **Settings** 标签页
2. 在左侧菜单中点击 **General**
3. 找到 **Repository Upload Token**
4. 复制这个token（通常以 `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx` 格式）

### 4. 配置GitHub Secrets

1. 打开GitHub仓库页面
2. 点击 **Settings** → **Secrets and variables** → **Actions**
3. 点击 **New repository secret**
4. 设置：
   - **Name**: `CODECOV_TOKEN`
   - **Secret**: 粘贴从Codecov复制的token
5. 点击 **Add secret**

## 🚀 验证设置

### 自动触发覆盖率分析

覆盖率工作流会在以下情况自动运行：
- 推送到 `main` 或 `dev` 分支
- 创建或更新Pull Request
- 手动触发（在Actions页面）

### 手动触发测试

1. 进入GitHub仓库的 **Actions** 标签页
2. 选择 **Code Coverage Analysis** 工作流
3. 点击 **Run workflow** 按钮
4. 选择分支并点击 **Run workflow**

## 📊 查看覆盖率报告

### 在Codecov上查看

1. 工作流完成后，访问 [codecov.io](https://codecov.io/)
2. 找到你的项目
3. 查看详细的覆盖率报告、趋势图和文件级别的覆盖率

### 在GitHub上查看

1. **Pull Request评论**: 自动在PR中添加覆盖率摘要
2. **Artifacts**: 下载HTML格式的详细报告
   - 进入工作流运行页面
   - 在 **Artifacts** 部分下载：
     - `coverage-html-reports`: HTML格式报告
     - `coverage-lcov-data`: LCOV格式数据

### 添加覆盖率徽章到README

在Codecov项目页面：
1. 点击 **Settings** → **Badge**
2. 复制Markdown格式的徽章代码
3. 粘贴到README.md文件中

示例徽章代码：
```markdown
[![codecov](https://codecov.io/gh/username/npu_sc/branch/main/graph/badge.svg)](https://codecov.io/gh/username/npu_sc)
```

## ⚙️ 自定义配置

### 创建Codecov配置文件

在项目根目录创建 `.codecov.yml` 文件：

```yaml
coverage:
  status:
    project:
      default:
        target: 80%
        threshold: 1%
    patch:
      default:
        target: 70%
  
  ignore:
    - "ac_types-master/**"
    - "build/**"
    - "**/test/**"
    - "**/*_test.cpp"
    - "**/*_test.h"

comment:
  layout: "reach, diff, flags, files"
  behavior: default
  require_changes: false
```

### 覆盖率目标设置

- **Project Coverage**: 整个项目的覆盖率目标（建议80%）
- **Patch Coverage**: 新代码的覆盖率目标（建议70%）
- **Threshold**: 允许的覆盖率下降幅度

## 🔍 故障排除

### 常见问题

#### 1. 上传失败: Invalid token
**解决方案**: 检查GitHub Secrets中的`CODECOV_TOKEN`是否正确设置

#### 2. 没有覆盖率数据
**解决方案**: 
- 检查coverage_workaround.sh脚本是否正常执行
- 确认测试是否成功运行
- 检查.gcda文件是否生成

#### 3. 覆盖率数据不准确
**解决方案**:
- 检查lcov过滤规则是否正确
- 确认源代码路径配置
- 查看工作流日志中的错误信息

### 调试命令

在工作流中添加调试步骤：

```yaml
- name: Debug coverage files
  run: |
    echo "=== Coverage Reports Directory ==="
    ls -la coverage_reports/
    echo "=== Combined Coverage Info ==="
    if [ -f "coverage_reports/combined_coverage.info" ]; then
      head -20 coverage_reports/combined_coverage.info
    fi
```

## 📈 最佳实践

1. **定期检查覆盖率趋势**: 确保覆盖率不会随时间下降
2. **设置合理的覆盖率目标**: 不要设置过高的目标导致开发效率降低
3. **关注关键模块**: 重点关注核心功能模块的覆盖率
4. **排除不必要的文件**: 测试文件、第三方代码等不应计入覆盖率
5. **在PR中关注覆盖率变化**: 新代码应该有合理的测试覆盖

## 🔗 相关链接

- [Codecov文档](https://docs.codecov.com/)
- [GitHub Actions文档](https://docs.github.com/en/actions)
- [LCOV文档](http://ltp.sourceforge.net/coverage/lcov.php)
- [SystemC测试最佳实践](https://systemc.org/) 