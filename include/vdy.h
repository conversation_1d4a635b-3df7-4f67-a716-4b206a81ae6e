#ifndef VALID_READY_MODULE_H
#define VALID_READY_MODULE_H

#include <tlm_utils/simple_initiator_socket.h>
#include <tlm_utils/simple_target_socket.h>
#include <systemc>
#include <tlm>

class Sender : public sc_core::sc_module
{
  public:
    tlm_utils::simple_initiator_socket<Sender> socket;
    sc_core::sc_in<bool> ready_signal;
    sc_core::sc_out<bool> valid_signal;

    Sender(sc_core::sc_module_name name, sc_core::sc_time delay = sc_core::SC_ZERO_TIME);

    void request_transaction(tlm::tlm_generic_payload& trans);

  private:
    sc_core::sc_event m_transaction_request_event;
    sc_core::sc_time m_delay;
    tlm::tlm_generic_payload* m_current_transaction;

    void transaction_process();
    void send_transaction();
};

class Receiver : public sc_core::sc_module
{
  public:
    tlm_utils::simple_target_socket<Receiver> socket;
    sc_core::sc_in<bool> valid_signal;
    sc_core::sc_out<bool> ready_signal;
    tlm::tlm_generic_payload* current_transaction;  // 存储当前接收到的事务
    sc_core::sc_signal<bool> is_received;           // 是否接收到数据
    Receiver(sc_core::sc_module_name name, sc_core::sc_time delay = sc_core::SC_ZERO_TIME);

    void b_transport(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    tlm::tlm_sync_enum nb_transport_fw(tlm::tlm_generic_payload& trans,
                                       tlm::tlm_phase& phase,
                                       sc_core::sc_time& delay);

    // 当外界接收处理后，调用finish()方法
    void finish();

  private:
    sc_core::sc_time m_delay;  // 延迟时间

    void process_received_data(tlm::tlm_generic_payload& payload);
};

#endif  // VALID_READY_MODULE_H
