#ifndef CMD_SCHEDULER_H
#define CMD_SCHEDULER_H

#include "cmd_dispatch.h"
#include "rsp_rob.h"
#include "scoreboard.h"
#include "tlm_utils/simple_initiator_socket.h"

class CmdScheduler : public sc_core::sc_module
{
  public:
    // 外部信号
    sc_core::sc_in<bool> cmd_queue_valid;
    sc_core::sc_out<bool> cmd_queue_ready;
    sc_core::sc_out<bool> rspq_valid;
    sc_core::sc_in<bool> rspq_ready;
    std::array<sc_core::sc_out<bool>, NPUConfig::FU_NUM> isq_cmd_valid;
    std::array<sc_core::sc_in<bool>, NPUConfig::FU_NUM> isq_cmd_ready;
    std::array<sc_core::sc_in<bool>, NPUConfig::FU_NUM> wbq_rsp_valid;
    std::array<sc_core::sc_out<bool>, NPUConfig::FU_NUM> wbq_rsp_ready;
    CmdScheduler(const sc_core::sc_module_name& name, uint8_t rob_queue_size);
    CmdDispatch m_cmd_dispatch;
    RSP_ROB m_rsp_rob;

  private:
    // 内部 disp_roflag 信号
    sc_core::sc_signal<bool> m_disp_roflag_valid;
    sc_core::sc_signal<bool> m_disp_roflag_ready;
    sc_core::sc_signal<bool> m_term_sync;

    void b_transport_cmd_input(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    void b_transport_wbq_rsp(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
};

#endif