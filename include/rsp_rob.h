#pragma once

#include <tlm_utils/simple_target_socket.h>
#include <array>
#include <queue>
#include <systemc>
#include <tlm>
#include "npu_config.h"
#include "sysc/communication/sc_signal_ports.h"
#include "sysc/kernel/sc_event.h"
#include "tlm_utils/simple_initiator_socket.h"
#include "utils/systemc_logger.h"

/**
 * @brief Structure representing the response information for the write-back queue.
 *
 * This structure contains an array of response data and an array of flags indicating the validity
 * of the response data. The response data is stored as an array of unsigned integers with a width
 * of NPUConfig::RV_XLEN, where NPUConfig::RV_XLEN is a constant representing the XLEN value. The
 * size of the arrays is determined by the constant NPUConfig::FU_NUM.
 *
 * The default constructor initializes the response data array with zeros and the validity flags
 * array with false.
 */
struct wbq_rsp_info
{
    std::array<sc_dt::sc_uint<NPUConfig::RV_XLEN>, NPUConfig::FU_NUM> wbq_rsp_data;
    wbq_rsp_info()
    {
        wbq_rsp_data.fill(0);
    }
};

/**
 * @brief Structure representing the information of disp_roflag.
 *
 * This structure holds the data and validity flag of disp_roflag.
 * The data is represented by a sc_uint with a width of 8 + NPUConfig::FU_ID_WD.
 * The validity flag indicates whether the data is valid or not.
 *
 * The default constructor initializes the disp_roflag_data to 0 and the validity flag to false.
 */
struct disp_roflag_info
{
    sc_dt::sc_uint<8 + NPUConfig::FU_ID_WD> disp_roflag_data;
    disp_roflag_info() : disp_roflag_data(0)
    {
    }
};

/**
 * @struct rspq_rsp_info
 * @brief Structure representing the response information in the response queue.
 *
 * This structure contains the response data and validity flag for the response queue.
 * The response data is stored as an unsigned integer with a width of NPUConfig::RV_XLEN + 5.
 * The validity flag indicates whether the response data is valid or not.
 *
 * @var rspq_rsp_info::rspq_rsp_data
 * The response data stored as an unsigned integer.
 *
 * @var rspq_rsp_info::rspq_rsp_valid
 * The validity flag for the response data.
 *
 * @public
 * @constructor
 * @brief Default constructor for rspq_rsp_info.
 *
 * Initializes the response data to 0 and the validity flag to false.
 */
struct rspq_rsp_info
{
    sc_dt::sc_uint<NPUConfig::RV_XLEN + 5> rspq_rsp_data;
    rspq_rsp_info() : rspq_rsp_data(0)
    {
    }
};

/**
 * @class RSP_ROB
 * @brief Represents a RSP_ROB module.
 *
 * The RSP_ROB class is a module that implements a RSP_ROB (Reorder Buffer) in a SystemC simulation.
 * It provides target and initiator sockets for communication with other modules, as well as signals
 * for synchronization and control. The module maintains a queue of disp_roflag_info objects and
 * handles incoming transactions from the wbq_rsp_socket and disp_roflag_socket. It also includes
 * a decoder_thread for processing the transactions and sending responses to the rspq_socket.
 *
 * @param rob_queue_size The size of the ROB queue.
 */
class RSP_ROB : public sc_core::sc_module
{
  public:
    tlm_utils::simple_target_socket<RSP_ROB> wbq_rsp_socket;
    tlm_utils::simple_target_socket<RSP_ROB> disp_roflag_socket;
    tlm_utils::simple_initiator_socket<RSP_ROB> rspq_socket;
    sc_core::sc_out<bool> term_sync;  // termination signal to cmd_scheduler

    std::array<sc_core::sc_in<bool>, NPUConfig::FU_NUM> wbq_rsp_valid;
    std::array<sc_core::sc_out<bool>, NPUConfig::FU_NUM> wbq_rsp_ready;
    sc_core::sc_in<bool> disp_roflag_valid;
    sc_core::sc_out<bool> disp_roflag_ready;
    sc_core::sc_out<bool> rspq_valid;
    sc_core::sc_in<bool> rspq_ready;  // ready signal from rspq

    // SC_HAS_PROCESS(RSP_ROB);
    RSP_ROB(const sc_core::sc_module_name& name, unsigned int rob_queue_size)
        : sc_module(name),
          term_sync("term_sync"),
          rspq_ready("rspq_ready"),
          rob_queue_size_(rob_queue_size)
    {
        wbq_rsp_socket.register_b_transport(this, &RSP_ROB::b_transport_wbq_rsp);
        disp_roflag_socket.register_b_transport(this, &RSP_ROB::b_transport_disp_roflag);
        SC_THREAD(decoder_thread);
        SC_METHOD(update_disp_roflag_ready);
        sensitive << rob_queue_event;
        // 默认所有wbq_rsp_ready都为true
        for (int i = 0; i < NPUConfig::FU_NUM; i++)
        {
            wbq_rsp_ready[i].initialize(true);
        }
        SC_METHOD(wbq_rsp_valid_change_detector);
        sensitive << wbq_rsp_valid;
    }
    void b_transport_wbq_rsp(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);

  private:
    std::queue<disp_roflag_info> rob_queue;
    wbq_rsp_info latest_wbq_rsp;
    sc_core::sc_event disp_roflag_event;
    sc_core::sc_event rob_queue_event;
    sc_core::sc_event wbq_rsp_valid_changed_event;
    unsigned int rob_queue_size_;

    void b_transport_disp_roflag(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    void decoder_thread();
    // bool is_rspq_blocked();
    // bool is_wbq_blocked();
    // bool is_roflag_blocked();
    void handle_blocked();
    // void handle_active();
    void update_disp_roflag_ready();
    void wbq_rsp_valid_change_detector();
    void send_rspq_rsp(const rspq_rsp_info& rsp);
};