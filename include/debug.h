#pragma once

#include <iostream>
#include <systemc>

// 定义颜色宏
#define RESET "\033[0m"
#define BLACK "\033[30m"              /* Black */
#define RED "\033[31m"                /* Red */
#define GREEN "\033[32m"              /* Green */
#define YELLOW "\033[33m"             /* Yellow */
#define BLUE "\033[34m"               /* Blue */
#define MAGENTA "\033[35m"            /* Magenta */
#define CYAN "\033[36m"               /* Cyan */
#define WHITE "\033[37m"              /* White */
#define BOLDBLACK "\033[1m\033[30m"   /* Bold Black */
#define BOLDRED "\033[1m\033[31m"     /* Bold Red */
#define BOLDGREEN "\033[1m\033[32m"   /* Bold Green */
#define BOLDYELLOW "\033[1m\033[33m"  /* Bold Yellow */
#define BOLDBLUE "\033[1m\033[34m"    /* Bold Blue */
#define BOLDMAGENTA "\033[1m\033[35m" /* Bold Magenta */
#define BOLDCYAN "\033[1m\033[36m"    /* Bold Cy<PERSON> */
#define BOLDWHITE "\033[1m\033[37m"   /* Bold White */

#define RSP_ROB_COLOR GREEN
#define TESTBENCH_COLOR RED
#define CMD_COLOR BOLDCYAN
#define CMD_DISPATCH_COLOR BOLDCYAN
#define SCOREBOARD_COLOR BOLDYELLOW

static void debug_print(const std::string& msg, const std::string& color = RESET)
{
#ifdef DEBUG_PRINT
    std::cout << color << sc_core::sc_time_stamp() << " " << msg << RESET << std::endl;
#endif
}

static void cmd_print(const std::string& msg, const std::string& color = RESET)
{
#ifdef DEBUG_PRINT
    std::cout << color << msg << RESET << std::endl;
#endif
}
