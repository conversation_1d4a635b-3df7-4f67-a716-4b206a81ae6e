#include "sc_logger.h"

SC_MODULE(Producer) {
    sc_core::sc_out<uint32_t> data_out;
    sc_core::sc_out<uint64_t> addr_out;
    sc_core::sc_in<bool> clk;

    void produce() {
        uint64_t addr = 0x1000;
        while (true) {
            wait(clk->posedge_event());
            uint32_t data = (uint32_t)sc_core::sc_time_stamp().value();
            
            SC_DEBUG("PRODUCER_DEBUG", "  TX Details:  addr: {:04x}, data: {:04x}", addr, data);
            
            data_out->write(data);
            addr_out->write(addr);
            addr += 4;

            if (addr > 0x1010) {
                SC_WARN("PRODUCER_WARN", "Address space wrapping around.");
                addr = 0x1000;
            }
        }
    }

    SC_CTOR(Producer) {
        SC_THREAD(produce);
        sensitive << clk;
    }
};

SC_MODULE(Consumer) {
    sc_core::sc_in<uint32_t> data_in;
    sc_core::sc_in<uint64_t> addr_in;
    sc_core::sc_in<bool> clk;

    void consume() {
        while (true) {
            wait(clk->posedge_event());
            uint32_t data = data_in->read();
            uint64_t addr = addr_in->read();
            
            SC_INFO("CONSUMER_INFO", "Consumed transaction: addr: {:04x}, data: {:04x}", addr, data);
        }
    }

    SC_CTOR(Consumer) {
        SC_THREAD(consume);
        sensitive << clk;
        dont_initialize();
    }
};

int sc_main(int argc, char* argv[]) {
    // 初始化日志系统
    sc_logger::initialize(spdlog::level::debug, "simulation.log");
    SC_INFO("SIMULATION_INFO", "Simulation starting...");

    sc_core::sc_clock clk("clk", 10, sc_core::SC_NS);
    sc_core::sc_signal<uint32_t> data_channel;
    sc_core::sc_signal<uint64_t> addr_channel;

    Producer prod("producer");
    prod.clk(clk);
    prod.data_out(data_channel);
    prod.addr_out(addr_channel);

    Consumer cons("consumer");
    cons.clk(clk);
    cons.data_in(data_channel);
    cons.addr_in(addr_channel);

    sc_core::sc_start(100, sc_core::SC_NS);

    SC_INFO("SIMULATION_INFO", "Simulation finished.");
    
    // 手动打印统计信息，避免依赖析构函数
    sc_logger::print_id_statistics();

    return 0;
}