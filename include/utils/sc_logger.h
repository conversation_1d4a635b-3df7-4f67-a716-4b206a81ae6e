#ifndef SC_LOGGER_H
#define SC_LOGGER_H

#include <cstdint>
#include <cstring>
#include <memory>
#include <string>
#include <systemc>
#include <unordered_map>
#include <mutex>
#include <fstream>
#include <thread>
#include <chrono>

//  集成 spdlog
#define SPDLOG_HEADER_ONLY
#include "spdlog/pattern_formatter.h"
#include "spdlog/sinks/basic_file_sink.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "spdlog/spdlog.h"
#include "spdlog/details/log_msg.h"

// 前置声明
namespace sc_logger
{
// ID统计管理器
class IDStatsManager {
private:
    std::unordered_map<std::string, size_t> id_counts_;
    std::mutex mutex_;
    
public:
    static IDStatsManager& instance() {
        static IDStatsManager instance;
        return instance;
    }
    
    void record_id(const std::string& id) {
        std::lock_guard<std::mutex> lock(mutex_);
        id_counts_[id]++;
        // 调试：验证ID是否被正确记录 (已验证功能正常)
        // std::cout << "[DEBUG] Recorded ID: " << id << " (count: " << id_counts_[id] << ")\n";    
    }
    
    // 只负责获取统计数据，实际输出由外部函数处理
    std::unordered_map<std::string, size_t> get_statistics_copy() {
        std::lock_guard<std::mutex> lock(mutex_);
        return id_counts_;
    }
    
    void print_statistics() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (id_counts_.empty()) {
            std::cout << "\n=== SC_LOGGER ID Statistics ===\n";
            std::cout << "No IDs recorded.\n";
            std::cout << "===============================\n";
            return;
        }
        
        // Find the maximum ID length for alignment
        size_t max_id_len = 0;
        for (const auto& pair : id_counts_) {
            if (pair.first.length() > max_id_len) {
                max_id_len = pair.first.length();
            }
        }

        std::cout << "\n=== SC_LOGGER ID Statistics ===\n";
        for (const auto& pair : id_counts_) {
            std::cout << "[" << pair.first << "]";
            // Calculate padding spaces to align counts. +6 for consistent spacing after ']'
            size_t spaces_to_add = (max_id_len - pair.first.length()) + 6; 
            for (size_t i = 0; i < spaces_to_add; ++i) {
                std::cout << " ";
            }
            std::cout << pair.second << "\n";
        }
        std::cout << "===============================\n";
        std::cout.flush(); // Ensure output is flushed
    }
    
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        id_counts_.clear();
    }
};

// 全局 logger 实例访问函数（C++11兼容）
inline std::shared_ptr<spdlog::logger>& get_logger()
{
    static std::shared_ptr<spdlog::logger> g_logger;
    return g_logger;
}

// 当前日志ID存储（线程局部存储）- 使用函数内静态变量避免多重定义
inline std::string& get_current_log_id() {
    thread_local std::string current_log_id = "";
    return current_log_id;
}

// 设置当前日志ID
inline void set_current_id(const std::string& id) {
    get_current_log_id() = id;
    // 记录ID统计
    IDStatsManager::instance().record_id(id);
}

// 获取当前日志ID
inline const std::string& get_current_id() {
    return get_current_log_id();
}

// 获取当前模块名，处理非进程上下文
inline const char* get_module_name()
{
    sc_core::sc_object* parent = sc_core::sc_get_current_process_b()
                                     ? sc_core::sc_get_current_process_b()->get_parent_object()
                                     : nullptr;
    return parent ? parent->basename() : "sc_main";
}

// 获取当前进程名
inline const char* get_process_name_str()
{
    sc_core::sc_process_b* handle = sc_core::sc_get_current_process_b();
    return handle ? handle->name() : "elaboration";
}
}  // namespace sc_logger

//  为 SystemC 类型特化 fmt::formatter
template <>
struct fmt::formatter<sc_core::sc_time>
{
    constexpr auto parse(format_parse_context& ctx) const -> decltype(ctx.begin())
    {
        return ctx.begin();
    }

    template <typename FormatContext>
    auto format(const sc_core::sc_time& t, FormatContext& ctx) const -> decltype(ctx.out())
    {
        return fmt::format_to(ctx.out(), "{}", t.to_string());
    }
};


//  定义 spdlog 自定义格式标志
namespace sc_logger
{
// %M: 模块名
class module_name_flag : public spdlog::custom_flag_formatter
{
  public:
    void format(const spdlog::details::log_msg&,
                const std::tm&,
                spdlog::memory_buf_t& dest) override
    {
        const char* name = get_module_name();
        dest.append(name, name + strlen(name));
    }
    std::unique_ptr<spdlog::custom_flag_formatter> clone() const override
    {
        return spdlog::details::make_unique<module_name_flag>();
    }
};

// %p_sc: 进程名
class process_name_flag : public spdlog::custom_flag_formatter
{
  public:
    void format(const spdlog::details::log_msg&,
                const std::tm&,
                spdlog::memory_buf_t& dest) override
    {
        const char* name = get_process_name_str();
        dest.append(name, name + strlen(name));
    }
    std::unique_ptr<spdlog::custom_flag_formatter> clone() const override
    {
        return spdlog::details::make_unique<process_name_flag>();
    }
};

// %T_sc: SystemC时间戳
class systemc_time_flag : public spdlog::custom_flag_formatter
{
  public:
    void format(const spdlog::details::log_msg&,
                const std::tm&,
                spdlog::memory_buf_t& dest) override
    {
        std::string time_str = sc_core::sc_time_stamp().to_string();
        // 对齐时间字符串到固定宽度 (例如 10 字符)
        if (time_str.length() < 10)
        {
            time_str = std::string(10 - time_str.length(), ' ') + time_str;
        }
        dest.append(time_str.data(), time_str.data() + time_str.size());
    }
    std::unique_ptr<spdlog::custom_flag_formatter> clone() const override
    {
        return spdlog::details::make_unique<systemc_time_flag>();
    }
};

// %I: ID信息
class id_flag : public spdlog::custom_flag_formatter
{
  public:
    void format(const spdlog::details::log_msg&,
                const std::tm&,
                spdlog::memory_buf_t& dest) override
    {
        const std::string& id = get_current_id();
        std::string padded_id;
        // Pad the ID to a fixed width (e.g., 25 characters), left-aligned
        if (!id.empty()) {
            padded_id = fmt::format("{:<25}", id); // Using fmt for padding
        } else {
            padded_id = fmt::format("{:<25}", "NO_ID");
        }
        dest.append(padded_id.data(), padded_id.data() + padded_id.size());
    }
    std::unique_ptr<spdlog::custom_flag_formatter> clone() const override
    {
        return spdlog::details::make_unique<id_flag>();
    }
};

// %L_sc: SystemC 日志级别 (带_SC后缀的完整级别名称)
class systemc_level_flag : public spdlog::custom_flag_formatter
{
  public:
    void format(const spdlog::details::log_msg& msg,
                const std::tm&,
                spdlog::memory_buf_t& dest) override
    {
        std::string level_name;
        switch (msg.level) {
            case spdlog::level::trace:
                level_name = "TRACE_SC";
                break;
            case spdlog::level::debug:
                level_name = "DEBUG_SC";
                break;
            case spdlog::level::info:
                level_name = "INFO_SC";
                break;
            case spdlog::level::warn:
                level_name = "WARN_SC";
                break;
            case spdlog::level::err:
                level_name = "ERROR_SC";
                break;
            case spdlog::level::critical:
                level_name = "FATAL_SC";
                break;
            default:
                level_name = "UNKNOWN_SC";
                break;
        }
        
        // Pad to fixed width (12 characters) for alignment, left-aligned
        std::string padded_level = fmt::format("{:<12}", level_name);
        dest.append(padded_level.data(), padded_level.data() + padded_level.size());
    }
    std::unique_ptr<spdlog::custom_flag_formatter> clone() const override
    {
        return spdlog::details::make_unique<systemc_level_flag>();
    }
};
}  // namespace sc_logger

//  定义用户接口宏 - 只支持带ID的版本
#define SC_LOG_IMPL(level, id, ...)                                                                \
    do {                                                                                           \
        if (sc_logger::get_logger() && sc_logger::get_logger()->should_log(level))                 \
        {                                                                                          \
            sc_logger::set_current_id(id);                                                         \
            sc_logger::get_logger()->log(                                                          \
                spdlog::source_loc{__FILE__, __LINE__, SPDLOG_FUNCTION}, level, __VA_ARGS__);      \
        }                                                                                          \
    } while(0)

// 主要接口宏 - 所有日志都必须带ID
#define SC_TRACE(id, ...) SC_LOG_IMPL(spdlog::level::trace, id, __VA_ARGS__)
#define SC_DEBUG(id, ...) SC_LOG_IMPL(spdlog::level::debug, id, __VA_ARGS__)
#define SC_INFO(id, ...) SC_LOG_IMPL(spdlog::level::info, id, __VA_ARGS__)
#define SC_WARN(id, ...) SC_LOG_IMPL(spdlog::level::warn, id, __VA_ARGS__)
#define SC_ERROR(id, ...) SC_LOG_IMPL(spdlog::level::err, id, __VA_ARGS__)
#define SC_FATAL(id, ...) SC_LOG_IMPL(spdlog::level::critical, id, __VA_ARGS__)

//  SystemC时间格式化辅助函数
namespace sc_logger
{
inline std::string format_time()
{
    return sc_core::sc_time_stamp().to_string();
}

// 当前初始化时的日志文件名（用于统计信息直接写入）
inline std::string& get_log_filename() {
    static std::string log_filename;
    return log_filename;
}

//  初始化函数
inline void initialize(spdlog::level::level_enum console_level = spdlog::level::info,
                       const std::string& log_file = "",
                       spdlog::level::level_enum file_level = spdlog::level::debug)
{
    // 保存日志文件名，供统计信息直接写入使用
    get_log_filename() = log_file;
    
    std::vector<spdlog::sink_ptr> sinks;
    auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    console_sink->set_level(console_level);
    sinks.push_back(console_sink);

    if (!log_file.empty())
    {
        auto file_sink = std::make_shared<spdlog::sinks::basic_file_sink_mt>(log_file, true);
        file_sink->set_level(file_level);
        sinks.push_back(file_sink);
    }

    get_logger() = std::make_shared<spdlog::logger>("sc_logger", sinks.begin(), sinks.end());
    get_logger()->set_level(spdlog::level::trace);  // Logger 本身级别设为最低，由 sink 控制过滤

    auto formatter = spdlog::details::make_unique<spdlog::pattern_formatter>();
    formatter->add_flag<module_name_flag>('M')
        .add_flag<process_name_flag>('p')
        .add_flag<systemc_time_flag>('T')
        .add_flag<id_flag>('I')
        .add_flag<systemc_level_flag>('L');

    // 新格式: [LEVEL][ID][文件:行][时间] 消息
    formatter->set_pattern("[%L][%I][%s:%-4#][%T] %v");
    get_logger()->set_formatter(std::move(formatter));
}

// 打印ID统计信息
inline void print_id_statistics() {
    // 首先打印到终端（保持原格式）
    IDStatsManager::instance().print_statistics();
    
    // 确保所有待写入的日志都被刷新到文件
    if (get_logger()) {
        get_logger()->flush();
        // 额外等待一小段时间确保文件系统写入完成
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 然后直接追加到日志文件（不带任何格式）
    auto stats = IDStatsManager::instance().get_statistics_copy();
    const std::string& filename = get_log_filename();
    
    if (!filename.empty()) {
        std::ofstream file(filename, std::ios::app);
        if (file.is_open()) {
            if (stats.empty()) {
                file << "\n╔══════════════════════════════════════════════════════════════════════════╗\n";
                file << "║                          SC_LOGGER ID Statistics                        ║\n";
                file << "╠══════════════════════════════════════════════════════════════════════════╣\n";
                file << "║ No IDs recorded.                                                         ║\n";
                file << "╚══════════════════════════════════════════════════════════════════════════╝\n";
            } else {
                file << "\n╔══════════════════════════════════════════════════════════════════════════╗\n";
                file << "║                          SC_LOGGER ID Statistics                        ║\n";
                file << "╠══════════════════════════════════════════════════════════════════════════╣\n";
                
                for (const auto& pair : stats) {
                    std::string id_display = pair.first.empty() ? "<EMPTY_ID>" : pair.first;
                    file << fmt::format("║ [{:<40}] {:>10} ║\n", id_display, pair.second);
                }
                
                file << "╚══════════════════════════════════════════════════════════════════════════╝\n";
            }
            file.flush(); // 确保立即写入文件
            file.close();
        }
    }
}

// 清除ID统计信息
inline void clear_id_statistics() {
    IDStatsManager::instance().clear();
}

// 析构时自动打印统计的RAII类
class AutoStatsPrinter {
public:
    AutoStatsPrinter() {}
    
    ~AutoStatsPrinter() {
       
    }
};

// 启用自动统计打印（程序结束时）
inline void enable_auto_stats() {
    static AutoStatsPrinter auto_printer;
}
}  // namespace sc_logger

#endif  // SC_LOGGER_H