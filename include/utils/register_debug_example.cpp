/*
 * register_debug_example.cpp
 * 
 * 示例代码：展示如何使用 sc_logger 对 register.h 中的结构体进行调试打印
 * 新增功能：支持ID信息的日志记录和统计
 * 
 * 编译前需要安装 spdlog 库
 * 使用方法：
 * 1. 确保包含了 sc_logger.h 
 * 2. 包含 register_formatters.h 以启用自定义格式化器
 * 3. 在 sc_main 中初始化 sc_logger
 * 4. 使用带ID的日志宏进行记录
 */

#include <systemc>
#include "sc_logger.h"          // 必须在 register_formatters.h 之前包含
#include "register.h"
#include "npu_config.h"
#include "struct_formatters.h"
using namespace hardware_instruction;
using namespace sc_core;

SC_MODULE(RegisterTestModule) {
    TldConfigManager tld_manager;
    MpConfigManager mp_manager;
    VpConfigManager vp_manager;
    
    SC_CTOR(RegisterTestModule) {
        SC_THREAD(test_process);
    }
    
    void test_process() {
        // 使用带ID的日志记录
        SC_INFO("TEST_START", "Starting register configuration test...");
        
        // 测试 TLD 配置
        test_tld_config();
        
        // 测试 MP 配置  
        test_mp_config();
        
        // 测试 VP 配置
        test_vp_config();
        
        // 测试 IssueQueueCmd 配置
        test_issue_queue_cmd();
        
        // 测试单独的结构体日志
        test_individual_structs();
        
        // 测试多种不同的ID
        test_various_ids();
        
        SC_INFO("TEST_END", "Register configuration test completed.");
    }
    
private:
    void test_tld_config() {
        SC_INFO("TLD_TEST", "=== Testing TLD Configuration ===");
        
        // 设置一些寄存器值
        tld_manager.writeReg(0, 0x12345678);
        tld_manager.writeReg(1, 0xABCDEF00);
        tld_manager.writeReg(2, 0x11223344);
        
        // 使用带ID的日志记录
        SC_INFO("TLD_CONFIG", "Traditional printing:");
        tld_manager.printConfig();
        
        SC_INFO("TLD_CONFIG", "Using sc_logger:");
        tld_manager.logConfig();     // INFO 级别
        tld_manager.debugConfig();   // DEBUG 级别
        
        // 打印所有寄存器和配置
        SC_INFO("TLD_ALL", "Logging all TLD configurations");
        tld_manager.logAll();
        
        // 测试相同ID的多次调用
        SC_INFO("TLD_MULTI", "First call with TLD_MULTI ID");
        SC_INFO("TLD_MULTI", "Second call with TLD_MULTI ID");
        SC_INFO("TLD_MULTI", "Third call with TLD_MULTI ID");
    }
    
    void test_mp_config() {
        SC_INFO("MP_TEST", "=== Testing MP Configuration ===");
        
        // 设置 MP 配置的一些字段
        mp_manager.writeReg(0, 0x003FFFFF);  // 设置 precision 相关字段
        mp_manager.writeReg(1, 0x001F07FF);  // 设置 tensor 相关字段
        mp_manager.writeReg(9, 0x3FFFFFFF);  // 设置 kernel 相关字段
        
        // 使用 sc_logger 打印配置
        SC_INFO("MP_CONFIG", "MP Configuration:");
        mp_manager.logConfig();
        
        // 调试级别打印所有详细信息
        SC_DEBUG("MP_DEBUG", "Debugging all MP configurations");
        mp_manager.debugAll();
        
        // 测试错误和警告日志
        SC_WARN("MP_WARN", "This is a warning message for MP configuration");
        SC_ERROR("MP_ERROR", "This is an error message for MP configuration");
    }
    
    void test_vp_config() {
        SC_INFO("VP_TEST", "=== Testing VP Configuration ===");
        
        // 设置 VP 配置
        vp_manager.writeReg(0, 0x0003FFFF);  // 设置 precision 和 operation
        vp_manager.writeReg(1, 0x001F07FF);  // 设置 size 相关字段
        
        SC_INFO("VP_CONFIG", "VP Configuration:");
        vp_manager.logConfig();
    }
    
    void test_issue_queue_cmd() {
        SC_INFO("ISSUE_QUEUE_TEST", "=== Testing IssueQueueCmd Configuration ===");
        instruction::IssueQueueCmd cmd;
        cmd.funct7 = 0x1;
        cmd.xd = true;
        cmd.xs1 = true;
        cmd.rs1val = 0x12345678;
        cmd.rs2val = 0xABCDEF00;
        SC_INFO("ISSUE_QUEUE_CONFIG", "IssueQueueCmd Configuration:");
        SC_INFO("ISSUE_QUEUE_DATA", "{}", cmd);
    }
    
    void test_individual_structs() {
        SC_INFO("STRUCT_TEST", "=== Testing Individual Struct Logging ===");
        
        // 创建并测试各种配置结构体
        TensorTransferConfig ttc{};
        ttc.cfg_type = 1;
        ttc.cfg_wd = 0x7;
        ttc.cfg_rem_dim0 = 15;
        ttc.cfg_size_dim0b = 1024;
        ttc.cfg_size_dim1 = 2048;
        ttc.cfg_size_dim2 = 4096;
        ttc.cfg_stride_dim1_gmem = 128;
        ttc.cfg_stride_dim2_gmem = 256;
        ttc.cfg_stride_dim1_lmem = 64;
        ttc.cfg_stride_dim2_lmem = 128;
        
        SC_INFO("TENSOR_TRANSFER", "TensorTransferConfig example: {}", ttc);
        
        MatrixProcessConfig mpc{};
        mpc.cfg_type_out = 1;
        mpc.cfg_type_orig = 2; 
        mpc.cfg_type_in = 3;
        mpc.cfg_type_wt = 0;
        mpc.cfg_wd_ref = 7;
        mpc.cfg_accu = 1;
        mpc.cfg_act = 0;
        mpc.cfg_shift = 8;
        mpc.cfg_k_x = 3;
        mpc.cfg_k_y = 3;
        mpc.cfg_slide_x = 1;
        mpc.cfg_slide_y = 1;
        
        SC_INFO("MATRIX_PROCESS", "MatrixProcessConfig example: {}", mpc);
        
        VectorProcessConfig vpc{};
        vpc.cfg_type_out = 1;
        vpc.cfg_type_in1 = 2;
        vpc.cfg_type_in2 = 3;
        vpc.cfg_wd_ref = 7;
        vpc.cfg_op = 0x15;  // 某种运算操作
        vpc.cfg_rem_dim0_ref = 31;
        vpc.cfg_size_dim0b_ref = 1024;
        
        SC_INFO("VECTOR_PROCESS", "VectorProcessConfig example: {}", vpc);
        
        // 测试 FieldDef
        FieldDef field = {0x100, 15, 8, "test_field"};
        SC_INFO("FIELD_DEF", "FieldDef example: {}", field);
        
        // 测试 ConfigType 枚举
        ConfigType type = ConfigType::MP;
        SC_INFO("CONFIG_TYPE", "ConfigType example: {}", type);
    }
    
    void test_various_ids() {
        SC_INFO("ID_VARIETY_TEST", "=== Testing Various ID Types ===");
        
        // 测试不同类型的ID
        SC_INFO("MEMORY_OP", "Memory operation completed successfully");
        SC_INFO("MEMORY_OP", "Another memory operation started");
        SC_INFO("MEMORY_OP", "Memory operation with error handling");
        
        SC_DEBUG("DEBUG_INFO", "Debug information for troubleshooting");
        SC_DEBUG("DEBUG_INFO", "More debug details");
        
        SC_WARN("PERF_WARN", "Performance warning detected");
        SC_ERROR("SYS_ERROR", "System error occurred");
        SC_FATAL("CRITICAL", "Critical system failure");
        
        // 测试特殊ID类型
        SC_INFO("BACKWARD_COMPAT", "This log uses a special ID for backward compatibility");
        SC_WARN("SPECIAL_WARN", "Warning with special ID");
        
        // 测试空ID
        SC_INFO("", "This log has an empty ID");
        
        // 测试长ID
        SC_INFO("VERY_LONG_ID_NAME_FOR_TESTING_PURPOSES", "This log has a very long ID");
    }
};

int sc_main(int /* argc */, char* /* argv */[]) {
    // 初始化 sc_logger
    sc_logger::initialize(
        spdlog::level::debug,    // 控制台输出级别
        "register_test.log",     // 日志文件名
        spdlog::level::trace     // 文件输出级别
    );
    
    // 启用自动统计打印功能
    sc_logger::enable_auto_stats();
    
    SC_INFO("SYSTEM", "SystemC Register Debug Example Started");
    
    // 创建测试模块
    RegisterTestModule test_module("test_module");
    
    // 运行仿真
    sc_start(100, SC_NS);
    
    SC_INFO("SYSTEM", "SystemC Register Debug Example Completed");
    
    // 手动打印统计信息（可选，因为已经启用了自动打印）
    SC_INFO("STATS", "Printing ID statistics manually:");
    sc_logger::print_id_statistics();
    
    return 0;
}

/*
 * 预期输出示例:
 * 
 * [INFO][SYSTEM          ][register_debug_example.cpp:175][       0 s] SystemC Register Debug Example Started
 * [INFO][TEST_START      ][register_debug_example.cpp:25 ][       0 s] Starting register configuration test...
 * [INFO][TLD_TEST        ][register_debug_example.cpp:47 ][       0 s] === Testing TLD Configuration ===
 * [INFO][TLD_CONFIG      ][register_debug_example.cpp:56 ][       0 s] Traditional printing:
 * TensorTransfer Configuration:
 *   cfg_type            = 0x0
 *   cfg_wd              = 0x6
 *   cfg_rem_dim0        = 0x78
 *   ...
 * [INFO][TLD_CONFIG      ][register_debug_example.cpp:60 ][       0 s] Using sc_logger:
 * [INFO][TLD_CONFIG      ][register_debug_example.cpp:60 ][       0 s] Configuration: TensorTransferConfig(type:0x0, wd:0x6, rem_dim0:120, size_dim0b:2048, size_dim1:3604, size_dim2:2748, stride_dim1_gmem:256, stride_dim2_gmem:48879616, stride_dim1_lmem:0, stride_dim2_lmem:0)
 * [DEBUG][TLD_CONFIG     ][register_debug_example.cpp:61 ][       0 s] Configuration: TensorTransferConfig(type:0x0, wd:0x6, rem_dim0:120, size_dim0b:2048, size_dim1:3604, size_dim2:2748, stride_dim1_gmem:256, stride_dim2_gmem:48879616, stride_dim1_lmem:0, stride_dim2_lmem:0)
 * [INFO][TLD_MULTI       ][register_debug_example.cpp:67 ][       0 s] First call with TLD_MULTI ID
 * [INFO][TLD_MULTI       ][register_debug_example.cpp:68 ][       0 s] Second call with TLD_MULTI ID
 * [INFO][TLD_MULTI       ][register_debug_example.cpp:69 ][       0 s] Third call with TLD_MULTI ID
 * ...
 * [INFO][SYSTEM          ][register_debug_example.cpp:185][     100 ns] SystemC Register Debug Example Completed
 * 
 * === SC_LOGGER ID Statistics ===
 * ID: DEFAULT - Count: 2
 * ID: SYSTEM - Count: 2
 * ID: TEST_START - Count: 1
 * ID: TLD_TEST - Count: 1
 * ID: TLD_CONFIG - Count: 4
 * ID: TLD_ALL - Count: 1
 * ID: TLD_MULTI - Count: 3
 * ID: MP_TEST - Count: 1
 * ID: MP_CONFIG - Count: 1
 * ID: MP_DEBUG - Count: 1
 * ID: MP_WARN - Count: 1
 * ID: MP_ERROR - Count: 1
 * ...
 * Total unique IDs: 25
 * ===============================
 */ 