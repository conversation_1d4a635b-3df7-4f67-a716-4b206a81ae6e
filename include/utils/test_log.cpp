#include <systemc>
#include "../include/utils/systemc_logger.h"
#include "sysc/kernel/sc_time.h"

SC_MODULE(TestModule)
{
    SystemCLogger logger{SystemCLogger::SC_INFO};

    SC_CTOR(TestModule) : logger()
    {
        // LOG_TO_CONSOLE();
        SC_THREAD(testThread);
    }

    void testThread()
    {
        int x = 5;
        while (true)
        {
            debug_sc("Debug: value = %d", x);
            info_sc("This is an info message");
            warning_sc("Warning: %s", "Low memory");

            // 使用 assert_sc（会在失败时抛出异常）
            assert_sc(x > 0, "x should be positive, but got %d", x);

            // 使用 try_sc（会捕获异常并继续执行）
            try_sc(x > 3, "x should be less than 3, but got %d", x);
            // 使用 assert_sc

            // 故意触发一个断言失败

            wait(10, sc_core::SC_NS);
        }
    }
};

int sc_main(int argc, char* argv[])
{
    TestModule testModule("testModule");
    LOG_TO_BOTH("simulation.txt");
    sc_start(100, sc_core::SC_NS);
    return 0;
}
