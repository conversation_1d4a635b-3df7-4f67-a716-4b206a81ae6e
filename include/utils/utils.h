#ifndef UTILS_H
#define UTILS_H

#include <array>
#include <cstdint>
#include "../../src/cim_cluster/inc/dcim_com.h"
#include "../ac_types-master/include/ac_fixed.h"
#include "../ac_types-master/include/ac_float.h"
#include "../ac_types-master/include/ac_std_float.h"
#include "../npu_config.h"
using Word256b = std::array<uint8_t, NPUConfig::LMEM_WD / 8>;
struct MaskInfo
{
    Word256b mask;
    bool need_mask;
    MaskInfo() : need_mask(false)
    {
        mask.fill(0xFF);
    };
};

inline uint32_t glb_f2mh_conv(float fdata, uint8_t data_fmt)
{
    switch (data_fmt)
    {
        case dtype::FP16:
        {
            ac_std_float<16, 5> val_half{fdata};
            return val_half.data();
        }

        case dtype::BF16:
        {
            ac_std_float<16, 8> val_bf16{fdata};
            return val_bf16.data();
        }
        case dtype::FP32:
        {
            ac_std_float<32, 8> val_fp32{fdata};
            return val_fp32.data();
        }
        case dtype::FP8E4:
        {
            ac_std_float<8, 4> val_fp8e4{fdata};
            return val_fp8e4.data().to_uint();
        }
        case dtype::FP8E5:
        {
            ac_std_float<8, 5> val_fp8e5{fdata};
            return val_fp8e5.data().to_uint();
        }
            //   TODO: add bbf16
            //   case dtype::DataFmt::BBF16: {
            //     ac_std_float<16, 8> val_bb16{fdata};
            //     return val_bb16.data().to_uint();
            //   }
        default:
            throw std::invalid_argument("Invalid data format");
    }
}

inline void glb_mh2f_conv(uint32_t mhdata, uint8_t data_fmt, float* fdata)
{
    switch (data_fmt)
    {
        case dtype::FP16:
        {
            ac_std_float<16, 5> val_half;
            val_half.set_data(ac_int<16, false>(mhdata));
            *fdata = val_half.to_float();
            break;
        }
        case dtype::BF16:
        {
            ac_std_float<16, 8> val_bf16;
            val_bf16.set_data(ac_int<16, false>(mhdata));
            *fdata = val_bf16.to_float();
            break;
        }
        case dtype::FP32:
        {
            ac_std_float<32, 8> val_fp32;
            val_fp32.set_data(ac_int<32, false>(mhdata));
            *fdata = val_fp32.to_float();
            break;
        }
        case dtype::FP8E4:
        {
            ac_std_float<8, 4> val_fp8e4;
            val_fp8e4.set_data(ac_int<8, true>(mhdata));
            *fdata = val_fp8e4.to_float();
        }
        case dtype::FP8E5:
        {
            ac_std_float<8, 5> val_fp8e5;
            val_fp8e5.set_data(ac_int<8, true>(mhdata));
            *fdata = val_fp8e5.to_float();
        }
        default:
            throw std::invalid_argument("Invalid data format");
    }
}

inline uint32_t width_code_to_bits(dtype::WidthCode width_code)
{
    switch (width_code)
    {
        case dtype::WidthCode::W4:
            return 4;
        case dtype::WidthCode::W8:
            return 8;
        case dtype::WidthCode::W16:
            return 16;
        case dtype::WidthCode::W32:
            return 32;
    }
}

inline dtype::WidthCode bits_to_width_code(uint8_t bit_width)
{
    switch (bit_width)
    {
        case 4:
            return dtype::WidthCode::W4;
        case 8:
            return dtype::WidthCode::W8;
        case 16:
            return dtype::WidthCode::W16;
        case 32:
            return dtype::WidthCode::W32;
    }
}

inline int32_t uint32_to_int32_prec(uint32_t data, uint8_t prec_in)
{
    // 根据精度转换为实际的有符号值
    int32_t actual_signed;
    switch (prec_in)
    {
        case dtype::INT4:
            // 4位有符号数的符号扩展
            actual_signed = ((data & 0x8) ? (data | 0xFFFFFFF0) : (data & 0x0F));
            break;
        case dtype::INT8:
            // 8位有符号数的符号扩展
            actual_signed = ((data & 0x80) ? (data | 0xFFFFFF00) : (data & 0xFF));
            break;
        case dtype::INT16:
            // 16位有符号数的符号扩展
            actual_signed = ((data & 0x8000) ? (data | 0xFFFF0000) : (data & 0xFFFF));
            break;
        case dtype::INT32:
            actual_signed = static_cast<int32_t>(data);
            break;
        default:
            actual_signed = static_cast<int32_t>(data);
    }
    return actual_signed;
}

// Convert dtype format to dcim_com format
inline uint8_t dtype_to_dcim_fmt(uint8_t dtype_fmt)
{
    switch (dtype_fmt)
    {
        case dtype::INT4:
            return INT4;
        case dtype::INT8:
            return INT8;
        case dtype::INT16:
            return INT16;
        case dtype::FP32:
            return FP32;
        case dtype::FP16:
            return FP16;
        case dtype::BF16:
            return BF16;
        case dtype::FP8E4:
            return FP8E4;
        case dtype::FP8E5:
            return FP8E5;
        default:
            throw std::invalid_argument("Unsupported data format");
    }
}

// Convert dcim_com format to dtype format
inline uint8_t dcim_fmt_to_dtype(uint8_t dcim_fmt)
{
    switch (dcim_fmt)
    {
        case INT4:
            return dtype::INT4;
        case INT8:
            return dtype::INT8;
        case INT16:
            return dtype::INT16;
        case FP32:
            return dtype::FP32;
        case FP16:
            return dtype::FP16;
        case BF16:
            return dtype::BF16;
        case FP8E4:
        case FP8E5:
            throw std::invalid_argument("FP8 formats not currently supported");
        default:
            throw std::invalid_argument("Invalid DCIM format");
    }
}

inline bool is_float_type(uint8_t type_code)
{
    return (type_code == static_cast<uint8_t>(dtype::TypeCode::FP) ||
            type_code == static_cast<uint8_t>(dtype::TypeCode::BF) ||
            type_code == static_cast<uint8_t>(dtype::TypeCode::FP2));
}

inline Word256b generate_mask(uint32_t rem_dim0, uint8_t prec)
{
    Word256b mask;
    mask.fill(0x00);  // Initialize all to zero

    uint8_t widthCode = prec & 0x07;

    // Handle special case of zero remainder
    if (rem_dim0 == 0)
    {
        return mask;  // Return all zeros
    }

    // Calculate bytes per element and valid bytes
    uint32_t bytes_per_elem;
    uint32_t valid_bytes;
    bool is_int4 = (widthCode == static_cast<uint8_t>(dtype::WidthCode::W4));

    if (is_int4)
    {
        // For INT4: 2 elements per byte
        valid_bytes = (rem_dim0 + 1) >> 1;  // Equivalent to (rem_dim0 + 1) / 2
    }
    else
    {
        // For other types: use power of 2 shift for bytes calculation
        bytes_per_elem = 1u << (widthCode - static_cast<uint8_t>(dtype::WidthCode::W8));
        valid_bytes = rem_dim0 * bytes_per_elem;
    }

    // Set mask values for complete bytes
    for (uint32_t i = 0; i < valid_bytes && i < mask.size(); i++)
    {
        mask[i] = 0xFF;
    }

    // Handle special case for last byte of INT4 with odd elements
    if (is_int4 && (rem_dim0 & 1))
    {
        mask[valid_bytes - 1] = 0x0F;  // Only lower 4 bits valid
    }

    return mask;
}

inline MaskInfo calculate_mask_info(uint32_t dim0b,
                                    uint32_t cfg_size_dim0b,
                                    uint32_t cfg_rem_dim0,
                                    uint8_t m_prec)
{
    MaskInfo info;
    // 只在dim0的最后一块且有余数时需要掩码
    if (dim0b == cfg_size_dim0b - 1 && cfg_rem_dim0 != 0)
    {
        info.need_mask = true;
        info.mask = generate_mask(cfg_rem_dim0, m_prec);
    }
    return info;
}

inline uint32_t ceil_div(uint32_t x, uint32_t y)
{
    return (x + y - 1) / y;
}

#endif  // UTILS_H
