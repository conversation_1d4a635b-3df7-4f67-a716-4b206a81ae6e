#ifndef SYSTEMC_LOGGER_H
#define SYSTEMC_LOGGER_H

#include <chrono>
#include <cstdarg>
#include <ctime>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <sstream>
#include <stdexcept>
#include <string>
#include <systemc>

#define RESET "\033[0m"
#define RED "\033[31m"
#define GRE<PERSON> "\033[32m"
#define YELLOW "\033[33m"
#define BLUE "\033[34m"
#define WHITE "\033[37m"

class SystemCLogger
{
  public:
    enum LogLevel
    {
        SC_DEBUG,
        SC_INFO,
        SC_WARNING,
        SC_ERROR
    };

    SystemCLogger(LogLevel level = SC_INFO);
    ~SystemCLogger();

    void enableConsoleOutput(bool enable);
    void enableFileOutput(bool enable);
    void setLogLevel(LogLevel level);
    void setLogFile(const std::string& filename);
    void setLogToConsole();
    void closeLogFile();
    void log(LogLevel level, const char* file, int line, const char* func, const char* format, ...);

  private:
    LogLevel m_logLevel;
    std::ofstream m_logFile;
    bool m_useFile;
    bool m_useConsole;

    std::string getCurrentTime();
    std::string getLevelString(LogLevel level);
    std::string getLevelColor(LogLevel level);
    void writeLog(LogLevel level,
                  const std::string& file,
                  int line,
                  const std::string& func,
                  const std::string& message);
};

// 全局日志对象
extern SystemCLogger g_logger;

// 日志宏定义
#ifdef ENABLE_LOGGING
    #define debug_sc(...)                                                                          \
        g_logger.log(SystemCLogger::SC_DEBUG, __FILE__, __LINE__, __FUNCTION__, __VA_ARGS__)
    #define info_sc(...)                                                                           \
        g_logger.log(SystemCLogger::SC_INFO, __FILE__, __LINE__, __FUNCTION__, __VA_ARGS__)
    #define warning_sc(...)                                                                        \
        g_logger.log(SystemCLogger::SC_WARNING, __FILE__, __LINE__, __FUNCTION__, __VA_ARGS__)
    #define error_sc(...)                                                                          \
        do                                                                                         \
        {                                                                                          \
            g_logger.log(SystemCLogger::SC_ERROR, __FILE__, __LINE__, __FUNCTION__, __VA_ARGS__);  \
            throw std::runtime_error("Error occurred");                                            \
        } while (0)

    #define assert_sc(cond, ...)                                                                   \
        do                                                                                         \
        {                                                                                          \
            if (!(cond))                                                                           \
            {                                                                                      \
                error_sc("Assertion failed: " #cond ". " __VA_ARGS__);                             \
            }                                                                                      \
        } while (0)

    #define try_sc(cond, ...)                                                                      \
        do                                                                                         \
        {                                                                                          \
            try                                                                                    \
            {                                                                                      \
                assert_sc(cond, __VA_ARGS__);                                                      \
            }                                                                                      \
            catch (const std::runtime_error& e)                                                    \
            {                                                                                      \
                std::cerr << "Caught assertion failure: " << e.what() << std::endl;                \
            }                                                                                      \
        } while (0)

#else
    #define debug_sc(...)
    #define info_sc(...)
    #define warning_sc(...)
    #define error_sc(...)
    #define assert_sc(cond, ...)
    #define try_sc(cond, ...)
#endif

#define LOG_TO_FILE(filename)                                                                      \
    g_logger.setLogFile(filename);                                                                 \
    g_logger.enableFileOutput(true)
#define LOG_TO_CONSOLE() g_logger.enableConsoleOutput(true)
#define LOG_TO_BOTH(filename)                                                                      \
    g_logger.setLogFile(filename);                                                                 \
    g_logger.enableFileOutput(true);                                                               \
    g_logger.enableConsoleOutput(true)

#endif  // SYSTEMC_LOGGER_H