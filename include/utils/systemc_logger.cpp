/**
 * @file systemc_logger.cpp
 * @brief Implementation of the SystemC Logger class
 * <AUTHOR> jiaxing
 * @date 2024-09-14
 */

#include "systemc_logger.h"
#include <chrono>
#include <sstream>

/** Global logger instance */
SystemCLogger g_logger;

/**
 * @brief Constructs a SystemCLogger object
 * @param level Initial log level
 */
SystemCLogger::SystemCLogger(LogLevel level)
    : m_logLevel(level), m_useFile(false), m_useConsole(true)
{
}

/**
 * @brief Destructor for SystemCLogger
 *
 * Closes the log file if it's open
 */
SystemCLogger::~SystemCLogger()
{
    if (m_logFile.is_open())
    {
        m_logFile.close();
    }
}

/**
 * @brief Sets the log level
 * @param level New log level
 */
void SystemCLogger::setLogLevel(LogLevel level)
{
    m_logLevel = level;
}

/**
 * @brief Enables or disables console output
 * @param enable True to enable console output, false to disable
 */
void SystemCLogger::enableConsoleOutput(bool enable)
{
    m_useConsole = enable;
}

/**
 * @brief Enables or disables file output
 * @param enable True to enable file output, false to disable
 */
void SystemCLogger::enableFileOutput(bool enable)
{
    m_useFile = enable;
}

/**
 * @brief Sets the log file
 * @param filename Name of the log file
 *
 * Opens the specified file in truncate mode, overwriting any existing content
 */
void SystemCLogger::setLogFile(const std::string& filename)
{
    if (m_logFile.is_open())
    {
        m_logFile.close();
    }

    // Use std::ios::trunc to overwrite existing file content
    m_logFile.open(filename, std::ios::out | std::ios::trunc);

    if (!m_logFile.is_open())
    {
        std::error_code ec(errno, std::system_category());
        std::cerr << "Failed to open log file '" << filename << "': " << ec.message() << std::endl;
        m_useFile = false;
    }
    else
    {
        std::cout << "Log file opened (overwrite mode): " << filename << std::endl;
        m_useFile = true;
    }
}

/**
 * @brief Closes the log file and resets output settings
 */
void SystemCLogger::closeLogFile()
{
    if (m_logFile.is_open())
    {
        m_logFile.close();
    }
    m_useFile = false;
    m_useConsole = true;
}

/**
 * @brief Sets logging to console only
 *
 * Closes any open log file and enables console output
 */
void SystemCLogger::setLogToConsole()
{
    if (m_logFile.is_open())
    {
        m_logFile.close();
    }
    m_useConsole = true;
}

/**
 * @brief Gets the current time as a formatted string
 * @return String representation of the current time
 */
std::string SystemCLogger::getCurrentTime()
{
    auto now = std::chrono::system_clock::now();
    auto now_c = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&now_c), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

/**
 * @brief Converts LogLevel to its string representation
 * @param level Log level
 * @return String representation of the log level
 */
std::string SystemCLogger::getLevelString(LogLevel level)
{
    switch (level)
    {
        case SC_DEBUG:
            return "DEBUG";
        case SC_INFO:
            return "INFO";
        case SC_WARNING:
            return "WARNING";
        case SC_ERROR:
            return "ERROR";
        default:
            return "UNKNOWN";
    }
}

/**
 * @brief Gets the color code for a given log level
 * @param level Log level
 * @return ANSI color code for the log level
 */
std::string SystemCLogger::getLevelColor(LogLevel level)
{
    switch (level)
    {
        case SC_DEBUG:
            return BLUE;
        case SC_INFO:
            return GREEN;
        case SC_WARNING:
            return YELLOW;
        case SC_ERROR:
            return RED;
        default:
            return WHITE;
    }
}

/**
 * @brief Logs a message with the specified level and details
 * @param level Log level
 * @param file Source file name
 * @param line Line number in the source file
 * @param func Function name
 * @param format Format string for the log message
 * @param ... Variable arguments for the format string
 */
void SystemCLogger::log(LogLevel level,
                        const char* file,
                        int line,
                        const char* func,
                        const char* format,
                        ...)
{
    if (level >= m_logLevel)
    {
        va_list args;
        va_start(args, format);

        char buffer[512];
        vsnprintf(buffer, sizeof(buffer), format, args);

        va_end(args);

        writeLog(level, file, line, func, buffer);
    }
}

/**
 * @brief Writes the log message to console and/or file
 * @param level Log level
 * @param file Source file name
 * @param line Line number in the source file
 * @param func Function name
 * @param message Formatted log message
 */
void SystemCLogger::writeLog(LogLevel level,
                             const std::string& file,
                             int line,
                             const std::string& func,
                             const std::string& message)
{
    std::stringstream ss;
    std::string levelStr = getLevelString(level);
    std::string timeStr = sc_core::sc_time_stamp().to_string();

    if (m_useConsole)
    {
        std::string levelColor = getLevelColor(level);
        std::cout << "[" << levelColor << std::setw(7) << std::left << levelStr << RESET << "] "
                  << "[" << std::setw(10) << std::left << timeStr << "] " << message;

        if (level == SC_ERROR)
        {
            std::cout << " (in " << file << ":" << func << ":" << line << ")";
        }
        std::cout << std::endl;
    }

    if (m_useFile && m_logFile.is_open())
    {
        ss << "[" << std::setw(7) << std::left << levelStr << "] "
           << "[" << std::setw(10) << std::left << timeStr << "] " << message;

        if (level == SC_ERROR)
        {
            ss << " (in " << file << ":" << func << ":" << line << ")";
        }

        m_logFile << ss.str() << std::endl;
        m_logFile.flush();
    }
}