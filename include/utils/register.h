#ifndef __REGISTER_H__
#define __REGISTER_H__

#include <array>
#include <cstdint>
#include <iostream>
#include <map>
#include <type_traits>
#include <systemc>

namespace hardware_instruction
{

// 基础字段定义
struct FieldDef
{
    uint32_t addr;
    uint32_t left;
    uint32_t right;
    const char* name;
};

// 配置类型枚举
enum class ConfigType
{
    TLD,    // Tensor Load
    TST,    // Tensor Store
    TM,     // Tensor Manipulation
    MP,     // Matrix Processing
    VP,     // Vector Processing
    CIM_TP  // CIM Tensor Processing (GEMV/GEMM)
};

// 基础寄存器管理类
class RegisterManagerBase
{
  protected:
    std::map<uint32_t, sc_dt::sc_uint<32>> registers;

    uint32_t readField(const FieldDef& field)
    {
        if (registers.find(field.addr) == registers.end())
        {
            registers[field.addr] = 0;
        }
        return (registers[field.addr].range(field.left, field.right)).to_uint();
    }

    bool writeField(const FieldDef& field, uint32_t value)
    {
        try
        {
            if (registers.find(field.addr) == registers.end())
            {
                registers[field.addr] = 0;
            }

            uint32_t field_mask = ((1u << (field.left - field.right + 1)) - 1) << field.right;

            registers[field.addr] &= ~field_mask;
            registers[field.addr] |= (value << field.right) & field_mask;

            return true;
        }
        catch (const std::exception& e)
        {
            std::cerr << "Error writing field " << field.name << ": " << e.what() << std::endl;
            return false;
        }
    }

  public:
    virtual ~RegisterManagerBase() = default;

    bool writeReg(uint32_t addr, uint32_t value)
    {
        try
        {
            registers[addr] = value;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cerr << "Error writing register at address " << addr << ": " << e.what()
                      << std::endl;
            return false;
        }
    }

    uint32_t readReg(uint32_t addr) const
    {
        try
        {
            auto it = registers.find(addr);
            if (it != registers.end())
            {
                return it->second.to_uint();
            }
            return 0;
        }
        catch (const std::exception& e)
        {
            std::cerr << "Error reading register at address " << addr << ": " << e.what()
                      << std::endl;
            return 0;
        }
    }

    void reset()
    {
        registers.clear();
    }

    void printAllRegs() const
    {
        std::cout << "Register Values:" << std::endl;
        for (std::map<uint32_t, sc_dt::sc_uint<32>>::const_iterator it = registers.begin();
             it != registers.end(); ++it)
        {
            std::cout << "Address 0x" << std::hex << it->first << ": 0x" << it->second.to_uint() << std::dec
                      << std::endl;
        }
    }
};

// TLD/TST配置结构
struct TensorTransferConfig
{
    uint32_t cfg_type;
    uint32_t cfg_wd;
    uint32_t cfg_rem_dim0;
    uint32_t cfg_size_dim0b;
    uint32_t cfg_size_dim1;
    uint32_t cfg_size_dim2;
    uint32_t cfg_stride_dim1_gmem;
    uint32_t cfg_stride_dim2_gmem;
    uint32_t cfg_stride_dim1_lmem;
    uint32_t cfg_stride_dim2_lmem;

    void print() const
    {
        std::cout << "TensorTransfer Configuration:" << std::endl;
        std::cout << std::hex << std::showbase;
        std::cout << "  cfg_type            = " << cfg_type << std::endl;
        std::cout << "  cfg_wd              = " << cfg_wd << std::endl;
        std::cout << "  cfg_rem_dim0        = " << cfg_rem_dim0 << std::endl;
        std::cout << "  cfg_size_dim0b      = " << cfg_size_dim0b << std::endl;
        std::cout << "  cfg_size_dim1       = " << cfg_size_dim1 << std::endl;
        std::cout << "  cfg_size_dim2       = " << cfg_size_dim2 << std::endl;
        std::cout << "  cfg_stride_dim1_gmem = " << cfg_stride_dim1_gmem << std::endl;
        std::cout << "  cfg_stride_dim2_gmem = " << cfg_stride_dim2_gmem << std::endl;
        std::cout << "  cfg_stride_dim1_lmem = " << cfg_stride_dim1_lmem << std::endl;
        std::cout << "  cfg_stride_dim2_lmem = " << cfg_stride_dim2_lmem << std::endl;
        std::cout << std::dec << std::noshowbase;
    }
};

// TM配置结构
struct TensorManipConfig
{
    uint32_t cfg_type;
    uint32_t cfg_wd;
    uint32_t cfg_rem_dim0;
    uint32_t cfg_size_dim0b;
    uint32_t cfg_size_dim1;
    uint32_t cfg_size_dim2;
    uint32_t cfg_stride_dim1_out;
    uint32_t cfg_stride_dim2_out;
    uint32_t cfg_stride_dim1_in;
    uint32_t cfg_stride_dim2_in;

    void print() const
    {
        std::cout << "TensorManip Configuration:" << std::endl;
        std::cout << std::hex << std::showbase;
        std::cout << "  cfg_type            = " << cfg_type << std::endl;
        std::cout << "  cfg_wd              = " << cfg_wd << std::endl;
        std::cout << "  cfg_rem_dim0        = " << cfg_rem_dim0 << std::endl;
        std::cout << "  cfg_size_dim0b      = " << cfg_size_dim0b << std::endl;
        std::cout << "  cfg_size_dim1       = " << cfg_size_dim1 << std::endl;
        std::cout << "  cfg_size_dim2       = " << cfg_size_dim2 << std::endl;
        std::cout << "  cfg_stride_dim1_out = " << cfg_stride_dim1_out << std::endl;
        std::cout << "  cfg_stride_dim2_out = " << cfg_stride_dim2_out << std::endl;
        std::cout << "  cfg_stride_dim1_in  = " << cfg_stride_dim1_in << std::endl;
        std::cout << "  cfg_stride_dim2_in  = " << cfg_stride_dim2_in << std::endl;
        std::cout << std::dec << std::noshowbase;
    }
};

// MP配置结构
struct MatrixProcessConfig
{
    // Precision
    uint32_t cfg_type_out;
    uint32_t cfg_type_orig;
    uint32_t cfg_type_in;
    uint32_t cfg_type_wt;
    uint32_t cfg_wd_ref;
    uint32_t cfg_wd_sc_out;
    uint32_t cfg_wd_sc_orig;
    uint32_t cfg_wd_sc_in;
    uint32_t cfg_wd_sc_wt;

    // Operation
    uint32_t cfg_accu;
    uint32_t cfg_act;
    uint32_t cfg_shift;

    // Tensor O
    uint32_t cfg_rem_dim0_out;
    uint32_t cfg_size_dim0b_out;
    uint32_t cfg_size_dim1_out;
    uint32_t cfg_size_dim2_out;
    uint32_t cfg_stride_dim1_out;
    uint32_t cfg_stride_dim2_out;

    // Tensor I
    uint32_t cfg_rem_dim0_in;
    uint32_t cfg_size_dim0b_in;
    uint32_t cfg_size_dim1_in;
    uint32_t cfg_size_dim2_in;
    uint32_t cfg_stride_dim1_in;
    uint32_t cfg_stride_dim2_in;

    // Kernel
    uint32_t cfg_k_x;
    uint32_t cfg_k_y;
    uint32_t cfg_slide_x;
    uint32_t cfg_slide_y;
    uint32_t cfg_dl_x;
    uint32_t cfg_dl_y;
    uint32_t cfg_log2trs_x;
    uint32_t cfg_log2trs_y;

    // Padding
    uint32_t cfg_pad_w;
    uint32_t cfg_pad_n;
    uint32_t cfg_pad_val;

    void print() const
    {
        std::cout << "MatrixProcess Configuration:" << std::endl;
        std::cout << std::hex << std::showbase;
        // Precision
        std::cout << "Precision:" << std::endl;
        std::cout << "  cfg_type_out        = " << cfg_type_out << std::endl;
        std::cout << "  cfg_type_orig       = " << cfg_type_orig << std::endl;
        std::cout << "  cfg_type_in         = " << cfg_type_in << std::endl;
        std::cout << "  cfg_type_wt         = " << cfg_type_wt << std::endl;
        std::cout << "  cfg_wd_ref          = " << cfg_wd_ref << std::endl;
        std::cout << "  cfg_wd_sc_out       = " << cfg_wd_sc_out << std::endl;
        std::cout << "  cfg_wd_sc_orig      = " << cfg_wd_sc_orig << std::endl;
        std::cout << "  cfg_wd_sc_in        = " << cfg_wd_sc_in << std::endl;
        std::cout << "  cfg_wd_sc_wt        = " << cfg_wd_sc_wt << std::endl;

        // Operation
        std::cout << "Operation:" << std::endl;
        std::cout << "  cfg_accu            = " << cfg_accu << std::endl;
        std::cout << "  cfg_act             = " << cfg_act << std::endl;
        std::cout << "  cfg_shift           = " << cfg_shift << std::endl;

        // Tensor O
        std::cout << "Tensor O:" << std::endl;
        std::cout << "  cfg_rem_dim0_out    = " << cfg_rem_dim0_out << std::endl;
        std::cout << "  cfg_size_dim0b_out  = " << cfg_size_dim0b_out << std::endl;
        std::cout << "  cfg_size_dim1_out   = " << cfg_size_dim1_out << std::endl;
        std::cout << "  cfg_size_dim2_out   = " << cfg_size_dim2_out << std::endl;
        std::cout << "  cfg_stride_dim1_out = " << cfg_stride_dim1_out << std::endl;
        std::cout << "  cfg_stride_dim2_out = " << cfg_stride_dim2_out << std::endl;

        // Tensor I
        std::cout << "Tensor I:" << std::endl;
        std::cout << "  cfg_rem_dim0_in     = " << cfg_rem_dim0_in << std::endl;
        std::cout << "  cfg_size_dim0b_in   = " << cfg_size_dim0b_in << std::endl;
        std::cout << "  cfg_size_dim1_in    = " << cfg_size_dim1_in << std::endl;
        std::cout << "  cfg_size_dim2_in    = " << cfg_size_dim2_in << std::endl;
        std::cout << "  cfg_stride_dim1_in  = " << cfg_stride_dim1_in << std::endl;
        std::cout << "  cfg_stride_dim2_in  = " << cfg_stride_dim2_in << std::endl;

        // Kernel
        std::cout << "Kernel:" << std::endl;
        std::cout << "  cfg_k_x             = " << cfg_k_x << std::endl;
        std::cout << "  cfg_k_y             = " << cfg_k_y << std::endl;
        std::cout << "  cfg_slide_x         = " << cfg_slide_x << std::endl;
        std::cout << "  cfg_slide_y         = " << cfg_slide_y << std::endl;
        std::cout << "  cfg_dl_x            = " << cfg_dl_x << std::endl;
        std::cout << "  cfg_dl_y            = " << cfg_dl_y << std::endl;
        std::cout << "  cfg_log2trs_x       = " << cfg_log2trs_x << std::endl;
        std::cout << "  cfg_log2trs_y       = " << cfg_log2trs_y << std::endl;

        // Padding
        std::cout << "Padding:" << std::endl;
        std::cout << "  cfg_pad_w           = " << cfg_pad_w << std::endl;
        std::cout << "  cfg_pad_n           = " << cfg_pad_n << std::endl;
        std::cout << "  cfg_pad_val         = " << cfg_pad_val << std::endl;
        std::cout << std::dec << std::noshowbase;
    }
};

// VP配置结构
struct VectorProcessConfig
{
    // Precision
    uint32_t cfg_type_out;
    uint32_t cfg_type_in1;
    uint32_t cfg_type_in2;
    uint32_t cfg_wd_ref;
    uint32_t cfg_wd_sc_out;
    uint32_t cfg_wd_sc_in1;
    uint32_t cfg_wd_sc_in2;

    // Operation
    uint32_t cfg_op;

    // Size
    uint32_t cfg_rem_dim0_ref;
    uint32_t cfg_size_dim0b_ref;

    void print() const
    {
        std::cout << "VectorProcess Configuration:" << std::endl;
        std::cout << std::hex << std::showbase;
        // Precision
        std::cout << "Precision:" << std::endl;
        std::cout << "  cfg_type_out        = " << cfg_type_out << std::endl;
        std::cout << "  cfg_type_in1        = " << cfg_type_in1 << std::endl;
        std::cout << "  cfg_type_in2        = " << cfg_type_in2 << std::endl;
        std::cout << "  cfg_wd_ref          = " << cfg_wd_ref << std::endl;
        std::cout << "  cfg_wd_sc_out       = " << cfg_wd_sc_out << std::endl;
        std::cout << "  cfg_wd_sc_in1       = " << cfg_wd_sc_in1 << std::endl;
        std::cout << "  cfg_wd_sc_in2       = " << cfg_wd_sc_in2 << std::endl;

        // Operation
        std::cout << "Operation:" << std::endl;
        std::cout << "  cfg_op              = " << cfg_op << std::endl;

        // Size
        std::cout << "Size:" << std::endl;
        std::cout << "  cfg_rem_dim0_ref    = " << cfg_rem_dim0_ref << std::endl;
        std::cout << "  cfg_size_dim0b_ref  = " << cfg_size_dim0b_ref << std::endl;
        std::cout << std::dec << std::noshowbase;
    }
};

// CIM_TP配置结构 (GEMV/GEMM)
struct CimTensorProcessConfig
{
    // Precision
    uint32_t cfg_type_out;
    uint32_t cfg_type_orig;
    uint32_t cfg_type_in;
    uint32_t cfg_type_wt;
    uint32_t cfg_wd_out;
    uint32_t cfg_wd_orig;
    uint32_t cfg_wd_in;
    uint32_t cfg_wd_wt;

    // Operation
    uint32_t cfg_accu;
    uint32_t cfg_act;
    uint32_t cfg_shift;

    // Tensor O
    uint32_t cfg_rem_dim0_out;
    uint32_t cfg_size_dim0b_out;
    uint32_t cfg_size_dim1_out;
    uint32_t cfg_stride_dim1_out;

    // Tensor I
    uint32_t cfg_rem_dim0_in;
    uint32_t cfg_size_dim0b_in;
    uint32_t cfg_size_dim1_in;
    uint32_t cfg_stride_dim1_in;

    void print() const
    {
        std::cout << "CIM Tensor Process Configuration:" << std::endl;
        std::cout << std::hex << std::showbase;
        // Precision
        std::cout << "Precision:" << std::endl;
        std::cout << "  cfg_type_out        = " << cfg_type_out << std::endl;
        std::cout << "  cfg_type_orig       = " << cfg_type_orig << std::endl;
        std::cout << "  cfg_type_in         = " << cfg_type_in << std::endl;
        std::cout << "  cfg_type_wt         = " << cfg_type_wt << std::endl;
        std::cout << "  cfg_wd_out          = " << cfg_wd_out << std::endl;
        std::cout << "  cfg_wd_orig         = " << cfg_wd_orig << std::endl;
        std::cout << "  cfg_wd_in           = " << cfg_wd_in << std::endl;
        std::cout << "  cfg_wd_wt           = " << cfg_wd_wt << std::endl;

        // Operation
        std::cout << "Operation:" << std::endl;
        std::cout << "  cfg_accu            = " << cfg_accu << std::endl;
        std::cout << "  cfg_act             = " << cfg_act << std::endl;
        std::cout << "  cfg_shift           = " << cfg_shift << std::endl;

        // Tensor O
        std::cout << "Tensor O:" << std::endl;
        std::cout << "  cfg_rem_dim0_out    = " << cfg_rem_dim0_out << std::endl;
        std::cout << "  cfg_size_dim0b_out  = " << cfg_size_dim0b_out << std::endl;
        std::cout << "  cfg_size_dim1_out   = " << cfg_size_dim1_out << std::endl;
        std::cout << "  cfg_stride_dim1_out = " << cfg_stride_dim1_out << std::endl;

        // Tensor I
        std::cout << "Tensor I:" << std::endl;
        std::cout << "  cfg_rem_dim0_in     = " << cfg_rem_dim0_in << std::endl;
        std::cout << "  cfg_size_dim0b_in   = " << cfg_size_dim0b_in << std::endl;
        std::cout << "  cfg_size_dim1_in    = " << cfg_size_dim1_in << std::endl;
        std::cout << "  cfg_stride_dim1_in  = " << cfg_stride_dim1_in << std::endl;
        std::cout << std::dec << std::noshowbase;
    }
};

// 配置特征模板
template <ConfigType Type>
struct ConfigTraits;

// TLD配置特征特化
template <>
struct ConfigTraits<ConfigType::TLD>
{
    static constexpr std::array<FieldDef, 10> getFields()
    {
        return {{{0, 1, 0, "cfg_type"},
                 {0, 4, 2, "cfg_wd"},
                 {1, 5, 0, "cfg_rem_dim0"},
                 {1, 16, 6, "cfg_size_dim0b"},
                 {2, 12, 0, "cfg_size_dim1"},
                 {2, 25, 13, "cfg_size_dim2"},
                 {3, 12, 0, "cfg_stride_dim1_gmem"},
                 {4, 24, 0, "cfg_stride_dim2_gmem"},
                 {5, 12, 0, "cfg_stride_dim1_lmem"},
                 {6, 24, 0, "cfg_stride_dim2_lmem"}}};
    }
    using Config = TensorTransferConfig;
};

// TST配置特征特化
template <>
struct ConfigTraits<ConfigType::TST>
{
    static constexpr std::array<FieldDef, 10> getFields()
    {
        return {{{0, 1, 0, "cfg_type"},
                 {0, 4, 2, "cfg_wd"},
                 {1, 5, 0, "cfg_rem_dim0"},
                 {1, 16, 6, "cfg_size_dim0b"},
                 {2, 12, 0, "cfg_size_dim1"},
                 {2, 25, 13, "cfg_size_dim2"},
                 {3, 12, 0, "cfg_stride_dim1_gmem"},
                 {4, 24, 0, "cfg_stride_dim2_gmem"},
                 {5, 12, 0, "cfg_stride_dim1_lmem"},
                 {6, 24, 0, "cfg_stride_dim2_lmem"}}};
    }
    using Config = TensorTransferConfig;
};

// TM配置特征特化
template <>
struct ConfigTraits<ConfigType::TM>
{
    static constexpr std::array<FieldDef, 10> getFields()
    {
        return {{{0, 1, 0, "cfg_type"},
                 {0, 4, 2, "cfg_wd"},
                 {1, 5, 0, "cfg_rem_dim0"},
                 {1, 16, 6, "cfg_size_dim0b"},
                 {2, 12, 0, "cfg_size_dim1"},
                 {2, 25, 13, "cfg_size_dim2"},
                 {3, 12, 0, "cfg_stride_dim1_out"},
                 {4, 24, 0, "cfg_stride_dim2_out"},
                 {5, 12, 0, "cfg_stride_dim1_in"},
                 {6, 24, 0, "cfg_stride_dim2_in"}}};
    }
    using Config = TensorManipConfig;
};

// MP配置特征特化
template <>
struct ConfigTraits<ConfigType::MP>
{
    static constexpr std::array<FieldDef, 35> getFields()
    {
        return {{// Precision
                 {0, 1, 0, "cfg_type_out"},
                 {0, 3, 2, "cfg_type_orig"},
                 {0, 5, 4, "cfg_type_in"},
                 {0, 7, 6, "cfg_type_wt"},
                 {0, 10, 8, "cfg_wd_ref"},
                 {0, 12, 11, "cfg_wd_sc_out"},
                 {0, 14, 13, "cfg_wd_sc_orig"},
                 {0, 16, 15, "cfg_wd_sc_in"},
                 {0, 18, 17, "cfg_wd_sc_wt"},

                 // Operation
                 {0, 19, 19, "cfg_accu"},
                 {0, 20, 20, "cfg_act"},
                 {0, 25, 21, "cfg_shift"},

                 // Tensor O
                 {1, 5, 0, "cfg_rem_dim0_out"},
                 {1, 16, 6, "cfg_size_dim0b_out"},
                 {2, 12, 0, "cfg_size_dim1_out"},
                 {2, 25, 13, "cfg_size_dim2_out"},
                 {3, 12, 0, "cfg_stride_dim1_out"},
                 {4, 24, 0, "cfg_stride_dim2_out"},

                 // Tensor I
                 {5, 5, 0, "cfg_rem_dim0_in"},
                 {5, 16, 6, "cfg_size_dim0b_in"},
                 {6, 12, 0, "cfg_size_dim1_in"},
                 {6, 25, 13, "cfg_size_dim2_in"},
                 {7, 12, 0, "cfg_stride_dim1_in"},
                 {8, 24, 0, "cfg_stride_dim2_in"},

                 // Kernel
                 {9, 4, 0, "cfg_k_x"},
                 {9, 9, 5, "cfg_k_y"},
                 {9, 14, 10, "cfg_slide_x"},
                 {9, 19, 15, "cfg_slide_y"},
                 {9, 24, 20, "cfg_dl_x"},
                 {9, 29, 25, "cfg_dl_y"},
                 {10, 2, 0, "cfg_log2trs_x"},
                 {10, 5, 3, "cfg_log2trs_y"},

                 // Padding
                 {10, 9, 6, "cfg_pad_w"},
                 {10, 13, 10, "cfg_pad_n"},
                 {10, 29, 14, "cfg_pad_val"}}};
    }
    using Config = MatrixProcessConfig;
};

// VP配置特征特化
template <>
struct ConfigTraits<ConfigType::VP>
{
    static constexpr std::array<FieldDef, 10> getFields()
    {
        return {{// Precision
                 {0, 1, 0, "cfg_type_out"},
                 {0, 3, 2, "cfg_type_in1"},
                 {0, 5, 4, "cfg_type_in2"},
                 {0, 8, 6, "cfg_wd_ref"},
                 {0, 9, 9, "cfg_wd_sc_out"},
                 {0, 10, 10, "cfg_wd_sc_in1"},
                 {0, 11, 11, "cfg_wd_sc_in2"},

                 // Operation
                 {0, 17, 12, "cfg_op"},

                 // Size
                 {1, 5, 0, "cfg_rem_dim0_ref"},
                 {1, 16, 6, "cfg_size_dim0b_ref"}}};
    }
    using Config = VectorProcessConfig;
};

// CIM_TP配置特征特化
template <>
struct ConfigTraits<ConfigType::CIM_TP>
{
    static constexpr std::array<FieldDef, 19> getFields()
    {
        return {{// Precision
                 {0, 1, 0, "cfg_type_out"},
                 {0, 3, 2, "cfg_type_orig"},
                 {0, 5, 4, "cfg_type_in"},
                 {0, 7, 6, "cfg_type_wt"},
                 {0, 10, 8, "cfg_wd_out"},
                 {0, 13, 11, "cfg_wd_orig"},
                 {0, 16, 14, "cfg_wd_in"},
                 {0, 19, 17, "cfg_wd_wt"},

                 // Operation
                 {0, 20, 20, "cfg_accu"},
                 {0, 21, 21, "cfg_act"},
                 {0, 26, 22, "cfg_shift"},

                 // Tensor O
                 {1, 5, 0, "cfg_rem_dim0_out"},
                 {1, 16, 6, "cfg_size_dim0b_out"},
                 {2, 12, 0, "cfg_size_dim1_out"},
                 {3, 12, 0, "cfg_stride_dim1_out"},
                 // Tensor I
                 {5, 5, 0, "cfg_rem_dim0_in"},
                 {5, 16, 6, "cfg_size_dim0b_in"},
                 {6, 12, 0, "cfg_size_dim1_in"},
                 {7, 12, 0, "cfg_stride_dim1_in"}}};
    }
    using Config = CimTensorProcessConfig;
};

// 具体的配置管理类
template <ConfigType Type>
class ConfigManager : public RegisterManagerBase
{
  private:
    using Traits = ConfigTraits<Type>;

  public:
    typename Traits::Config getConfig()
    {
        typename Traits::Config cfg{};  // Zero-initialize all fields
        auto fields = Traits::getFields();
        for (const auto& field : fields)
        {
            uint32_t value = readField(field);
            setConfigField(cfg, field.name, value);
        }
        return cfg;
    }

    typename Traits::Config getConfig() const
    {
        typename Traits::Config cfg{};  // Zero-initialize all fields
        auto fields = Traits::getFields();
        for (const auto& field : fields)
        {
            uint32_t value = const_cast<ConfigManager*>(this)->readField(field);
            const_cast<ConfigManager*>(this)->setConfigField(cfg, field.name, value);
        }
        return cfg;
    }

    bool setConfig(const typename Traits::Config& cfg)
    {
        auto fields = Traits::getFields();
        for (const auto& field : fields)
        {
            uint32_t value = getConfigField(cfg, field.name);
            if (!writeField(field, value))
            {
                return false;
            }
        }
        return true;
    }

    // 打印当前配置
    void printConfig() const
    {
        typename Traits::Config cfg = getConfig();
        cfg.print();
    }

    // 打印所有寄存器和配置
    void printAll() const
    {
        std::cout << "\n=== Register Values ===" << std::endl;
        printAllRegs();
        std::cout << "\n=== Configuration Values ===" << std::endl;
        printConfig();
    }

    // 使用 sc_logger 进行日志输出的配置打印方法（需要在使用前包含 register_formatters.h）
    void logConfig() const;

    // 使用 sc_logger 进行日志输出的调试配置打印方法（需要在使用前包含 register_formatters.h）
    void debugConfig() const;

    // 使用 sc_logger 进行日志输出的所有寄存器和配置打印（需要在使用前包含 register_formatters.h）
    void logAll() const;

    // 使用 sc_logger 进行日志输出的调试版本（需要在使用前包含 register_formatters.h）
    void debugAll() const;

  private:
    // 设置配置字段值
    void setConfigField(typename Traits::Config& cfg, const char* name, uint32_t value)
    {
        if (std::is_same<typename Traits::Config, TensorTransferConfig>::value)
        {
            TensorTransferConfig* ttConfig = reinterpret_cast<TensorTransferConfig*>(&cfg);
            if (strcmp(name, "cfg_type") == 0)
                ttConfig->cfg_type = value;
            else if (strcmp(name, "cfg_wd") == 0)
                ttConfig->cfg_wd = value;
            else if (strcmp(name, "cfg_rem_dim0") == 0)
                ttConfig->cfg_rem_dim0 = value;
            else if (strcmp(name, "cfg_size_dim0b") == 0)
                ttConfig->cfg_size_dim0b = value;
            else if (strcmp(name, "cfg_size_dim1") == 0)
                ttConfig->cfg_size_dim1 = value;
            else if (strcmp(name, "cfg_size_dim2") == 0)
                ttConfig->cfg_size_dim2 = value;
            else if (strcmp(name, "cfg_stride_dim1_gmem") == 0)
                ttConfig->cfg_stride_dim1_gmem = value;
            else if (strcmp(name, "cfg_stride_dim2_gmem") == 0)
                ttConfig->cfg_stride_dim2_gmem = value;
            else if (strcmp(name, "cfg_stride_dim1_lmem") == 0)
                ttConfig->cfg_stride_dim1_lmem = value;
            else if (strcmp(name, "cfg_stride_dim2_lmem") == 0)
                ttConfig->cfg_stride_dim2_lmem = value;
        }
        else if (std::is_same<typename Traits::Config, TensorManipConfig>::value)
        {
            TensorManipConfig* tmConfig = reinterpret_cast<TensorManipConfig*>(&cfg);
            if (strcmp(name, "cfg_type") == 0)
                tmConfig->cfg_type = value;
            else if (strcmp(name, "cfg_wd") == 0)
                tmConfig->cfg_wd = value;
            else if (strcmp(name, "cfg_rem_dim0") == 0)
                tmConfig->cfg_rem_dim0 = value;
            else if (strcmp(name, "cfg_size_dim0b") == 0)
                tmConfig->cfg_size_dim0b = value;
            else if (strcmp(name, "cfg_size_dim1") == 0)
                tmConfig->cfg_size_dim1 = value;
            else if (strcmp(name, "cfg_size_dim2") == 0)
                tmConfig->cfg_size_dim2 = value;
            else if (strcmp(name, "cfg_stride_dim1_out") == 0)
                tmConfig->cfg_stride_dim1_out = value;
            else if (strcmp(name, "cfg_stride_dim2_out") == 0)
                tmConfig->cfg_stride_dim2_out = value;
            else if (strcmp(name, "cfg_stride_dim1_in") == 0)
                tmConfig->cfg_stride_dim1_in = value;
            else if (strcmp(name, "cfg_stride_dim2_in") == 0)
                tmConfig->cfg_stride_dim2_in = value;
        }
        else if (std::is_same<typename Traits::Config, MatrixProcessConfig>::value)
        {
            MatrixProcessConfig* mpConfig = reinterpret_cast<MatrixProcessConfig*>(&cfg);
            // Precision
            if (strcmp(name, "cfg_type_out") == 0)
                mpConfig->cfg_type_out = value;
            else if (strcmp(name, "cfg_type_orig") == 0)
                mpConfig->cfg_type_orig = value;
            else if (strcmp(name, "cfg_type_in") == 0)
                mpConfig->cfg_type_in = value;
            else if (strcmp(name, "cfg_type_wt") == 0)
                mpConfig->cfg_type_wt = value;
            else if (strcmp(name, "cfg_wd_ref") == 0)
                mpConfig->cfg_wd_ref = value;
            else if (strcmp(name, "cfg_wd_sc_out") == 0)
                mpConfig->cfg_wd_sc_out = value;
            else if (strcmp(name, "cfg_wd_sc_orig") == 0)
                mpConfig->cfg_wd_sc_orig = value;
            else if (strcmp(name, "cfg_wd_sc_in") == 0)
                mpConfig->cfg_wd_sc_in = value;
            else if (strcmp(name, "cfg_wd_sc_wt") == 0)
                mpConfig->cfg_wd_sc_wt = value;
            // Operation
            else if (strcmp(name, "cfg_accu") == 0)
                mpConfig->cfg_accu = value;
            else if (strcmp(name, "cfg_act") == 0)
                mpConfig->cfg_act = value;
            else if (strcmp(name, "cfg_shift") == 0)
                mpConfig->cfg_shift = value;
            // Tensor O
            else if (strcmp(name, "cfg_rem_dim0_out") == 0)
                mpConfig->cfg_rem_dim0_out = value;
            else if (strcmp(name, "cfg_size_dim0b_out") == 0)
                mpConfig->cfg_size_dim0b_out = value;
            else if (strcmp(name, "cfg_size_dim1_out") == 0)
                mpConfig->cfg_size_dim1_out = value;
            else if (strcmp(name, "cfg_size_dim2_out") == 0)
                mpConfig->cfg_size_dim2_out = value;
            else if (strcmp(name, "cfg_stride_dim1_out") == 0)
                mpConfig->cfg_stride_dim1_out = value;
            else if (strcmp(name, "cfg_stride_dim2_out") == 0)
                mpConfig->cfg_stride_dim2_out = value;
            // Tensor I
            else if (strcmp(name, "cfg_rem_dim0_in") == 0)
                mpConfig->cfg_rem_dim0_in = value;
            else if (strcmp(name, "cfg_size_dim0b_in") == 0)
                mpConfig->cfg_size_dim0b_in = value;
            else if (strcmp(name, "cfg_size_dim1_in") == 0)
                mpConfig->cfg_size_dim1_in = value;
            else if (strcmp(name, "cfg_size_dim2_in") == 0)
                mpConfig->cfg_size_dim2_in = value;
            else if (strcmp(name, "cfg_stride_dim1_in") == 0)
                mpConfig->cfg_stride_dim1_in = value;
            else if (strcmp(name, "cfg_stride_dim2_in") == 0)
                mpConfig->cfg_stride_dim2_in = value;
            // Kernel
            else if (strcmp(name, "cfg_k_x") == 0)
                mpConfig->cfg_k_x = value;
            else if (strcmp(name, "cfg_k_y") == 0)
                mpConfig->cfg_k_y = value;
            else if (strcmp(name, "cfg_slide_x") == 0)
                mpConfig->cfg_slide_x = value;
            else if (strcmp(name, "cfg_slide_y") == 0)
                mpConfig->cfg_slide_y = value;
            else if (strcmp(name, "cfg_dl_x") == 0)
                mpConfig->cfg_dl_x = value;
            else if (strcmp(name, "cfg_dl_y") == 0)
                mpConfig->cfg_dl_y = value;
            else if (strcmp(name, "cfg_log2trs_x") == 0)
                mpConfig->cfg_log2trs_x = value;
            else if (strcmp(name, "cfg_log2trs_y") == 0)
                mpConfig->cfg_log2trs_y = value;
            // Padding
            else if (strcmp(name, "cfg_pad_w") == 0)
                mpConfig->cfg_pad_w = value;
            else if (strcmp(name, "cfg_pad_n") == 0)
                mpConfig->cfg_pad_n = value;
            else if (strcmp(name, "cfg_pad_val") == 0)
                mpConfig->cfg_pad_val = value;
        }
        else if (std::is_same<typename Traits::Config, VectorProcessConfig>::value)
        {
            VectorProcessConfig* vpConfig = reinterpret_cast<VectorProcessConfig*>(&cfg);
            // Precision
            if (strcmp(name, "cfg_type_out") == 0)
                vpConfig->cfg_type_out = value;
            else if (strcmp(name, "cfg_type_in1") == 0)
                vpConfig->cfg_type_in1 = value;
            else if (strcmp(name, "cfg_type_in2") == 0)
                vpConfig->cfg_type_in2 = value;
            else if (strcmp(name, "cfg_wd_ref") == 0)
                vpConfig->cfg_wd_ref = value;
            else if (strcmp(name, "cfg_wd_sc_out") == 0)
                vpConfig->cfg_wd_sc_out = value;
            else if (strcmp(name, "cfg_wd_sc_in1") == 0)
                vpConfig->cfg_wd_sc_in1 = value;
            else if (strcmp(name, "cfg_wd_sc_in2") == 0)
                vpConfig->cfg_wd_sc_in2 = value;
            // Operation
            else if (strcmp(name, "cfg_op") == 0)
                vpConfig->cfg_op = value;
            // Size
            else if (strcmp(name, "cfg_rem_dim0_ref") == 0)
                vpConfig->cfg_rem_dim0_ref = value;
            else if (strcmp(name, "cfg_size_dim0b_ref") == 0)
                vpConfig->cfg_size_dim0b_ref = value;
        }
        else if (std::is_same<typename Traits::Config, CimTensorProcessConfig>::value)
        {
            CimTensorProcessConfig* ctpConfig = reinterpret_cast<CimTensorProcessConfig*>(&cfg);
            // Precision
            if (strcmp(name, "cfg_type_out") == 0)
                ctpConfig->cfg_type_out = value;
            else if (strcmp(name, "cfg_type_orig") == 0)
                ctpConfig->cfg_type_orig = value;
            else if (strcmp(name, "cfg_type_in") == 0)
                ctpConfig->cfg_type_in = value;
            else if (strcmp(name, "cfg_type_wt") == 0)
                ctpConfig->cfg_type_wt = value;
            else if (strcmp(name, "cfg_wd_out") == 0)
                ctpConfig->cfg_wd_out = value;
            else if (strcmp(name, "cfg_wd_orig") == 0)
                ctpConfig->cfg_wd_orig = value;
            else if (strcmp(name, "cfg_wd_in") == 0)
                ctpConfig->cfg_wd_in = value;
            else if (strcmp(name, "cfg_wd_wt") == 0)
                ctpConfig->cfg_wd_wt = value;
            // Operation
            else if (strcmp(name, "cfg_accu") == 0)
                ctpConfig->cfg_accu = value;
            else if (strcmp(name, "cfg_act") == 0)
                ctpConfig->cfg_act = value;
            else if (strcmp(name, "cfg_shift") == 0)
                ctpConfig->cfg_shift = value;
            // Tensor O
            else if (strcmp(name, "cfg_rem_dim0_out") == 0)
                ctpConfig->cfg_rem_dim0_out = value;
            else if (strcmp(name, "cfg_size_dim0b_out") == 0)
                ctpConfig->cfg_size_dim0b_out = value;
            else if (strcmp(name, "cfg_size_dim1_out") == 0)
                ctpConfig->cfg_size_dim1_out = value;
            else if (strcmp(name, "cfg_stride_dim1_out") == 0)
                ctpConfig->cfg_stride_dim1_out = value;
            // Tensor I
            else if (strcmp(name, "cfg_rem_dim0_in") == 0)
                ctpConfig->cfg_rem_dim0_in = value;
            else if (strcmp(name, "cfg_size_dim0b_in") == 0)
                ctpConfig->cfg_size_dim0b_in = value;
            else if (strcmp(name, "cfg_size_dim1_in") == 0)
                ctpConfig->cfg_size_dim1_in = value;
            else if (strcmp(name, "cfg_stride_dim1_in") == 0)
                ctpConfig->cfg_stride_dim1_in = value;
        }
    }

    // 获取配置字段值
    uint32_t getConfigField(const typename Traits::Config& cfg, const char* name) const
    {
        if (std::is_same<typename Traits::Config, TensorTransferConfig>::value)
        {
            const TensorTransferConfig* ttConfig = reinterpret_cast<const TensorTransferConfig*>(&cfg);
            if (strcmp(name, "cfg_type") == 0)
                return ttConfig->cfg_type;
            else if (strcmp(name, "cfg_wd") == 0)
                return ttConfig->cfg_wd;
            else if (strcmp(name, "cfg_rem_dim0") == 0)
                return ttConfig->cfg_rem_dim0;
            else if (strcmp(name, "cfg_size_dim0b") == 0)
                return ttConfig->cfg_size_dim0b;
            else if (strcmp(name, "cfg_size_dim1") == 0)
                return ttConfig->cfg_size_dim1;
            else if (strcmp(name, "cfg_size_dim2") == 0)
                return ttConfig->cfg_size_dim2;
            else if (strcmp(name, "cfg_stride_dim1_gmem") == 0)
                return ttConfig->cfg_stride_dim1_gmem;
            else if (strcmp(name, "cfg_stride_dim2_gmem") == 0)
                return ttConfig->cfg_stride_dim2_gmem;
            else if (strcmp(name, "cfg_stride_dim1_lmem") == 0)
                return ttConfig->cfg_stride_dim1_lmem;
            else if (strcmp(name, "cfg_stride_dim2_lmem") == 0)
                return ttConfig->cfg_stride_dim2_lmem;
        }
        else if (std::is_same<typename Traits::Config, TensorManipConfig>::value)
        {
            const TensorManipConfig* tmConfig = reinterpret_cast<const TensorManipConfig*>(&cfg);
            if (strcmp(name, "cfg_type") == 0)
                return tmConfig->cfg_type;
            else if (strcmp(name, "cfg_wd") == 0)
                return tmConfig->cfg_wd;
            else if (strcmp(name, "cfg_rem_dim0") == 0)
                return tmConfig->cfg_rem_dim0;
            else if (strcmp(name, "cfg_size_dim0b") == 0)
                return tmConfig->cfg_size_dim0b;
            else if (strcmp(name, "cfg_size_dim1") == 0)
                return tmConfig->cfg_size_dim1;
            else if (strcmp(name, "cfg_size_dim2") == 0)
                return tmConfig->cfg_size_dim2;
            else if (strcmp(name, "cfg_stride_dim1_out") == 0)
                return tmConfig->cfg_stride_dim1_out;
            else if (strcmp(name, "cfg_stride_dim2_out") == 0)
                return tmConfig->cfg_stride_dim2_out;
            else if (strcmp(name, "cfg_stride_dim1_in") == 0)
                return tmConfig->cfg_stride_dim1_in;
            else if (strcmp(name, "cfg_stride_dim2_in") == 0)
                return tmConfig->cfg_stride_dim2_in;
        }
        else if (std::is_same<typename Traits::Config, MatrixProcessConfig>::value)
        {
            const MatrixProcessConfig* mpConfig = reinterpret_cast<const MatrixProcessConfig*>(&cfg);
            // Precision
            if (strcmp(name, "cfg_type_out") == 0)
                return mpConfig->cfg_type_out;
            else if (strcmp(name, "cfg_type_orig") == 0)
                return mpConfig->cfg_type_orig;
            else if (strcmp(name, "cfg_type_in") == 0)
                return mpConfig->cfg_type_in;
            else if (strcmp(name, "cfg_type_wt") == 0)
                return mpConfig->cfg_type_wt;
            else if (strcmp(name, "cfg_wd_ref") == 0)
                return mpConfig->cfg_wd_ref;
            else if (strcmp(name, "cfg_wd_sc_out") == 0)
                return mpConfig->cfg_wd_sc_out;
            else if (strcmp(name, "cfg_wd_sc_orig") == 0)
                return mpConfig->cfg_wd_sc_orig;
            else if (strcmp(name, "cfg_wd_sc_in") == 0)
                return mpConfig->cfg_wd_sc_in;
            else if (strcmp(name, "cfg_wd_sc_wt") == 0)
                return mpConfig->cfg_wd_sc_wt;
            // Operation
            else if (strcmp(name, "cfg_accu") == 0)
                return mpConfig->cfg_accu;
            else if (strcmp(name, "cfg_act") == 0)
                return mpConfig->cfg_act;
            else if (strcmp(name, "cfg_shift") == 0)
                return mpConfig->cfg_shift;
            // Tensor O
            else if (strcmp(name, "cfg_rem_dim0_out") == 0)
                return mpConfig->cfg_rem_dim0_out;
            else if (strcmp(name, "cfg_size_dim0b_out") == 0)
                return mpConfig->cfg_size_dim0b_out;
            else if (strcmp(name, "cfg_size_dim1_out") == 0)
                return mpConfig->cfg_size_dim1_out;
            else if (strcmp(name, "cfg_size_dim2_out") == 0)
                return mpConfig->cfg_size_dim2_out;
            else if (strcmp(name, "cfg_stride_dim1_out") == 0)
                return mpConfig->cfg_stride_dim1_out;
            else if (strcmp(name, "cfg_stride_dim2_out") == 0)
                return mpConfig->cfg_stride_dim2_out;
            // Tensor I
            else if (strcmp(name, "cfg_rem_dim0_in") == 0)
                return mpConfig->cfg_rem_dim0_in;
            else if (strcmp(name, "cfg_size_dim0b_in") == 0)
                return mpConfig->cfg_size_dim0b_in;
            else if (strcmp(name, "cfg_size_dim1_in") == 0)
                return mpConfig->cfg_size_dim1_in;
            else if (strcmp(name, "cfg_size_dim2_in") == 0)
                return mpConfig->cfg_size_dim2_in;
            else if (strcmp(name, "cfg_stride_dim1_in") == 0)
                return mpConfig->cfg_stride_dim1_in;
            else if (strcmp(name, "cfg_stride_dim2_in") == 0)
                return mpConfig->cfg_stride_dim2_in;
            // Kernel
            else if (strcmp(name, "cfg_k_x") == 0)
                return mpConfig->cfg_k_x;
            else if (strcmp(name, "cfg_k_y") == 0)
                return mpConfig->cfg_k_y;
            else if (strcmp(name, "cfg_slide_x") == 0)
                return mpConfig->cfg_slide_x;
            else if (strcmp(name, "cfg_slide_y") == 0)
                return mpConfig->cfg_slide_y;
            else if (strcmp(name, "cfg_dl_x") == 0)
                return mpConfig->cfg_dl_x;
            else if (strcmp(name, "cfg_dl_y") == 0)
                return mpConfig->cfg_dl_y;
            else if (strcmp(name, "cfg_log2trs_x") == 0)
                return mpConfig->cfg_log2trs_x;
            else if (strcmp(name, "cfg_log2trs_y") == 0)
                return mpConfig->cfg_log2trs_y;
            // Padding
            else if (strcmp(name, "cfg_pad_w") == 0)
                return mpConfig->cfg_pad_w;
            else if (strcmp(name, "cfg_pad_n") == 0)
                return mpConfig->cfg_pad_n;
            else if (strcmp(name, "cfg_pad_val") == 0)
                return mpConfig->cfg_pad_val;
        }
        else if (std::is_same<typename Traits::Config, VectorProcessConfig>::value)
        {
            const VectorProcessConfig* vpConfig = reinterpret_cast<const VectorProcessConfig*>(&cfg);
            // Precision
            if (strcmp(name, "cfg_type_out") == 0)
                return vpConfig->cfg_type_out;
            else if (strcmp(name, "cfg_type_in1") == 0)
                return vpConfig->cfg_type_in1;
            else if (strcmp(name, "cfg_type_in2") == 0)
                return vpConfig->cfg_type_in2;
            else if (strcmp(name, "cfg_wd_ref") == 0)
                return vpConfig->cfg_wd_ref;
            else if (strcmp(name, "cfg_wd_sc_out") == 0)
                return vpConfig->cfg_wd_sc_out;
            else if (strcmp(name, "cfg_wd_sc_in1") == 0)
                return vpConfig->cfg_wd_sc_in1;
            else if (strcmp(name, "cfg_wd_sc_in2") == 0)
                return vpConfig->cfg_wd_sc_in2;
            // Operation
            else if (strcmp(name, "cfg_op") == 0)
                return vpConfig->cfg_op;
            // Size
            else if (strcmp(name, "cfg_rem_dim0_ref") == 0)
                return vpConfig->cfg_rem_dim0_ref;
            else if (strcmp(name, "cfg_size_dim0b_ref") == 0)
                return vpConfig->cfg_size_dim0b_ref;
        }
        else if (std::is_same<typename Traits::Config, CimTensorProcessConfig>::value)
        {
            const CimTensorProcessConfig* ctpConfig = reinterpret_cast<const CimTensorProcessConfig*>(&cfg);
            // Precision
            if (strcmp(name, "cfg_type_out") == 0)
                return ctpConfig->cfg_type_out;
            else if (strcmp(name, "cfg_type_orig") == 0)
                return ctpConfig->cfg_type_orig;
            else if (strcmp(name, "cfg_type_in") == 0)
                return ctpConfig->cfg_type_in;
            else if (strcmp(name, "cfg_type_wt") == 0)
                return ctpConfig->cfg_type_wt;
            else if (strcmp(name, "cfg_wd_out") == 0)
                return ctpConfig->cfg_wd_out;
            else if (strcmp(name, "cfg_wd_orig") == 0)
                return ctpConfig->cfg_wd_orig;
            else if (strcmp(name, "cfg_wd_in") == 0)
                return ctpConfig->cfg_wd_in;
            else if (strcmp(name, "cfg_wd_wt") == 0)
                return ctpConfig->cfg_wd_wt;
            // Operation
            else if (strcmp(name, "cfg_accu") == 0)
                return ctpConfig->cfg_accu;
            else if (strcmp(name, "cfg_act") == 0)
                return ctpConfig->cfg_act;
            else if (strcmp(name, "cfg_shift") == 0)
                return ctpConfig->cfg_shift;
            // Tensor O
            else if (strcmp(name, "cfg_rem_dim0_out") == 0)
                return ctpConfig->cfg_rem_dim0_out;
            else if (strcmp(name, "cfg_size_dim0b_out") == 0)
                return ctpConfig->cfg_size_dim0b_out;
            else if (strcmp(name, "cfg_size_dim1_out") == 0)
                return ctpConfig->cfg_size_dim1_out;
            else if (strcmp(name, "cfg_stride_dim1_out") == 0)
                return ctpConfig->cfg_stride_dim1_out;
            // Tensor I
            else if (strcmp(name, "cfg_rem_dim0_in") == 0)
                return ctpConfig->cfg_rem_dim0_in;
            else if (strcmp(name, "cfg_size_dim0b_in") == 0)
                return ctpConfig->cfg_size_dim0b_in;
            else if (strcmp(name, "cfg_size_dim1_in") == 0)
                return ctpConfig->cfg_size_dim1_in;
            else if (strcmp(name, "cfg_stride_dim1_in") == 0)
                return ctpConfig->cfg_stride_dim1_in;
        }
        return 0;
    }
};

// C++11兼容性：解决方案 - 使用函数返回常量数组
// 这样可以避免静态成员定义的复杂性

// 特化的类型别名，方便使用
using TldConfigManager = ConfigManager<ConfigType::TLD>;
using TstConfigManager = ConfigManager<ConfigType::TST>;
using TmConfigManager = ConfigManager<ConfigType::TM>;
using MpConfigManager = ConfigManager<ConfigType::MP>;
using VpConfigManager = ConfigManager<ConfigType::VP>;
using CimTpConfigManager = ConfigManager<ConfigType::CIM_TP>;

}  // namespace hardware_instruction

#endif  // __REGISTER_H__