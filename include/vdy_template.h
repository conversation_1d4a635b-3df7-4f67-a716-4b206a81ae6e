#ifndef VDY_TEMPLATE_H
#define VDY_TEMPLATE_H

#include <tlm_utils/simple_initiator_socket.h>
#include <tlm_utils/simple_target_socket.h>
#include <array>
#include <iomanip>
#include <systemc>
#include <tlm>
#include "debug.h"

template <typename T>
void debug_vdy(const std::string& module_name, int interface_id, const T& message)
{
    std::ostringstream oss;
    oss << module_name << "[" << interface_id << "]: " << message;
    debug_print(oss.str());
}

template <int NUM_INTERFACES>
class Sender : public sc_core::sc_module
{
  public:
    std::array<tlm_utils::simple_initiator_socket<Sender>, NUM_INTERFACES> sockets;
    std::array<sc_core::sc_in<bool>, NUM_INTERFACES> ready_signals;
    std::array<sc_core::sc_out<bool>, NUM_INTERFACES> valid_signals;

    Sender(sc_core::sc_module_name name, const sc_core::sc_time& delay = sc_core::SC_ZERO_TIME)
        : sc_module(name), m_delay(delay)
    {
        for (int i = 0; i < NUM_INTERFACES; ++i)
        {
            m_current_transactions[i] = nullptr;
        }
        SC_THREAD(transaction_process);
        debug_vdy(this->name(), -1, "Initialized with delay: " + m_delay.to_string());
    }

    void request_transaction(int interface_id, tlm::tlm_generic_payload& trans)
    {
        debug_vdy(this->name(), interface_id, "request_transaction called");
        m_current_transactions[interface_id] = &trans;
        m_transaction_request_events[interface_id].notify();
        debug_vdy(this->name(), interface_id, "Event notified");
    }

  private:
    sc_core::sc_time m_delay;
    std::array<sc_core::sc_event, NUM_INTERFACES> m_transaction_request_events;
    std::array<tlm::tlm_generic_payload*, NUM_INTERFACES> m_current_transactions;

    void transaction_process()
    {
        debug_vdy(this->name(), -1, "transaction_process started");
        while (true)
        {
            sc_core::sc_event_or_list events;
            for (int i = 0; i < NUM_INTERFACES; ++i)
            {
                events |= m_transaction_request_events[i];
            }

            wait(events);

            debug_vdy(this->name(), -1, "Woke up from wait");

            for (int interface_id = 0; interface_id < NUM_INTERFACES; ++interface_id)
            {
                if (m_transaction_request_events[interface_id].triggered())
                {
                    debug_vdy(
                        this->name(), interface_id, "Event triggered, processing transaction");
                    process_transaction(interface_id);
                }
            }
        }
    }

    void process_transaction(int interface_id)
    {
        debug_vdy(this->name(), interface_id, "Transaction process started");

        // 等待握手完成
        do
        {
            wait(ready_signals[interface_id].value_changed_event() |
                 valid_signals[interface_id].value_changed_event());
        } while (!(valid_signals[interface_id].read() && ready_signals[interface_id].read()));

        debug_vdy(this->name(), interface_id, "Handshake completed, sending transaction");
        send_transaction(interface_id);
        valid_signals[interface_id].write(false);

        debug_vdy(this->name(), interface_id, "Transaction process completed");
    }

    void send_transaction(int interface_id)
    {
        if (m_current_transactions[interface_id])
        {
            sc_core::sc_time delay = m_delay;
            sockets[interface_id]->b_transport(*m_current_transactions[interface_id], delay);
            wait(delay);
            if (m_current_transactions[interface_id]->is_response_error())
            {
                debug_vdy(this->name(),
                          interface_id,
                          "Transaction failed with response status: " +
                              std::to_string(
                                  m_current_transactions[interface_id]->get_response_status()));
            }
            else
            {
                debug_vdy(this->name(), interface_id, "Transaction completed successfully");
            }
        }
        else
        {
            debug_vdy(this->name(), interface_id, "Error: No transaction to send");
        }
    }
    int m_transaction_process_count = 0;
};

template <int NUM_INTERFACES>
class Receiver : public sc_core::sc_module
{
  public:
    std::array<tlm_utils::simple_target_socket<Receiver>, NUM_INTERFACES> sockets;
    std::array<sc_core::sc_in<bool>, NUM_INTERFACES> valid_signals;
    std::array<sc_core::sc_out<bool>, NUM_INTERFACES> ready_signals;
    std::array<tlm::tlm_generic_payload*, NUM_INTERFACES> current_transactions;
    std::array<sc_core::sc_signal<bool>, NUM_INTERFACES> is_received;

    Receiver(sc_core::sc_module_name name, const sc_core::sc_time& delay = sc_core::SC_ZERO_TIME)
        : sc_module(name), m_delay(delay)
    {
        for (int i = 0; i < NUM_INTERFACES; ++i)
        {
            sockets[i].register_b_transport(this, &Receiver::b_transport_wrapper);
            sockets[i].register_nb_transport_fw(this, &Receiver::nb_transport_fw_wrapper);
            current_transactions[i] = nullptr;
            is_received[i].write(false);
        }
        debug_vdy(this->name(), -1, "Initialized with delay: " + m_delay.to_string());
    }

    void b_transport_wrapper(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
    {
        int interface_id = get_interface_id_from_socket(trans.get_streaming_width());
        b_transport(interface_id, trans, delay);
    }

    tlm::tlm_sync_enum nb_transport_fw_wrapper(tlm::tlm_generic_payload& trans,
                                               tlm::tlm_phase& phase,
                                               sc_core::sc_time& delay)
    {
        int interface_id = get_interface_id_from_socket(trans.get_streaming_width());
        return nb_transport_fw(interface_id, trans, phase, delay);
    }

    void b_transport(int interface_id, tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
    {
        debug_vdy(this->name(), interface_id, "b_transport called");
        current_transactions[interface_id] = &trans;
        process_received_data(interface_id, trans);
        trans.set_response_status(tlm::TLM_OK_RESPONSE);
        is_received[interface_id].write(true);
        ready_signals[interface_id].write(false);
        debug_vdy(this->name(), interface_id, "b_transport completed");
    }

    tlm::tlm_sync_enum nb_transport_fw(int interface_id,
                                       tlm::tlm_generic_payload& trans,
                                       tlm::tlm_phase& phase,
                                       sc_core::sc_time& delay)
    {
        debug_vdy(this->name(),
                  interface_id,
                  "nb_transport_fw called with phase: " + std::to_string(static_cast<int>(phase)));
        if (phase == tlm::BEGIN_REQ)
        {
            process_received_data(interface_id, trans);
            phase = tlm::END_RESP;
            trans.set_response_status(tlm::TLM_OK_RESPONSE);
            is_received[interface_id].write(true);
            ready_signals[interface_id].write(false);
            debug_vdy(this->name(), interface_id, "nb_transport_fw completed");
            return tlm::TLM_COMPLETED;
        }
        return tlm::TLM_ACCEPTED;
    }

    void finish(int interface_id)
    {
        if (current_transactions[interface_id])
        {
            current_transactions[interface_id] = nullptr;
            is_received[interface_id].write(false);
        }
    }

  private:
    sc_core::sc_time m_delay;

    void process_received_data(int interface_id, tlm::tlm_generic_payload& payload)
    {
        const auto* data_ptr = payload.get_data_ptr();
        auto length = payload.get_data_length();

        std::ostringstream oss;
        oss << std::hex << std::setfill('0');
        for (unsigned int i = 0; i < length; ++i)
        {
            oss << std::setw(2) << static_cast<int>(data_ptr[i]) << " ";
        }
        debug_vdy(this->name(), interface_id, oss.str());
    }
    auto get_interface_id_from_socket(unsigned int streaming_width) -> int
    {
        // 这里假设 streaming_width 被用来存储接口 ID
        return streaming_width;
    }
};

#endif  // VDY_TEMPLATE_H