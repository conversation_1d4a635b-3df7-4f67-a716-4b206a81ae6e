#ifndef CMD_DISPATCH_H
#define CMD_DISPATCH_H

#include <tlm_utils/simple_initiator_socket.h>
#include <tlm_utils/simple_target_socket.h>
#include <systemc>
#include <tlm>
#include "cmd_decoder.h"
#include "npu_config.h"
#include "rsp_rob.h"
#include "scoreboard.h"
#include "sysc/communication/sc_signal.h"
#include "sysc/communication/sc_signal_ports.h"
#include "sysc/kernel/sc_event.h"

struct ISQ_CMD
{
    struct CmdData
    {
        std::conditional_t<NPUConfig::RV_XLEN == 32, sc_dt::sc_uint<32>, sc_dt::sc_uint<64>> rs2val;
        std::conditional_t<NPUConfig::RV_XLEN == 32, sc_dt::sc_uint<32>, sc_dt::sc_uint<64>> rs1val;
        sc_dt::sc_uint<10> inst;

        CmdData() : rs2val(0), rs1val(0), inst(0)
        {
        }
    };
    std::array<CmdData, NPUConfig::FU_NUM> isq_cmd_data;

    ISQ_CMD() : isq_cmd_data()
    {
    }

    // 单独设置 rs2val
    void set_rs2val(
        size_t index,
        const std::conditional_t<NPUConfig::RV_XLEN == 32, sc_dt::sc_uint<32>, sc_dt::sc_uint<64>>&
            value)
    {
        if (index < NPUConfig::FU_NUM)
        {
            isq_cmd_data[index].rs2val = value;
        }
    }

    // 单独设置 rs1val
    void set_rs1val(
        size_t index,
        const std::conditional_t<NPUConfig::RV_XLEN == 32, sc_dt::sc_uint<32>, sc_dt::sc_uint<64>>&
            value)
    {
        if (index < NPUConfig::FU_NUM)
        {
            isq_cmd_data[index].rs1val = value;
        }
    }

    // 单独设置 inst
    void set_inst(size_t index, const sc_dt::sc_uint<10>& value)
    {
        if (index < NPUConfig::FU_NUM)
        {
            isq_cmd_data[index].inst = value;
        }
    }

    // 获取 rs2val
    std::conditional_t<NPUConfig::RV_XLEN == 32, sc_dt::sc_uint<32>, sc_dt::sc_uint<64>> get_rs2val(
        size_t index) const
    {
        return (index < NPUConfig::FU_NUM) ? isq_cmd_data[index].rs2val
                                           : std::conditional_t<NPUConfig::RV_XLEN == 32,
                                                                sc_dt::sc_uint<32>,
                                                                sc_dt::sc_uint<64>>(0);
    }

    // 获取 rs1val
    std::conditional_t<NPUConfig::RV_XLEN == 32, sc_dt::sc_uint<32>, sc_dt::sc_uint<64>> get_rs1val(
        size_t index) const
    {
        return (index < NPUConfig::FU_NUM) ? isq_cmd_data[index].rs1val
                                           : std::conditional_t<NPUConfig::RV_XLEN == 32,
                                                                sc_dt::sc_uint<32>,
                                                                sc_dt::sc_uint<64>>(0);
    }

    // 获取 inst
    sc_dt::sc_uint<10> get_inst(size_t index) const
    {
        return (index < NPUConfig::FU_NUM) ? isq_cmd_data[index].inst : sc_dt::sc_uint<10>(0);
    }
};

enum BlockReason
{
    NONE = 0,
    ROB = 1 << 0,
    SBD = 1 << 1,
    ISQ = 1 << 2,
    SYNC = 1 << 3
};

class CmdDispatch : public sc_core::sc_module
{
  public:
    tlm_utils::simple_target_socket<CmdDispatch> cmd_queue_socket;
    tlm_utils::simple_initiator_socket<CmdDispatch> rob_rsp_socket;
    tlm_utils::simple_initiator_socket<CmdDispatch> info_sbd_socket;

    tlm_utils::simple_initiator_socket<CmdDispatch> isq_cmd_socket;
    // rob_rsp
    sc_core::sc_out<bool> rob_rsp_valid;
    sc_core::sc_in<bool> rob_rsp_ready;
    sc_core::sc_in<bool> term_sync;
    // cmd_queue
    sc_core::sc_in<bool> cmd_queue_valid;
    sc_core::sc_out<bool> cmd_queue_ready;
    // isq_cmd
    std::array<sc_core::sc_out<bool>, NPUConfig::FU_NUM> isq_cmd_valid;
    std::array<sc_core::sc_in<bool>, NPUConfig::FU_NUM> isq_cmd_ready;
    // 新增：用于外部通知 SBD 更新的事件
    sc_core::sc_event sbd_update_event;

    CmdDispatch(const sc_core::sc_module_name& name)
        : sc_module(name), sbd_update_event("sbd_update_event"), sync_wait_flag(false)
    {
        cmd_queue_socket.register_b_transport(this, &CmdDispatch::cmd_queue_b_transport);

        SC_THREAD(cmd_process_thread);
        sensitive << cmd_received_event;
        SC_METHOD(handle_term_sync_method);
        sensitive << term_sync;
    }
    void cmd_process_thread();
    void get_cmd(Command& cmd);

  public:
    // 行为描述处理函数
    bool is_command_invalid();
    bool is_cimc_locked(uint32_t lmem_index);
    bool is_lmem_locked(uint32_t lmem_index);

    bool is_command_blocked();
    bool is_rob_blocked();
    bool is_sbd_blocked();
    bool is_isq_blocked();
    bool is_sync_blocked();
    bool is_lmem_busy(uint32_t lmem_index);
    bool is_cimc_busy(uint32_t lmem_index);

    void handle_invalid_command();
    void handle_blocked_command();
    void handle_active_command();

    void handle_sync_trap(InstructionName inst_type);
    void handle_rob_write(InstructionName inst_type);
    void handle_isq_write(InstructionName inst_type);
    void handle_sbd_update(InstructionName inst_type);

    bool should_write_rob(InstructionName inst_type);
    bool should_write_isq(InstructionName inst_type);
    bool should_update_sbd(InstructionName inst_type);
    auto create_roflag(InstructionName inst_type) -> disp_roflag_info;
    FunctionUnitType get_target_fu(InstructionName inst_type);
    void update_cimc(scoreboardUpdate& update, uint32_t lmem_index);
    void update_lmem_status(scoreboardUpdate& update, uint32_t lmem_index, LMemStatus status);
    void update_single_lmem(scoreboardUpdate& update,
                            uint32_t lmem_index,
                            FunctionUnitType fu_type,
                            uint8_t ch_util,
                            LMemStatus status = LMemStatus::BUSY);
    void update_double_lmem_simple(scoreboardUpdate& update,
                                   uint32_t rs1_lmem_index,
                                   uint32_t rs2_lmem_index,
                                   FunctionUnitType fu_type);
    void update_double_lmem_conditional(scoreboardUpdate& update,
                                        uint32_t rs1_lmem_index,
                                        uint32_t rs2_lmem_index,
                                        FunctionUnitType fu_type);

    void cmd_queue_b_transport(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    // debug
    bool debug_is_invalid;
    bool debug_is_blocked;

  private:
    Command recv_cmd;
    ISQ_CMD isq_cmd;
    sc_core::sc_event cmd_received_event;
    bool sync_wait_flag;

    scoreboardData info_sbd;

    void process_cmd();
    void send_to_rsp_rob(disp_roflag_info& roflag);
    void send_to_issue_queue();
    void read_info_sbd();
    void send_update_transaction(const scoreboardUpdate& update);

    int check_block_reason();
    void handle_term_sync_method();
};

#endif  // CMD_DISPATCH_H