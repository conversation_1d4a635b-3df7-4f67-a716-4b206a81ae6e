#include "npu_config.h"

// Define static const members of NPUConfig that need external linkage
constexpr int NPUConfig::LMEM_WD;
constexpr int NPUConfig::SPAD_NUM;
constexpr int NPUConfig::SPAD_WD;
constexpr int NPUConfig::SPAD_DP;
constexpr int NPUConfig::SPAD_NUM_BYTES;
constexpr int NPUConfig::SPAD_ADDR_WD;

constexpr int NPUConfig::CIMC_NUM;
constexpr int NPUConfig::CIMM_PG_NUM;
constexpr int NPUConfig::CIMC_CIME_NUM;
constexpr int NPUConfig::CIME_ROW_NUM;
constexpr int NPUConfig::CIME_COL_NUM;
constexpr int NPUConfig::CIMC_DP;
constexpr int NPUConfig::CIMC_NUM_BYTES;
constexpr int NPUConfig::CIMC_ADDR_WD;

constexpr int NPUConfig::LMEM_OFFSET;
constexpr int NPUConfig::LMEM_INDEX;
constexpr int NPUConfig::LMEM_NUM;

constexpr uint32_t NPUConfig::TOTAL_MACROS;
constexpr uint32_t NPUConfig::ROWS_PER_MACRO;
constexpr uint32_t NPUConfig::PAGES_PER_MACRO_ROW;
constexpr uint32_t NPUConfig::WORDS_PER_MACRO;
constexpr uint32_t NPUConfig::MACROS_PER_ENGINE;
constexpr uint32_t NPUConfig::WORDS_PER_ENGINE;
constexpr uint32_t NPUConfig::TOTAL_ENGINES;
constexpr uint64_t NPUConfig::TOTAL_WORDS;
constexpr uint64_t NPUConfig::TOTAL_BYTES;

constexpr int NPUConfig::FU_NUM;
constexpr int NPUConfig::FU_ID_WD;
constexpr int NPUConfig::CIMC_MODE_WD;
constexpr int NPUConfig::SBD_TRAN_SIZE;
constexpr int NPUConfig::ROB_QUEUE_SIZE; 