/**
 * @file scoreboard.h
 * @brief Header file for the scoreboard module.
 *
 * This file defines the scoreboard module, which is responsible for managing
 * the status of local memory (LMEM) entries in a SystemC simulation
 * environment. The scoreboard module uses TLM-2.0 for communication and
 * provides methods to update and read the status of LMEM entries.
 */

#ifndef SCOREBOARD_H
#define SCOREBOARD_H

#include <tlm.h>
#include <tlm_utils/simple_target_socket.h>
#include <array>
#include <systemc>
#include "npu_config.h"
#include "utils/systemc_logger.h"

/**
 * @brief Structure representing an LMEM entry.
 *
 * This structure contains the status, functional unit ID, channel utilization,
 * and CIMC mode of an LMEM entry.
 */
struct LMemEntry
{
    sc_dt::sc_uint<2> status;
    sc_dt::sc_uint<NPUConfig::FU_ID_WD> fu_id;
    sc_dt::sc_uint<3> ch_util;
    sc_dt::sc_uint<NPUConfig::CIMC_MODE_WD> cimc_mode;

    /**
     * @brief Default constructor.
     *
     * Initializes the LMEM entry with default values.
     */
    LMemEntry() : status(LMemStatus::IDLE), fu_id(FunctionUnitType::LMU), ch_util(0), cimc_mode(0)
    {
    }
};

/**
 * @brief Structure representing a scoreboard update.
 *
 * This structure contains the information needed to update the scoreboard,
 * including the LMEM ID, LMEM entries, and flags indicating which fields to
 * update.
 */
struct scoreboardUpdate
{
    sc_dt::sc_uint<NPUConfig::LMEM_NUM> lmem_id;
    std::array<LMemEntry, NPUConfig::LMEM_NUM> lmem_entries;
    sc_dt::sc_uint<1> update_status;
    sc_dt::sc_uint<1> update_fu_id;
    sc_dt::sc_uint<1> update_ch_util;
    sc_dt::sc_uint<1> update_cimc_mode;

    /**
     * @brief Default constructor.
     *
     * Initializes the scoreboard update with default values.
     */
    scoreboardUpdate()
        : lmem_id(0),
          lmem_entries(),
          update_status(0),
          update_fu_id(0),
          update_ch_util(0),
          update_cimc_mode(0)
    {
    }
};

/**
 * @brief Structure representing the scoreboard data.
 *
 * This structure contains the entire scoreboard data, including all LMEM
 * entries.
 */
struct scoreboardData
{
    // whole scoreboard data
    std::array<LMemEntry, NPUConfig::LMEM_NUM> lmem_entries;
    /**
     * @brief Default constructor.
     *
     * Initializes the scoreboard data with default values.
     */
    scoreboardData() : lmem_entries()
    {
    }
};

std::string scoreboard_data_string(const scoreboardData& data);

/**
 * @brief Class representing the scoreboard module.
 *
 * This class is a SystemC module that manages the status of LMEM entries. It
 * uses TLM-2.0 for communication and provides methods to update and read the
 * status of LMEM entries.
 */
class scoreboard : public sc_core::sc_module
{
  public:
    tlm_utils::simple_target_socket<scoreboard> socket;

    // SC_HAS_PROCESS(scoreboard);
    scoreboard(const sc_core::sc_module_name& name);

    /**
     * @brief TLM-2.0 blocking transport method.
     *
     * This method handles the TLM-2.0 blocking transport call, which is used to
     * update the scoreboard with new data.
     *
     * @param trans The TLM-2.0 generic payload containing the update data.
     * @param delay The time delay associated with the transaction.
     */
    virtual void b_transport(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);

    /**
     * @brief Method to read the scoreboard data.
     *
     * This method allows other modules to read the current state of the
     * scoreboard.
     *
     * @param data The scoreboardData structure to be filled with the current
     * data.
     */
    void read_scoreboard(scoreboardData& data);

    // 添加一个方法来打印当前scoreboard状态,用于调试
    void print_scoreboard() const;

    // 设置scoreboard状态
    void set_scoreboard_state(uint32_t lmem_index,
                              LMemStatus status,
                              FunctionUnitType fu_id = FunctionUnitType::INVALID,
                              uint8_t ch_util = 0);
    void reset();

  private:
    std::array<LMemEntry, NPUConfig::LMEM_NUM> lmem_entries;  ///< Array of LMEM entries

    /**
     * @brief Helper method to update a single LMEM entry.
     *
     * This method updates the specified LMEM entry based on the provided
     * scoreboardUpdate structure.
     *
     * @param update The scoreboardUpdate structure containing the update data.
     */
    void update_entry(const scoreboardUpdate& update);
};

#endif  // SCOREBOARD_H