#ifndef __CMD_DECODER_H__
#define __CMD_DECODER_H__

#include <cstdint>
#include <cstring>
#include <type_traits>
#include <vector>
#include "npu_config.h"
#include "utils/systemc_logger.h"

// 指令组枚举
enum class InstructionGroup
{
    Control,
    DataTransfer,
    TensorManipulation,
    MatrixProcessing,
    VectorProcessing
};

// 指令名称枚举
enum class InstructionName
{
    SYNC,
    SW_CIMC,
    LK_LMEM,
    UNLK_LMEM,
    WR_LMEM,
    RD_LMEM,
    TLD_CFG,
    TLD_DRV,
    TST_CFG,
    TST_DRV,
    TM_CFG,
    BC_PRE,
    BC_DRV,
    MOV_DRV,
    TRANS_DRV,
    MP_CFG,
    CONV_PRE,
    CONV_DRV,
    DWCONV_DRV,  // TODO:DWCONV_PRE
    VP_CFG,
    VV_V_PRE,
    VV_V_DRV,
    VS_V_PRE,
    VS_V_DRV,
    V_S_DRV,
    V_V_DRV
};

// 命令结构
struct Command
{
    std::conditional_t<NPUConfig::RV_XLEN == 32, uint32_t, uint64_t> rs2val;
    std::conditional_t<NPUConfig::RV_XLEN == 32, uint32_t, uint64_t> rs1val;
    uint8_t func7;
    uint8_t rs2;
    uint8_t rs1;
    bool xd;
    bool xs1;
    bool xs2;
    uint8_t rd;
    uint8_t opcode;
};

// LMEM 地址结构说明
/*
   MSB                                                                         LSB
   +----------------+---------------------------+------------------------+----------+
   |    Reserved    |        LMEM_INDEX         |      LMEM_OFFSET       |    0     |
   +----------------+---------------------------+------------------------+----------+
                    |NPUConfig::LMEM_INDEX|NPUConfig::LMEM_OFFSET|   5    |
*/
struct LmemAddr32
{
    uint32_t zero : 5;
    uint32_t lmem_offset : NPUConfig::LMEM_OFFSET;
    uint32_t lmem_index : NPUConfig::LMEM_INDEX;
    uint32_t reserved : (32 - 5 - NPUConfig::LMEM_OFFSET - NPUConfig::LMEM_INDEX);
};

struct LmemAddr64
{
    uint64_t zero : 5;
    uint64_t lmem_offset : NPUConfig::LMEM_OFFSET;
    uint64_t lmem_index : NPUConfig::LMEM_INDEX;
    uint64_t reserved : (64 - 5 - NPUConfig::LMEM_OFFSET - NPUConfig::LMEM_INDEX);
};

// 根据 NPUConfig::RV_XLEN 选择适当的结构体
using LmemAddr = std::conditional_t<NPUConfig::RV_XLEN == 32, LmemAddr32, LmemAddr64>;

// 辅助函数：解析 LMEM 地址
inline LmemAddr parseLmemAddr(std::conditional_t<NPUConfig::RV_XLEN == 32, uint32_t, uint64_t> addr)
{
    LmemAddr result;
    std::memcpy(&result, &addr, sizeof(LmemAddr));
    return result;
}

// 辅助函数：构造 LMEM 地址
inline std::conditional_t<NPUConfig::RV_XLEN == 32, uint32_t, uint64_t> constructLmemAddr(
    uint32_t lmem_index,
    uint32_t lmem_offset)
{
    LmemAddr addr{};
    addr.zero = 0;
    addr.lmem_offset = lmem_offset;
    addr.lmem_index = lmem_index;
    addr.reserved = 0;

    std::conditional_t<NPUConfig::RV_XLEN == 32, uint32_t, uint64_t> result;
    std::memcpy(&result, &addr, sizeof(LmemAddr));
    return result;
}

Command decode(const uint8_t* cmd_ptr);
std::vector<uint8_t> generateRawCommand(uint64_t rs2val, uint64_t rs1val, uint32_t instruction);
uint32_t generateInstruction(uint8_t func7, uint8_t xd, uint8_t xs1, uint8_t xs2);
bool isValidFunc7(uint8_t func7);
uint32_t generateValidInstruction();
InstructionGroup decodeGroup(const uint8_t& func7);
InstructionName decodeName(const uint8_t& func7);
void print_cmd(const Command& cmd);
uint8_t encodeName(const InstructionName& name);
Command generate_command(InstructionName inst_type,
                         uint64_t rs1val = 0,
                         uint64_t rs2val = 0,
                         bool xd = false,
                         bool xs1 = false,
                         bool xs2 = false);
#endif