
---

# TMU 地址分析与处理逻辑

## 1. 概述
本文档详细说明了张量操作单元 (Tensor Manipulation Unit, TMU) 的内存布局、寻址方式以及其支持的操作（BC广播、MOVE、TRANS）的处理逻辑，特别是基于块 (Block) 的处理方式。
## 2. 配置参数
TMU 的行为由 `TensorManipConfig` 结构体控制：
```cpp
struct TensorManipConfig {
    // --- 数据类型 ---
    uint32_t cfg_type; // 与 cfg_wd 结合确定数据类型
    uint32_t cfg_wd;   // (具体映射关系未在此文档说明)
                       // 支持: INT4, INT8, INT16, FP8, FP16, BF16

    // --- 输入/输出 Tensor 维度信息 ---

    // 维度 0 (最内层，256-bit 对齐处理后)
    uint32_t cfg_rem_dim0;     // size_dim0 % size_dim0a: 最后一个 dim0b 块中有效 dim0a 元素数
    uint32_t cfg_size_dim0b;   // dim0b 维度的大小 (ceil(size_dim0 / size_dim0a))

    // 维度 1 和 2
    uint32_t cfg_size_dim1;    // dim1 维度的大小
    uint32_t cfg_size_dim2;    // dim2 维度的大小 (最外层)

    // --- 输入/输出 Tensor 步长信息 (单位: 256-bit chunks) ---
    uint32_t cfg_stride_dim1_out; // 输出 Tensor dim1 步长
    uint32_t cfg_stride_dim2_out; // 输出 Tensor dim2 步长
    uint32_t cfg_stride_dim1_in;  // 输入 Tensor dim1 步长
    uint32_t cfg_stride_dim2_in;  // 输入 Tensor dim2 步长
};
```

*   **数据位宽 (`data_bit_width`)**: 由 `cfg_type` 和 `cfg_wd` 确定 (例如 4, 8, 16 bit)。
## 3. Tensor 内存布局
TMU 处理的 Tensor 具有逻辑上的三维结构 `(dim2, dim1, dim0)`，但其在内存中的物理布局有特定规则：
1.  **256-bit 对齐**: 内存操作的基本单位是 256-bit (32 Bytes)。
2.  **`dim0` 拆分**:
    *   逻辑维度 `dim0` (元素个数) 被拆分为两个子维度：`dim0a` 和 `dim0b`。
    *   `size_dim0a`: 一个 256-bit chunk 内能容纳的元素个数。
        `size_dim0a = 256 / data_bit_width`
    *   `size_dim0b`: 需要多少个 256-bit chunk 来存储 `dim0` 方向上的所有元素（向上取整）。
        `size_dim0b = ceil(size_dim0 / size_dim0a)` (对应 `cfg_size_dim0b`)
3.  **`dim0` 填充**:
    *   在 `dim0` 方向上，数据必须向上取整到 256-bit 的整数倍。
    *   `cfg_rem_dim0`: 表示原始 `size_dim0` 除以 `size_dim0a` 的余数。
        `cfg_rem_dim0 = size_dim0 % size_dim0a`
    *   如果 `cfg_rem_dim0 > 0`，则在最后一个 `dim0b` chunk 中，只有前 `cfg_rem_dim0` 个 `dim0a` 位置是有效数据，其余 `size_dim0a - cfg_rem_dim0` 个位置用 0 填充。如果 `cfg_rem_dim0 == 0`，则最后一个 chunk 是满的（或者 `size_dim0` 本身是 0）。
4.  **存储顺序**: 数据在内存中按以下维度顺序线性排列（从最快变化到最慢变化）：
    `dim0a` -> `dim0b` -> `dim1` -> `dim2`
5.  **步长 (Stride)**:
    *   `cfg_stride_dim1_in/out`: 在 `dim1` 维度上移动一个单位 (`idx_dim1` 增加 1) 需要跳过的 **256-bit chunk** 数量。
    *   `cfg_stride_dim2_in/out`: 在 `dim2` 维度上移动一个单位 (`idx_dim2` 增加 1) 需要跳过的 **256-bit chunk** 数量。
    *   `dim0b` 维度的步长隐式为 1 (个 256-bit chunk)。

### 3.1 特殊情况：CIMC 内存布局与寻址

当 Tensor 的目标存储介质为 CIMC (Compute-In-Memory Cluster) 时，其物理布局和寻址方式与上述通用线性模型（主要适用于 SPAD）存在显著差异：

1.  **物理结构:** CIMC 具有特定的多维物理结构，通常包含：
    *   **Engine :** 顶层单元 (例如 4 个)。
    *   **Macro :** 每个引擎包含若干宏单元 (例如 2 个)。
    *   **Row :** 每个宏单元的页中包含多行 (例如 33 行，包括 32 行数据和 1 行指数)。
    *   **Page :** 每个宏单元包含若干页 (例如 16 页)。
    *   总容量由这些维度决定 (例如 4 Engines * 2 Macros/Engine * 33 Rows/Macro * 16 Pages/Row = 4224 个 256-bit words)。

2.  **物理寻址优先级 (线性地址映射):** 在 CIMC 的线性地址空间中，地址映射的优先级（最快变化到最慢变化）通常为：
    `Page` -> `Row` -> `Macro` -> `Engine`
    这意味着连续的 word 地址首先会遍历完一个 Row 的所有 Page，然后才移动到下一个 Row。

3.  **TMU 逻辑索引映射 (写入 CIMC 时):** 当 TMU 配置为向 CIMC 写入数据时，其逻辑维度索引 (`idx_d2`, `idx_d1`, `idx_d0b`) 通常需要映射到 CIMC 的物理维度：
    *   `idx_d2` (TMU 逻辑维度 2) -> `Page` (CIMC 物理页索引, 0-15)。
    *   `idx_d1` (TMU 逻辑维度 1) -> `Engine` + `Row within Engine` (CIMC 物理引擎和宏内行索引)。
        *   例如，在 `MOV` 操作中，`block_idx_d1` (0-3) 对应 `Engine` 索引。
        *   `rel_idx_d1` (0-63) 对应引擎内的行，需要进一步映射到 `Macro` (0-1) 和 `Row within Macro` (0-31，仅数据行)。
    *   `idx_d0b` (TMU 逻辑维度 0b) 通常为 1，不直接参与 CIMC 物理地址的主要结构映射。

4.  **地址计算差异:**
    *   **不使用 Stride:** 由于 CIMC 的非线性物理布局（特别是 Page 优先和指数行的存在），TMU 在计算 CIMC 写入地址时，**不使用** 配置中的 `cfg_stride_dim1_out` 和 `cfg_stride_dim2_out` 参数。
    *   **直接物理地址计算:** TMU (或其接口逻辑) 需要根据映射后的物理索引 (Engine, Macro, Row, Page) 直接计算目标 word 在 CIMC 线性地址空间中的偏移量。例如，使用类似 `word_offset = E * WORDS_PER_ENGINE + M * WORDS_PER_MACRO + R * PAGES_PER_MACRO_ROW + P` 的公式。
    *   **指数行处理:** 写入数据时，必须显式地将逻辑行索引映射到物理数据行 (0-31)，并单独处理指数行 (物理行 32) 的写入（例如在 `MOV-ALIGN` 操作中）。
因此，理解目标内存是 SPAD 还是 CIMC 对于正确配置 TMU 和解释其地址计算行为至关重要。CIMC 的寻址需要对物理结构有明确的了解，并采用与通用 stride 模型不同的计算方式。
## 4. 地址计算
### 4.1 计算 256-bit Chunk 地址
给定 Tensor 的基地址 `byte_base` 和一个逻辑索引 `(idx_dim2, idx_dim1, idx_dim0b)`，对应 256-bit chunk 的字节地址计算如下：
```cpp
// 计算 256-bit chunk 的偏移量 (单位: 256-bit)
offset_256bit = idx_dim2 * cfg_stride_dim2 + idx_dim1 * cfg_stride_dim1 + idx_dim0b;
// 计算 256-bit chunk 的起始字节地址
// Note: Matches calculate_byte_address in C++
chunk_byte_addr = byte_base + offset_256bit * 32; // 1 chunk = 32 bytes
```
*注意*: 这里的 `cfg_stride_dim1` 和 `cfg_stride_dim2` 应根据是计算输入地址还是输出地址，选用 `_in` 或 `_out` 的 stride。
## 5. TMU 操作
TMU 主要执行以下操作：
1.  **BC (Broadcast)**: 将一个标量值 (`scalar_value`) 写入到整个输出 Tensor (`byte_base_out`)。此操作通常**不**需要复杂的块处理或读取输入 Tensor。
2.  **MOVE**: 将输入 Tensor (`byte_base_in`) 的内容按原样复制到输出 Tensor (`byte_base_out`)。此操作使用块处理。
3.  **TRANS (Transpose)**: 将输入 Tensor (`byte_base_in`) 进行 `dim1` 和 `dim0` 轴的转置 (具体转置方式需结合块处理理解)，并将结果写入输出 Tensor (`byte_base_out`)。此操作使用块处理。
## 6. 块 (Block) 处理
对于 MOVE 和 TRANS 操作，为了高效处理，Tensor 被划分为若干块 (Block) 进行读写。
### 6.1 块处理动机
*   匹配 TMU 内部处理单元/缓冲区的大小。
*   实现并行化处理。
*   简化复杂操作（如 TRANS）的控制逻辑。
### 6.2 块维度与尺寸
分块主要在 `dim1` 和 `dim0b` 维度上进行。`dim2` 维度通常作为最外层循环，逐层处理。

| 参数                 | 描述                                       | MOVE / MOV-ALIGN (In/Out) | TRANS / TRANS-ALIGN (In)          | TRANS / TRANS-ALIGN (Out) |
| :------------------- | :----------------------------------------- | :------------------------ | :-------------------------------- | :------------------------ |
| `block_size_dim1`    | Block 在 dim1 轴的大小 (行数)              | 64                        | Dynamic (e.g., 16 for 16b, 32 for 8b, 64 for 4b) - see `get_trans_input_block_dims` | 64                        |
| `block_size_dim0b`   | Block 在 dim0b 轴的大小 (256-bit chunk 数) | 1                         | Dynamic (e.g., 4 for 16b, 2 for 8b, 1 for 4b) - see `get_trans_input_block_dims`   | 1                         |
| **Block Bit Size**   | **(dim1 * dim0b * 256)**                   | **64x256 bit**            | **Dynamic (e.g., 16x4x256 for 16b)** | **64x256 bit**            |
| `block_num_dim1`     | dim1 轴上的 Block 总数                     | `ceil_div(cfg_size_dim1, 64)` | `ceil_div(cfg_size_dim1_in, block_size_dim1_in)` | `ceil_div(cfg_size_dim1_out, 64)` |
| `block_num_dim0b`    | dim0b 轴上的 Block 总数                    | `cfg_size_dim0b`          | `ceil_div(cfg_size_dim0b_in, block_size_dim0b_in)` | `cfg_size_dim0b_out`      |
*   **Input Block (`block_in`)**: MOVE 时与 Output Block 相同。TRANS 时尺寸依赖数据位宽。
*   **Output Block (`block_out`)**: MOVE 和 TRANS 时尺寸固定为 `(block_size_dim1_out, block_size_dim0b_out) = (64, 1)`。
### 6.3 块迭代逻辑
处理流程通常按以下嵌套循环进行：
```cpp
// 假设处理输入 Tensor
for (size_t idx_dim2_in = 0; idx_dim2_in < cfg_size_dim2_in; ++idx_dim2_in) {
    // 计算 dim1 方向上的块数量
    size_t block_num_dim1_in = ceil((double)cfg_size_dim1_in / block_size_dim1_in);
    for (size_t block_idx_dim1_in = 0; block_idx_dim1_in < block_num_dim1_in; ++block_idx_dim1_in) {
        // 计算 dim0b 方向上的块数量
        size_t block_num_dim0b_in = ceil((double)cfg_size_dim0b_in / block_size_dim0b_in);
        for (size_t block_idx_dim0b_in = 0; block_idx_dim0b_in < block_num_dim0b_in; ++block_idx_dim0b_in) {

            // 1. 计算当前 Input Block 的基地址 (见 6.4)
            // 2. 确定当前 Input Block 的有效数据区域 (tile_size, 见 7.1)
            // 3. 计算对应的 Output Block 的索引和基地址
            // 4. 确定 Output Block 的有效数据区域 (tile_size)
            // 5. 执行块内数据处理 (读取、转换(TRANS)、写入)
            //    - 处理块内地址 (见 6.5)
            //    - 处理填充 (见 7)
        }
    }
}
```
### 6.4 块基地址计算
计算索引为 `(idx_dim2, block_idx_dim1, block_idx_dim0b)` 的块的起始字节地址：
```cpp
// Input Block Base Address
// Calculate the absolute start index for the block in each dimension
block_start_idx_dim1_in = block_idx_dim1_in * block_size_dim1_in;
block_start_idx_dim0b_in = block_idx_dim0b_in * block_size_dim0b_in;
// Use calculate_byte_address logic (offset calculation shown here)
block_offset_256bit_in = idx_dim2_in * cfg_stride_dim2_in
                       + block_start_idx_dim1_in * cfg_stride_dim1_in
                       + block_start_idx_dim0b_in; // dim0b stride is implicitly 1
block_byte_base_in = byte_base_in + block_offset_256bit_in * 32;
// Output Block Base Address (similar calculation, using _out parameters)
// block_idx_dim1_out, block_idx_dim0b_out need mapping based on _in indices and operation type (e.g., using get_input_block_indices_for_output for TRANS)
block_start_idx_dim1_out = block_idx_dim1_out * block_size_dim1_out; // e.g., block_size_dim1_out = 64
block_start_idx_dim0b_out = block_idx_d0b_out * block_size_dim0b_out; // e.g., block_size_dim0b_out = 1
// Use calculate_byte_address logic (offset calculation shown here)
block_offset_256bit_out = idx_dim2_out * cfg_stride_dim2_out // Often idx_dim2_out = idx_dim2_in
                        + block_start_idx_dim1_out * cfg_stride_dim1_out
                        + block_start_idx_dim0b_out;
block_byte_base_out = byte_base_out + block_offset_256bit_out * 32;
```
### 6.5 块内地址处理
访问块内部的数据时，需要计算相对于块基地址 (`block_byte_base`) 的偏移。
假设要访问块内相对索引为 `(rel_idx_dim1, rel_idx_dim0b)` 的 256-bit chunk (其中 `0 <= rel_idx_dim1 < block_size_dim1`, `0 <= rel_idx_dim0b < block_size_dim0b`)：
```cpp
// 计算块内某个 chunk 的绝对地址
// 1. 计算块内 chunk 的绝对索引
abs_idx_dim1 = block_start_idx_dim1 + rel_idx_dim1;
abs_idx_dim0b = block_start_idx_dim0b + rel_idx_dim0b;
// 2. 使用 calculate_byte_address (或其逻辑) 计算绝对地址
// (Using _in strides for example)
offset_256bit = idx_dim2 * cfg_stride_dim2_in + abs_idx_dim1 * cfg_stride_dim1_in + abs_idx_dim0b;
chunk_byte_addr = byte_base + offset_256bit * 32;
// 或者，计算相对于块基地址的偏移 (less common in C++ impl)
// relative_offset_256bit = rel_idx_dim1 * cfg_stride_dim1 + rel_idx_dim0b;
// chunk_byte_addr = block_byte_base + relative_offset_256bit * 32;
```
*注意*: 对于 TRANS 操作，读取输入块和写入输出块时，`rel_idx` 的映射关系以及使用的 `cfg_stride` 会不同，需要根据具体的转置逻辑确定。
## 7. 处理填充与边缘块
当 Tensor 尺寸不能被块尺寸整除时，边缘块会包含填充区域。TMU 处理时必须正确处理这些填充。
### 7.1 块边缘填充 (`tile_size`)
`tile_size` 表示一个块内实际有效的（非填充）数据区域大小。
*   **计算 `tile_size`**: The C++ code uses a helper function `calculate_tile_size`.
    ```cpp
    // Pseudocode reflecting the C++ function call
    // Assumes 'is_input' flag determines whether to use _in or _out config/sizes
    TileSize calculate_tile_size(
        uint32_t block_idx_d1,     // Current block index in dim1
        uint32_t block_idx_d0b,    // Current block index in dim0b
        uint32_t cfg_size_dim1,    // Total tensor size in dim1
        uint32_t cfg_size_dim0b,   // Total tensor size in dim0b
        uint32_t block_num_dim1,   // Total number of blocks in dim1
        uint32_t block_num_dim0b,  // Total number of blocks in dim0b
        uint32_t block_size_dim1,  // Size of block in dim1
        uint32_t block_size_dim0b  // Size of block in dim0b
    ) {
        bool is_dim1_edge = (block_idx_d1 == block_num_dim1 - 1);
        bool is_dim0b_edge = (block_idx_d0b == block_num_dim0b - 1);

        uint32_t rem_dim1 = cfg_size_dim1 % block_size_dim1;
        uint32_t rem_dim0b = cfg_size_dim0b % block_size_dim0b;

        uint32_t tile_dim1 = is_dim1_edge ? (rem_dim1 == 0 ? block_size_dim1 : rem_dim1) : block_size_dim1;
        uint32_t tile_dim0b = is_dim0b_edge ? (rem_dim0b == 0 ? block_size_dim0b : rem_dim0b) : block_size_dim0b;

        return {tile_dim1, tile_dim0b}; // Returns std::pair or similar structure
    }

    // Example Usage:
    TileSize input_tile_size = calculate_tile_size(
        input_block_idx.d1, input_block_idx.d0b,
        config.cfg_size_dim1_in, config.cfg_size_dim0b_in,
        block_num_dim1_in, block_num_dim0b_in,
        IN_BLOCK_SIZE_DIM1, IN_BLOCK_SIZE_DIM0B
    );
    ```
*   **使用 `tile_size`**:
    *   在读取块内数据时，如果块内相对索引 `(rel_idx_dim1, rel_idx_dim0b)` 满足 `rel_idx_dim1 >= tile_size_dim1` 或 `rel_idx_dim0b >= tile_size_dim0b`，则该位置属于块级填充区域。
    *   从填充区域读取时，应视为读取到 0。
    *   向填充区域写入时（通常发生在输出块），根据需要可能写入 0 或跳过写入。
### 7.2 Chunk 内部填充 (`rem_dim0`)
这种填充发生在 Tensor 的最后一个 `dim0b` chunk 内部，与块处理的边界无关，但可能与边缘块重叠。
*   **判断条件**: 当处理的 256-bit chunk 的绝对索引 `idx_dim0b` 满足 `idx_dim0b == cfg_size_dim0b - 1` 且 `cfg_rem_dim0 > 0` 时。
*   **处理**: 在这个特定的 chunk 内，只有 `idx_dim0a` 从 `0` 到 `cfg_rem_dim0 - 1` 的元素是有效的。访问 `idx_dim0a >= cfg_rem_dim0` 的位置应视为 0. 
* The C++ code handles this using `calculate_mask_info` which generates a `MaskInfo` struct containing a byte mask (`Word256b mask`) and a boolean `need_mask`. This mask is then used implicitly or explicitly during read/write operations (e.g., inside `read_data`).
### 7.3 填充处理策略
*   **读取 (Input Block)**: TMU 在读取数据时，必须结合 `tile_size` 和 `rem_dim0` (如果适用) 来识别填充位置。硬件应能直接生成 0 值，而不是读取无效内存。
*   **写入 (Output Block)**: 写入输出块时，同样需要考虑输出 Tensor 的 `tile_size` 和 `rem_dim0`。通常只将有效数据写入目标位置，填充区域可以跳过或显式写入 0。
---
# 指令操作流程分析
## 架构图
![[tmu_overview.png]]模块中主要包含以下几个部件:
a. tmu_datapath:根据当前张量处理任务,对数据的来源和处理方式进行控制。
	i. 对于MOV指令,通过tmu_datapath的bypass通路直接进行处理,对应图中的②④通路
	ii. 对于TRANS指令,通过tmu_datapath的①④通路进行处理
	iii. 对于MOV-ALIGN指令,通过tmu_datapath的②③通路进行处理
	iv. 对于TRANS-ALIGN指令,通过tmu_datapath的①③通路进行处理
b. TMU_TRANS module: 处于tmu_datapath内部,用于完成转置操作
c. TMU_ALIGN module: 处于tmu_datapath内部,用于完成对齐操作
d. tmu_rd_req_tx:根据相关配置信号,以及Local memory中数据存储的基地址,对矩阵分块并产生数据读取的地址和长度信息,并完成Local memory的读请求。或者通过与LSU(DRAM)的直连通路,通过Valid-Ready stream直接完成对DRAM的数据读取。
e. tmu_rd_ack_rx: 将接收到的来自Local memory的数据通过tmu访存通道读取到tmu中,将读取到的信息发送到tmu_datapath单元,进行数据后续处理
f. tmu_wr_req_tx: 接收来自tmu_datapath处理后数据,或者来自寄存器的scalar数据,依据相关配置信号,以及local memory中存储的输出数据存储基地址,对矩阵分块并产生数据读取的地址和长度信息,将数据由tmu的访存通道存入local memory。
g. tmu_wr_ack_rx:用于确认数据已经完全写入Local memory中
## 功能定义
### **数据搬移操作，MOV**
MOV 操作完成如下两步操作:
Step1: 根据 byte_base_in 寻址到存储在 LMEM 中的 Tensor I，将 Tensor I 读出
Step2: 根据 byte_base_out 寻址到存储在 LMEM 中的 Tensor O，并将读入 Tensor I 位置，结束执行。
LMEM 中存储着 `Tensor I` 和 `Tensor O`。
1.  **Step 1 (Read):**
    *   TMU 根据 `byte_base_in` 地址从 LMEM 中读取 `Tensor I`。
    *   数据流向：`Tensor I` (LMEM) -> TMU
2.  **Step 2 (Write):**
    *   TMU 根据 `byte_base_out` 地址将读取到的 `Tensor I` 的数据写入 LMEM 中 `Tensor O` 的位置。
    *   数据流向：TMU -> `Tensor O` (LMEM)
### **数据广播操作，BC**
BC 操作完成如下两步操作:
Step1: 接收到的标量输入 Scalar I
Step2: 根据 byte_base_out 寻址到存储在 LMEM 中的 Tensor O，并将 Scalar I 广播到 Tensor O 位置，结束执行。
LMEM 中存储着 `Tensor O`。
1.  **Step 1 (Receive Scalar):**
    *   TMU 接收到输入的标量 `Scalar I`。
2.  **Step 2 (Broadcast and Write):**
    *   TMU 根据 `byte_base_out` 地址将接收到的 `Scalar I` 广播并写入 LMEM 中 `Tensor O` 的所有位置。
    *   数据流向：`Scalar I` -> TMU (Broadcast) -> `Tensor O` (LMEM)
### **矩阵转置操作，TRANS**
TRANS 操作完成如下三步操作:
Step1: 根据 byte_base_in 寻址到存储在 LMEM 中的 Tensor I，从 LMEM 中读出 Tensor I
Step2: TMU 内部 Transpose 模块，对 Tensor I 进行转置操作，得到转置后的 Tensor Ot
Step3: 根据 byte_base_out 寻址到存储在 LMEM 中的 Tensor O，将转置后结果 Tensor Ot 写入 Tensor O 位置，结束执行。
LMEM 中存储着 `Tensor I` 和 `Tensor O`。
  **Step 1 (Read):**
    *   TMU 根据 `byte_base_in` 地址从 LMEM 中读取 `Tensor I`。
    *   数据流向：`Tensor I` (LMEM) -> TMU
  **Step 2 (Transpose):**
    *   TMU 内部的 `Transpose` 模块对接收到的 `Tensor I` 进行转置操作，得到 `Tensor Ot`。
    *   数据流向：`Tensor I` (TMU) -> `Transpose` 模块 -> `Tensor Ot` (TMU)
 **Step 3 (Write):**
    *   TMU 根据 `byte_base_out` 地址将转置后的 `Tensor Ot` 写入 LMEM 中 `Tensor O` 的位置。
    *   数据流向：`Tensor Ot` (TMU) -> `Tensor O` (LMEM)
### **矩阵移动对齐操作，MOV-ALIGN**
ALIGN 操作完成如下四步操作:
Step1: 根据 byte_base_in 寻址到存储在 LMEM 中的 Tensor I，从 LMEM 中读出 Tensor I
Step2: TMU 中的 GetMaxExp 模块根据相关配置信号，将 Tensor I 进行指数对齐操作，找到对应的每列数据最大指数，组成向量 Vector Exp
Step3: 向 LMEM-CIM 中写入最大指数向量 Vector Exp
Step4: 根据 byte_base_out 寻址到存储在 LMEM 中的 Tensor O，从 LMEM 的 byte_base_in 地址读出 Tensor I，根据 Vector Exp 对尾数进行移位对齐，并写入 Tensor O 位置，结束执行。
LMEM 中存储着 `Tensor I`、`Tensor O Exp.` 和 `Tensor O`。
1.  **Step 1 (Read Tensor I):**
    *   TMU 从 LMEM 读取 `Tensor I` (根据 `byte_base_in`)。
    *   数据流向：`Tensor I` (LMEM) -- step1 --> TMU
2.  **Step 2 (Get Max Exponent):**
    *   TMU 将 `Tensor I` 送入 `GetMaxExp` 模块，进行指数对齐，生成 `Vector Exp`。
    *   数据流向：`Tensor I` (TMU) -- step2 --> `GetMaxExp` --> `Vector Exp` (TMU)
3.  **Step 3 (Write Vector Exp):**
    *   TMU 将 `Vector Exp` 写入 LMEM-CIM。
    *   数据流向：`Vector Exp` (TMU) -- step3 --> `Tensor O Exp.` (LMEM)
4.  **Step 4 (Read and Align):**
    *   TMU 从 LMEM 读取 `Tensor I` (根据 `byte_base_in`)。
    *   TMU 的 `Align Shift` 模块根据 `Vector Exp` 对 `Tensor I` 的尾数进行移位对齐。
    *   对齐后的数据写入 LMEM 中的 `Tensor O` (根据 `byte_base_out`)。
    *   数据流向：`Tensor I` (LMEM) -- step4 --> `Align Shift` (TMU) -- step4 --> `Tensor O` (LMEM)
    *   数据流向：`Tensor O Exp.` (LMEM) -- step4 (作为控制信号) --> `Align Shift` (TMU)
### **矩阵转置向量对齐操作，TRANS-ALIGN**
ALIGN 操作完成如下五步操作:
Step1: 根据 byte_base_in 寻址到存储在 LMEM 中的 Tensor I，从 LMEM 中读出 Tensor I
Step2: TMU 中的 Transpose 模块根据相关配置信号，将 Tensor I 进行转置操作，得到转置后的 Tensor Ot
Step3: 根据相关配置信号，对 Tensor Ot 进行指数对齐操作，找到对应的每列数据最大指数，组成向量 Vector Exp
Step4: 向 LMEM-CIM 中写入最大指数向量 Vector Exp
Step5: 再次产生 Tensor Ot，根据 Vector Exp 对尾数进行移位对齐，并写入 Tensor O 位置，结束执行。
LMEM 中存储着 `Tensor I`、`Tensor O Exp.` 和 `Tensor O`。
1.  **Step 1 (Read Tensor I):**
    *   TMU 从 LMEM 读取 `Tensor I` (根据 `byte_base_in`)。
    *   数据流向：`Tensor I` (LMEM) -- step1 --> TMU
2.  **Step 2 (Transpose):**
    *   TMU 将 `Tensor I` 送入 `Transpose` 模块，得到转置后的 `Tensor Ot`。
    *   数据流向：`Tensor I` (TMU) -- step2 --> `Transpose` --> `Tensor Ot` (TMU)
3.  **Step 3 (Get Max Exponent):**
    *   TMU 将 `Tensor Ot` 送入 `GetMaxExp` 模块，进行指数对齐，生成 `Vector Exp`。
    *   数据流向：`Tensor Ot` (TMU) -- step3 --> `GetMaxExp` --> `Vector Exp` (TMU)
4.  **Step 4 (Write Vector Exp):**
    *   TMU 将 `Vector Exp` 写入 LMEM-CIM。
    *   数据流向：`Vector Exp` (TMU) -- step4 --> `Tensor O Exp.` (LMEM)
5.  **Step 5 (Align and Write):**
    *   TMU 再次产生 `Tensor Ot` (可能缓存或重新从 Step 2 获取)。
    *   TMU 的 `Align Shift` 模块根据 `Vector Exp` 对 `Tensor Ot` 的尾数进行移位对齐。
    *   对齐后的数据写入 LMEM 中的 `Tensor O` (根据 `byte_base_out`)。
    *   数据流向：`Tensor Ot` (TMU) -- step5 --> `Align Shift` (TMU) -- step5 --> `Tensor O` (LMEM)
    *   数据流向：`Tensor O Exp.` (LMEM) -- step4 (作为控制信号) --> `Align Shift` (TMU)

---
