# CMD_DECODER 模块文档

## 目录

1. [简介](#简介)
2. [架构](#架构)
3. [功能](#功能)
4. [数据结构](#数据结构)
5. [主要函数](#主要函数)
6. [使用示例](#使用示例)
7. [测试](#测试)
8. [注意事项](#注意事项)

## 简介

CMD_DECODER 模块是一个用于解码指令的组件，主要用于处理和解析来自指令队列的原始命令数据。该模块支持 RV32 和 RV64 指令集，能够解析各种指令类型，并提供了丰富的辅助函数用于指令的生成和处理。

## 架构

CMD_DECODER 模块主要包含以下组件：

1. 指令解码器（decode 函数）
2. 指令组解码器（decodeGroup 函数）
3. 指令名称解码器（decodeName 函数）
4. 指令生成器（generateInstruction 函数）
5. 原始命令生成器（generateRawCommand 函数）
```mermaid
graph TD
A[原始命令] --> B[CMD_DECODER]
B --> C[解码后的指令]
B --> D[指令组]
B --> E[指令名称]
F[指令参数] --> G[指令生成器]
G --> H[原始命令生成器]
H --> A
```

## 功能

1. 解码原始命令数据，提取各个字段的信息
2. 识别指令组和指令名称
3. 生成有效的指令和原始命令数据
4. 支持 RV32 和 RV64 指令集
5. 提供丰富的辅助函数用于指令处理和测试

## 数据结构

### Command 结构体
```cpp
struct Command {
std::conditional_t<NPUConfig::RV_XLEN == 32, uint32_t, uint64_t> rs2val;
std::conditional_t<NPUConfig::RV_XLEN == 32, uint32_t, uint64_t> rs1val;
uint8_t func7;
uint8_t rs2;
uint8_t rs1;
bool xd;
bool xs1;
bool xs2;
uint8_t rd;
uint8_t opcode;
};
```

### LmemAddr 结构体
```cpp
struct LmemAddr {
uint64_t zero : 5;
uint64_t lmem_offset : NPUConfig::LMEM_OFFSET;
uint64_t lmem_index : NPUConfig::LMEM_INDEX;
uint64_t reserved : (64 - 5 - NPUConfig::LMEM_OFFSET - NPUConfig::LMEM_INDEX);
};
```

## 主要函数

1. `Command decode(const uint8_t* cmd_ptr)`：解码原始命令数据
2. `InstructionGroup decodeGroup(const uint8_t& func7)`：解码指令组
3. `InstructionName decodeName(const uint8_t& func7)`：解码指令名称
4. `uint32_t generateInstruction(uint8_t func7, uint8_t xd, uint8_t xs1, uint8_t xs2)`：生成指令
5. `std::vector<uint8_t> generateRawCommand(uint64_t rs2val, uint64_t rs1val, uint32_t instruction)`：生成原始命令数据
6. `LmemAddr parseLmemAddr(uint64_t addr)`：解析 LMEM 地址
7. `uint64_t constructLmemAddr(uint32_t lmem_index, uint32_t lmem_offset)`：构造 LMEM 地址

## 使用示例
```cpp
// 解码原始命令
uint8_t raw_command[16] = {...}; // 原始命令数据
Command cmd = decode(raw_command);
// 生成指令
uint32_t instruction = generateInstruction(0b0000001, 1, 1, 0);
// 生成原始命令
uint64_t rs2val = 0x1234567890ABCDEF;
uint64_t rs1val = 0xFEDCBA9876543210;
auto raw_cmd = generateRawCommand(rs2val, rs1val, instruction);
// 解析 LMEM 地址
uint64_t addr = 0x1234567890ABCDEF;
LmemAddr lmem_addr = parseLmemAddr(addr);
```

## 测试

CMD_DECODER 模块的测试主要包括以下方面：

1. 指令解码测试
2. 指令组和指令名称解码测试
3. 指令生成测试
4. 原始命令生成测试
5. LMEM 地址解析和构造测试
6. 边界条件和异常情况测试

详细的测试用例可以参考 `test_decoder.cpp` 文件。
测试输出
```log
[==========] Running 16 tests from 2 test suites.
[----------] Global test environment set-up.
[----------] 6 tests from CmdDecoderTest
[ RUN      ] CmdDecoderTest.InvalidFunc7
[       OK ] CmdDecoderTest.InvalidFunc7 (0 ms)
[ RUN      ] CmdDecoderTest.DecodeFunction
[       OK ] CmdDecoderTest.DecodeFunction (0 ms)
[ RUN      ] CmdDecoderTest.DecodeEncodeNameConsistency
[       OK ] CmdDecoderTest.DecodeEncodeNameConsistency (0 ms)
[ RUN      ] CmdDecoderTest.LmemAddrTest
[       OK ] CmdDecoderTest.LmemAddrTest (0 ms)
[ RUN      ] CmdDecoderTest.GenerateCommandFunction
[       OK ] CmdDecoderTest.GenerateCommandFunction (0 ms)
[ RUN      ] CmdDecoderTest.DecodeFunctionPerformance
Average decode time: 0.549441 microseconds
[       OK ] CmdDecoderTest.DecodeFunctionPerformance (549 ms)
[----------] 6 tests from CmdDecoderTest (550 ms total)

[----------] 10 tests from ValidFunc7Values/DecodeGroupTest
[ RUN      ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/0
[       OK ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/0 (0 ms)
[ RUN      ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/1
[       OK ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/1 (0 ms)
[ RUN      ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/2
[       OK ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/2 (0 ms)
[ RUN      ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/3
[       OK ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/3 (0 ms)
[ RUN      ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/4
[       OK ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/4 (0 ms)
[ RUN      ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/5
[       OK ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/5 (0 ms)
[ RUN      ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/6
[       OK ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/6 (0 ms)
[ RUN      ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/7
[       OK ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/7 (0 ms)
[ RUN      ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/8
[       OK ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/8 (0 ms)
[ RUN      ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/9
[       OK ] ValidFunc7Values/DecodeGroupTest.ValidFunc7/9 (0 ms)
[----------] 10 tests from ValidFunc7Values/DecodeGroupTest (0 ms total)

[----------] Global test environment tear-down
[==========] 16 tests from 2 test suites ran. (550 ms total)
[  PASSED  ] 16 tests.
```
## 注意事项

1. 确保正确设置 `NPUConfig::RV_XLEN`，以支持 RV32 或 RV64 指令集。
2. 在使用 `generateInstruction` 和 `generateRawCommand` 函数时，注意参数的有效性。
3. 解析 LMEM 地址时，需要注意 `NPUConfig::LMEM_OFFSET` 和 `NPUConfig::LMEM_INDEX` 的配置。
5. 定期检查和更新支持的指令集，以适应可能的扩展和变化。