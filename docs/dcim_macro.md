1. 概述
DCIM_MACRO 是一个基于 SystemC/TLM 的计算宏模块，主要用于处理不同数据类型的矩阵运算。该模块支持多种输入、权重和输出数据类型，具有可配置性和灵活性。
2. 架构设计
2.1 模块接口
https://nanocorechip.feishu.cn/sync/EvhUdabgYsoDOLbitDbcTisbnSf
模块提供三个 TLM 接口：
initiator：用于输出计算结果
input_target：用于接收输入数据
weight_target：用于接收权重数据
2.2 内部存储
模块包含以下内部存储单元：
sram_wt[64][33]：权重数据存储，最大支持 64 列权重数据
sram_in[32]：输入数据存储，支持 32 个输入数据
output_data[64]：输出数据缓冲区，最大支持 64 个输出结果
3. 数据类型支持
3.1 输入数据类型 (InputType)
包含输入特征图和权重类型
enum class InputType: uint8_t{
    INT4 = 0B0000,
    INT8 = 0B0001,
    INT12 = 0B0010,
    INT16 = 0B0011,
    FP32 = 0B0100,
    FP16 = 0B0101,
    BF16 = 0B0110,
    BBF16 = 0B0111,
    FP8_E4M3 = 0B1000,
    FP8_E5M2 = 0B1001
};
3.2 输出数据类型 (OutputDataType)
enum class OutputDataType: uint8_t{
    INT32 = 0,
    FP32 = 1
};
3.3 权重大小映射
根据权重数据类型对应的权重大小：
INT4: 64 列
INT8: 32 列
INT12/INT16/FP32/FP16/BF16: 16 列
BBF16/FP8_E4M3/FP8_E5M2: 32 列
因为权重矩阵固定为32行*256bit，所以不同的类型，其列数不一样，通过pair和get_weight_size进行映射以获取实际列数。
static constexpr std::array<std::pair<InputType, uint8_t>, 10> weight_size_pairs = {{
    {InputType::INT4, 64},
    {InputType::INT8, 32},
    {InputType::INT12, 16},
    {InputType::INT16, 16},
    {InputType::FP32, 16},
    {InputType::FP16, 16},
    {InputType::BF16, 16},
    {InputType::BBF16, 32},
    {InputType::FP8_E4M3, 32},
    {InputType::FP8_E5M2, 32}
}};

static inline uint8_t get_weight_size(InputType type) {
    auto it = std::find_if(weight_size_pairs.begin(), weight_size_pairs.end(),
                          [type](const auto& pair) { return pair.first == type; });
    return it != weight_size_pairs.end() ? it->second : 0;
}
4. 数据结构
4.1 配置格式
struct ConfigFormat{

    InputType input_type;
    InputType weight_type;
    OutputDataType output_type;

    ConfigFormat(InputType in_type = InputType::INT4,
                InputType wt_type = InputType::INT4,
                OutputDataType out_type = OutputDataType::INT32)
        : input_type(in_type), weight_type(wt_type), output_type(out_type) {}
};
由于config是通过cim_cluster广播到每一个cim_engine以及内部的macro，其macro类中config由其上层engine进行配置，engine由cluster进行配置。
4.2 数据包格式
//权重数据
struct  DCIM_MACRO_WeightPayload {
    std::array<std::array<uint16_t, 33>, 64> weight_data;
};
//输入数据
struct  DCIMInputPayload {
     std::array<uint16_t, 32> input_data;
};
//输出数据
struct DCIMOutputPayload {
     std::array<uint32_t, 64> output_data;
};

其中权重并不需要每次都进行配置，故将权重和输入分开通过TLM进行传输；
输出考虑到macro spec中固定位宽为32bit，其输出有效通道最大为64（INT4）；实际有效通道根据权重类型进行映射，例如位宽为8，则只有低32个有效。
5. 工作流程
暂时无法在飞书文档外展示此内容
1. 类初始化，weight sram初始化为全0
2. 通过engine对内部macro进行config配置，接收weight_TLM数据配置权重
3. 接收输入TLM，计算矩阵结果，并输出到DCIMOutputPayload ，通过TLM传输到engine的target
4. 当2配置后，可以直接通过3接收输入进行计算输出，也可以重新进行2配置权重或者config
6. 测试框架
测试框架提供了完整的测试用例，支持：
多种数据类型组合测试
自动化测试流程
结果可视化输出
6.1 支持的测试用例
std::vector<std::pair<InputType, InputType>> test_cases = {
            {InputType::INT4, InputType::FP16},
            {InputType::INT4, InputType::BF16},
            {InputType::INT4, InputType::BBF16},
            {InputType::INT4, InputType::FP8_E4M3},
            {InputType::INT4, InputType::FP8_E5M2},
            {InputType::INT4, InputType::INT16},
            {InputType::INT8, InputType::INT8},
            {InputType::INT4, InputType::INT4}
        };
6.2 使用示例
...
void test_process() {
        std::vector<std::pair<InputType, InputType>> test_cases = {
            {InputType::INT4, InputType::FP16},
            {InputType::INT4, InputType::BF16},
            {InputType::INT4, InputType::BBF16},
            {InputType::INT4, InputType::FP8_E4M3},
            {InputType::INT4, InputType::FP8_E5M2},
            {InputType::INT4, InputType::INT16},
            {InputType::INT8, InputType::INT8},
            {InputType::INT4, InputType::INT4}
        };

        for (const auto& test_case : test_cases) {
            run_test_case(test_case.first, test_case.second, OutputDataType::FP32);
            wait(10, SC_NS);
        }

        sc_stop();
    }

    void run_test_case(InputType in_type, InputType wt_type, OutputDataType out_type) {
    std::cout << "\n=== Running test case ===" << std::endl;
    std::cout << "Input type: " << static_cast<int>(in_type) << std::endl;
    std::cout << "Weight type: " << static_cast<int>(wt_type) << std::endl;
    std::cout << "Output type: " << static_cast<int>(out_type) << std::endl;

    // 准备输入数据包
    DCIMInputPayload input_payload;
    DCIM_MACRO_WeightPayload config_payload;
    ConfigFormat config{in_type, wt_type, out_type};
    dcim_macro.setConfig(config);

    // 填充输入数据
    const uint16_t* in_data = get_input_data(in_type,wt_type);
    
    std::copy(in_data, in_data + 32, input_payload.input_data.begin());
    
    // 填充权重数据
    const uint16_t (*wt_data)[33] = get_weight_data(wt_type);
    wt_size = get_weight_size(wt_type);
    
    // 只复制需要的权重数据
    for(int i = 0; i < wt_size; i++) {
        std::copy(wt_data[i], wt_data[i] + 33, config_payload.weight_data[i].begin());
    }
   
    // 准备TLM传输
    tlm::tlm_generic_payload trans;
    sc_time delay = sc_time(0, SC_NS);
    
    // 设置传输属性
    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_data_ptr(reinterpret_cast<unsigned char*>(&config_payload));
    trans.set_data_length(sizeof(DCIM_MACRO_WeightPayload));
    trans.set_streaming_width(sizeof(DCIM_MACRO_WeightPayload));
    trans.set_byte_enable_ptr(0);
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

    // 发送权重
    config_socket->b_transport(trans, delay);
    wait(SC_ZERO_TIME);
    // 发送输入
    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_data_ptr(reinterpret_cast<unsigned char*>(&input_payload));
    trans.set_data_length(sizeof(DCIMInputPayload));
    trans.set_streaming_width(sizeof(DCIMInputPayload));
    trans.set_byte_enable_ptr(0);
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
    initiator_socket->b_transport(trans, delay);
    }
    ...
完整测试代码见文件test_dcim_macro.cpp
输出如下：与dcim_macro_cmodel进行对比
=== Running test case ===
Input type: 0
Weight type: 5
Output type: 1
TestBench output data:
  -7407.062500    7067.562500    7496.812500    5558.500000  -11170.750000   10178.000000    -663.062500    4091.187500    9444.375000   12711.687500  -10941.562500    7509.375000     729.375000  -16772.500000   14192.687500   20117.312500

=== Running test case ===
Input type: 0
Weight type: 6
Output type: 1
TestBench output data:
     20.656250     695.843750   -2635.296875    -234.437500    2978.609375    1607.937500   -1302.453125    5370.062500     818.921875   -2215.140625    2698.750000    2507.562500   -3227.234375    -838.523438    -632.453125    1133.062500

=== Running test case ===
Input type: 0
Weight type: 7
Output type: 1
TestBench output data:
  -2800.000000   -1468.000000   -3428.000000   -4800.000000   -5256.000000   -1116.000000   -2284.000000   -3592.000000   -3900.000000     228.000000    -928.000000   -3828.000000    -184.000000    1604.000000    7072.000000   -2704.000000
  -1712.000000     -44.000000   -1960.000000     220.000000     564.000000   -1434.000000    1940.000000     640.000000      66.000000    -812.000000   -2184.000000     473.000000   -2352.000000    1284.000000     292.000000     653.000000

=== Running test case ===
Input type: 0
Weight type: 8
Output type: 1
TestBench output data:
    300.500000      24.750000     401.000000      15.250000      42.250000     -43.250000    -714.000000      64.250000     -69.000000      17.500000     353.500000     387.000000     -99.500000     -46.500000     111.250000     -82.500000
    -62.000000     286.750000    -128.750000     316.250000     507.250000    -374.750000    -286.000000     260.250000     117.750000     301.500000       3.250000     174.250000    -526.250000     -25.500000     -52.750000     451.250000

=== Running test case ===
Input type: 0
Weight type: 9
Output type: 1
TestBench output data:
    433.000000    -213.500000      -5.000000     373.500000    -563.500000     102.250000     136.000000     215.500000    -323.500000     161.000000      78.000000     225.000000     173.000000     226.000000     285.500000     -89.500000
      3.000000    -227.000000     184.500000     448.500000     101.000000    -197.000000      94.750000     482.000000    -377.000000    -327.500000    -229.000000      50.875000    -203.000000     354.000000     254.000000     -82.500000

=== Running test case ===
Input type: 0
Weight type: 3
Output type: 1
TestBench output data:
  22510.000000  472688.000000  -79883.000000 -719608.000000 -176443.000000 -145898.000000 -134511.000000  -83404.000000 -462378.000000 -134711.000000  -86384.000000 -307702.000000  926095.000000 -149405.000000 -391029.000000  502522.000000

=== Running test case ===
Input type: 1
Weight type: 1
Output type: 1
TestBench output data:
 -17074.000000   37057.000000    1804.000000   25915.000000    4721.000000   18634.000000    4846.000000   54618.000000    2165.000000  -24989.000000  -20350.000000  -38023.000000   34375.000000  -66237.000000  -33550.000000  -13803.000000
  27653.000000  -13131.000000   22694.000000  -16224.000000    1956.000000   42464.000000  -24523.000000  -32864.000000  -48912.000000   19873.000000   48209.000000   29431.000000   37205.000000   -9421.000000  -34209.000000    8753.000000

=== Running test case ===
Input type: 0
Weight type: 0
Output type: 1
TestBench output data:
   -137.000000      58.000000     -99.000000      31.000000     213.000000      83.000000    -142.000000    -150.000000     -98.000000     109.000000       6.000000      77.000000    -145.000000     -37.000000      89.000000     246.000000
    255.000000     -78.000000      -5.000000      13.000000     -79.000000     164.000000      -7.000000     -54.000000     -90.000000     -39.000000    -137.000000      57.000000      54.000000       6.000000     -94.000000     -89.000000
     34.000000      16.000000     141.000000     127.000000     -44.000000      79.000000      22.000000     -49.000000    -139.000000     195.000000      79.000000     -43.000000     168.000000    -160.000000      87.000000     -97.000000
    156.000000     132.000000    -257.000000      18.000000      -7.000000      51.000000       0.500000     147.000000      -8.000000      38.000000     -79.000000      24.000000    -184.000000      39.000000     -62.000000     321.000000
