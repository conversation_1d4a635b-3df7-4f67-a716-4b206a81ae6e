# Scoreboard 模块文档

## 目录

1. [简介](#简介)
2. [架构](#架构)
3. [特性](#特性)
4. [安装](#安装)
5. [使用方法](#使用方法)
6. [API 参考](#api-参考)
7. [数据结构](#数据结构)
9. [测试](#测试)
10. [注意事项](#注意事项)

## 简介

Scoreboard 是一个为 SystemC 仿真环境设计的模块,用于管理本地内存(LMEM)条目的状态。它使用 TLM-2.0 进行通信,提供了更新和读取 LMEM 条目状态的方法。

## 架构

```mermaid
graph LR
A[Initiator Module] -->|TLM-2.0 Socket| B[Scoreboard]
B -->|Internal Storage| C[LMEM Entries]
B -->|Read/Write| C
```

在这个架构中:
- Initiator Module 代表发起更新或读取请求的模块
- Scoreboard 是我们的核心模块,它通过 TLM-2.0 Socket 接收请求
- LMEM Entries 是 Scoreboard 内部存储的数据结构,用于保存 LMEM 状态信息

## 特性

- 支持多个 LMEM 条目的状态管理
- 使用 TLM-2.0 进行通信
- 提供更新和读取 LMEM 条目状态的方法

## 安装

将 `scoreboard.h` 和 `scoreboard.cpp` 文件添加到项目中,并确保它们在编译路径中。

## 使用方法

1. 包含头文件:
   ```cpp
   #include "scoreboard.h"
   ```

2. 创建 Scoreboard 实例:
   ```cpp
   scoreboard sbd("scoreboard_instance");
   ```

3. 使用 TLM-2.0 socket 连接到 Scoreboard:
   ```cpp
   your_module.socket.bind(sbd.socket);
   ```

4. 发送更新或读取事务:
   ```cpp
   // 更新事务
   scoreboardUpdate update;
   // 设置更新数据...
   send_update_transaction(update);

   // 读取事务
   scoreboardData data = send_read_transaction();
   ```

## API 参考

### scoreboard 类

#### 构造函数
```cpp
scoreboard(const sc_core::sc_module_name &name);
```

#### 公共方法

- `void b_transport(tlm::tlm_generic_payload &trans, sc_core::sc_time &delay)`
- `void read_scoreboard(scoreboardData &data)`
- `void print_scoreboard() const`
- `void set_scoreboard_state(uint32_t lmem_index, LMemStatus status, FunctionUnitType fu_id = FunctionUnitType::INVALID, uint8_t ch_util = 0)`
- `void reset()`

## 数据结构

### LMemEntry
```cpp
struct LMemEntry {
sc_dt::sc_uint<2> status;
sc_dt::sc_uint<NPUConfig::FU_ID_WD> fu_id;
sc_dt::sc_uint<3> ch_util;
sc_dt::sc_uint<NPUConfig::CIMC_MODE_WD> cimc_mode;
};
```

### scoreboardUpdate
```cpp
struct scoreboardUpdate {
sc_dt::sc_uint<NPUConfig::LMEM_NUM> lmem_id;
std::array<LMemEntry, NPUConfig::LMEM_NUM> lmem_entries;
sc_dt::sc_uint<1> update_status;
sc_dt::sc_uint<1> update_fu_id;
sc_dt::sc_uint<1> update_ch_util;
sc_dt::sc_uint<1> update_cimc_mode;
};
```

### scoreboardData
```cpp
struct scoreboardData {
std::array<LMemEntry, NPUConfig::LMEM_NUM> lmem_entries;
};
```

## 测试
```cpp
#include "scoreboard.h"
#include "utils/systemc_logger.h"
#include <tlm_utils/simple_initiator_socket.h>
#include <vector>
#include <thread>
#include <chrono>
class TestBench : public sc_core::sc_module {
public:
    tlm_utils::simple_initiator_socket<TestBench> socket;
    explicit TestBench(const sc_core::sc_module_name& name) : sc_core::sc_module(name) {
        SC_THREAD(run_test);
    }

private:
    void run_test() {
        sc_core::wait(sc_core::SC_ZERO_TIME);

        test_single_update();
        test_multiple_updates();
        test_boundary_conditions();
        test_error_handling();
        test_concurrent_access();
        test_performance();
        test_functional_completeness();
        test_consistency();

        info_sc("All tests completed.");
    }

    void test_single_update() {
        scoreboardUpdate update;
        update.lmem_id = 1;  // Update LMEM 0
        update.update_status = 1;
        update.lmem_entries[0].status = LMemStatus::BUSY;

        send_update_transaction(update);
        scoreboardData readData = send_read_transaction();
        info_sc(scoreboard_data_string(readData).c_str());

        sc_assert(readData.lmem_entries[0].status == LMemStatus::BUSY);
        info_sc("Single update test passed.");
    }

    void test_multiple_updates() {
        scoreboardUpdate update;
        update.lmem_id = 0b11;  // Update LMEM 0 and 1
        update.update_status = 1;
        update.update_fu_id = 1;
        update.lmem_entries[0].status = LMemStatus::BUSY;
        update.lmem_entries[0].fu_id = 2;
        update.lmem_entries[1].status = LMemStatus::READY;
        update.lmem_entries[1].fu_id = 3;

        send_update_transaction(update);
        scoreboardData readData = send_read_transaction();
        sc_assert(readData.lmem_entries[0].status == LMemStatus::BUSY);
        sc_assert(readData.lmem_entries[0].fu_id == 2);
        sc_assert(readData.lmem_entries[1].status == LMemStatus::READY);
        sc_assert(readData.lmem_entries[1].fu_id == 3);
        info_sc("Multiple updates test passed.");
    }

    void test_boundary_conditions() {
        // Test updating all LMEMs
        scoreboardUpdate update;
        update.lmem_id = (1 << NPUConfig::LMEM_NUM) - 1;
        update.update_status = 1;
        for (int i = 0; i < NPUConfig::LMEM_NUM; ++i) {
            update.lmem_entries[i].status = LMemStatus::BUSY;
        }
        send_update_transaction(update);
        scoreboardData readData = send_read_transaction();
        for (int i = 0; i < NPUConfig::LMEM_NUM; ++i) {
            sc_assert(readData.lmem_entries[i].status == LMemStatus::BUSY);
        }

        // Test updating no LMEMs
        update.lmem_id = 0;
        send_update_transaction(update);
        scoreboardData newReadData = send_read_transaction();
        for (int i = 0; i < NPUConfig::LMEM_NUM; ++i) {
            sc_assert(newReadData.lmem_entries[i].status == readData.lmem_entries[i].status);
        }

        info_sc("Boundary conditions test passed.");
    }

    void test_error_handling() {
        // This test depends on how error handling is implemented in the scoreboard
        // For example, you might test invalid LMEM_ID or status values
        info_sc("Error handling test not implemented.");
    }

    void test_concurrent_access() {
        std::vector<std::thread> threads;
        threads.reserve(10);
for (int i = 0; i < 10; ++i) {
            threads.emplace_back([this, i]() {
                scoreboardUpdate update;
                update.lmem_id = 1 << (i % NPUConfig::LMEM_NUM);
                update.update_status = 1;
                update.lmem_entries[i % NPUConfig::LMEM_NUM].status = LMemStatus::BUSY;
                send_update_transaction(update);
                send_read_transaction();
            });
        }
        for (auto& thread : threads) {
            thread.join();
        }
        info_sc("Concurrent access test completed.");
    }

    void test_performance() {
        auto start = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 1000; ++i) {
            scoreboardUpdate update;
            update.lmem_id = 1 << (i % NPUConfig::LMEM_NUM);
            update.update_status = 1;
            update.lmem_entries[i % NPUConfig::LMEM_NUM].status = LMemStatus::BUSY;
            send_update_transaction(update);
        }
        auto end = std::chrono::high_resolution_clock::now();
        std::chrono::duration<double, std::milli> elapsed = end - start;
        info_sc("Performance test: 1000 updates in %f ms", elapsed.count());
    }

    void test_functional_completeness() {
        // Test all possible state transitions
        std::vector<LMemStatus> states = {LMemStatus::IDLE, LMemStatus::READY, LMemStatus::BUSY, LMemStatus::LOCK};
            for (auto to_state : states) {
                scoreboardUpdate update;
                update.lmem_id = 1;
                update.update_status = 1;
                update.lmem_entries[0].status = to_state;
                send_update_transaction(update);
                scoreboardData readData = send_read_transaction();
                sc_assert(readData.lmem_entries[0].status == to_state);
            }

        // Test CIMC mode update (only for CIM Cluster)
        scoreboardUpdate update;
        update.lmem_id = 1 << (NPUConfig::LMEM_NUM - 1);  // Last LMEM is CIM Cluster
        update.update_cimc_mode = 1;
        update.lmem_entries[NPUConfig::LMEM_NUM - 1].cimc_mode = 2;
        send_update_transaction(update);
        scoreboardData readData = send_read_transaction();
        sc_assert(readData.lmem_entries[NPUConfig::LMEM_NUM - 1].cimc_mode == 2);

        info_sc("Functional completeness test passed.");
    }

    void test_consistency() {
        scoreboardUpdate update;
        update.lmem_id = 0b11;  // Update LMEM 0 and 1
        update.update_status = 1;
        update.update_fu_id = 1;
        update.lmem_entries[0].status = LMemStatus::BUSY;
        update.lmem_entries[0].fu_id = 2;
        update.lmem_entries[1].status = LMemStatus::READY;
        update.lmem_entries[1].fu_id = 3;

        send_update_transaction(update);
        scoreboardData readData = send_read_transaction();
        sc_assert(readData.lmem_entries[0].status == LMemStatus::BUSY);
        sc_assert(readData.lmem_entries[0].fu_id == 2);
        sc_assert(readData.lmem_entries[1].status == LMemStatus::READY);
        sc_assert(readData.lmem_entries[1].fu_id == 3);
        info_sc("Consistency test passed.");
    }

    void send_update_transaction(const scoreboardUpdate& update) {
        tlm::tlm_generic_payload trans;
        sc_core::sc_time delay = sc_core::SC_ZERO_TIME;

        trans.set_command(tlm::TLM_WRITE_COMMAND);
        trans.set_data_ptr(reinterpret_cast<unsigned char*>(const_cast<scoreboardUpdate*>(&update)));
        trans.set_data_length(sizeof(scoreboardUpdate));
        trans.set_streaming_width(sizeof(scoreboardUpdate));
        trans.set_byte_enable_ptr(0);
        trans.set_dmi_allowed(false);
        trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

        socket->b_transport(trans, delay);

        if (trans.is_response_error()) {
            SC_REPORT_ERROR("TLM-2", "Response error from b_transport");
        }
    }

    auto send_read_transaction() -> scoreboardData {
        scoreboardData readData;
        tlm::tlm_generic_payload trans;
        sc_core::sc_time delay = sc_core::SC_ZERO_TIME;
        trans.set_command(tlm::TLM_READ_COMMAND);
        trans.set_data_ptr(reinterpret_cast<unsigned char*>(&readData));
        trans.set_data_length(sizeof(scoreboardData));
        trans.set_streaming_width(sizeof(scoreboardData));
        trans.set_byte_enable_ptr(0);
        trans.set_dmi_allowed(false);
        trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

        socket->b_transport(trans, delay);

        if (trans.is_response_error()) {
            SC_REPORT_ERROR("TLM-2", "Response error from b_transport");
        }

        return readData;
    }
};

SC_MODULE(Top) {
    scoreboard scoreboard;
    TestBench testbench;

    SC_CTOR(explicit Top) : scoreboard("scoreboard"), testbench("testbench") {
        testbench.socket.bind(scoreboard.socket);
    }
};

int sc_main(int argc, char* argv[]) {
    g_logger.setLogLevel(SystemCLogger::SC_INFO);
    Top top("top");
    sc_core::sc_start();
    return 0;
}



```
输出
```
[INFO   ] [0 s       ] scoreboard status:
LMEM 0: status=BUSY, fu_id=LMU, ch_util=0, cimc_mode=0
LMEM 1: status=IDLE, fu_id=LMU, ch_util=0, cimc_mode=0
LMEM 2: status=IDLE, fu_id=LMU, ch_util=0, cimc_mode=0
LMEM 3: status=IDLE, fu_id=LMU, ch_util=0, cimc_mode=0
LMEM 4: status=IDLE, fu_id=LMU, ch_util=0, cimc_mode=0

[INFO   ] [0 s       ] Single update test passed.
[INFO   ] [0 s       ] Multiple updates test passed.
[INFO   ] [0 s       ] Boundary conditions test passed.
[INFO   ] [0 s       ] Error handling test not implemented.
[INFO   ] [0 s       ] Concurrent access test completed.
[INFO   ] [0 s       ] Performance test: 1000 updates in 1.113000 ms
[INFO   ] [0 s       ] Functional completeness test passed.
[INFO   ] [0 s       ] Consistency test passed.
[INFO   ] [0 s       ] All tests completed.
```

## 注意事项

- 确保在使用记分板之前正确初始化和连接 TLM-2.0 socket。
- 记分板的 LMEM 条目数量由 `NPUConfig::LMEM_NUM` 定义,请确保与系统配置一致。
- 需要加入依赖库magic_enum