# RSP_ROB 模块文档

## 目录

1. [简介](#简介)
2. [架构](#架构)
3. [特性](#特性)
4. [安装](#安装)
5. [使用方法](#使用方法)
6. [API 参考](#api-参考)
7. [数据结构](#数据结构)
8. [测试](#测试)
9. [注意事项](#注意事项)

## 简介

RSP_ROB（Response Reorder Buffer）是一个为 SystemC 仿真环境设计的模块，用于管理和重排来自不同功能单元的响应。它使用 TLM-2.0 进行通信，提供了处理来自写回队列（WBQ）和指令分发器的响应的方法。

## 架构

```mermaid
graph TD
    A[WBQ Response Socket] -->|TLM-2.0| B[RSP_ROB]
    C[Disp Roflag Socket] -->|TLM-2.0| B
    B -->|TLM-2.0| D[RSPQ Socket]
    B -->|Signal| E[Term Sync]
    F[WBQ Response Valid] -->|Signal| B
    B -->|Signal| G[WBQ Response Ready]
    H[Disp Roflag Valid] -->|Signal| B
    B -->|Signal| I[Disp Roflag Ready]
    B -->|Signal| J[RSPQ Valid]
    K[RSPQ Ready] -->|Signal| B
```

在这个架构中:
- WBQ Response Socket 和 Disp Roflag Socket 是输入接口，用于接收写回队列响应和指令分发标志
- RSPQ Socket 是输出接口，用于发送重排后的响应
- Term Sync 是输出信号，用于终止同步等待状态
- 其他信号用于握手和状态指示
### decoder_roflag
```mermaid
graph TD
    A[开始] --> B{ROB队列为空?}
    B -->|是| C[等待disp_roflag事件]
    C --> B
    B -->|否| D[从队列中获取disp_roflag]
    D --> E[解析disp_roflag数据]
    E --> F[检查wbq_rsp_valid]
    F --> G{是否阻塞?}
    G -->|是| H[等待解除阻塞条件]
    H --> G
    G -->|否| I{sync_flag为1?}
    I -->|是| J[处理同步终止]
    J --> K[发送RSPQ响应]
    K --> L[设置term_sync信号]
    I -->|否| M{inst_xd为1?}
    M -->|是| N{fu_ena为1?}
    N -->|是| O[从WBQ获取数据]
    N -->|否| P[设置默认响应]
    O --> Q[发送RSPQ响应]
    P --> Q
    M -->|否| R[结束本次循环]
    L --> R
    Q --> R
    R --> B
```
## 特性

- 支持多个功能单元的响应管理
- 使用 TLM-2.0 进行通信
- 提供响应重排和同步控制功能
- 实现了阻塞和非阻塞处理机制

## 安装

将 `rsp_rob.h` 和 `rsp_rob.cpp` 文件添加到项目中，并确保它们在编译路径中。

## 使用方法

1. 包含头文件:
   ```cpp
   #include "rsp_rob.h"
   ```

2. 创建 RSP_ROB 实例:
   ```cpp
   RSP_ROB rsp_rob("rsp_rob_instance", queue_size);
   ```

3. 连接 TLM-2.0 socket 和信号:
   ```cpp
   your_module.wbq_socket.bind(rsp_rob.wbq_rsp_socket);
   your_module.disp_socket.bind(rsp_rob.disp_roflag_socket);
   rsp_rob.rspq_socket.bind(your_rspq_module.target_socket);
   // 连接其他信号...
   ```

4. 在仿真中使用 RSP_ROB:
   ```cpp
   // 发送 WBQ 响应
   tlm::tlm_generic_payload trans;
   sc_time delay = SC_ZERO_TIME;
   wbq_rsp_info wbq_data;
   // 设置 wbq_data...
   trans.set_data_ptr(reinterpret_cast<unsigned char*>(&wbq_data));
   trans.set_data_length(sizeof(wbq_rsp_info));
   rsp_rob.wbq_rsp_socket->b_transport(trans, delay);

   // 发送 Disp Roflag
   disp_roflag_info roflag_data;
   // 设置 roflag_data...
   trans.set_data_ptr(reinterpret_cast<unsigned char*>(&roflag_data));
   trans.set_data_length(sizeof(disp_roflag_info));
   rsp_rob.disp_roflag_socket->b_transport(trans, delay);
   ```

## API 参考

### RSP_ROB 类

#### 构造函数
```cpp
RSP_ROB(const sc_core::sc_module_name& name, unsigned int rob_queue_size);
```

#### 公共接口

- `tlm_utils::simple_target_socket<RSP_ROB> wbq_rsp_socket`
- `tlm_utils::simple_target_socket<RSP_ROB> disp_roflag_socket`
- `tlm_utils::simple_initiator_socket<RSP_ROB> rspq_socket`
- `sc_core::sc_out<bool> term_sync`
- `std::array<sc_core::sc_in<bool>, NPUConfig::FU_NUM> wbq_rsp_valid`
- `std::array<sc_core::sc_out<bool>, NPUConfig::FU_NUM> wbq_rsp_ready`
- `sc_core::sc_in<bool> disp_roflag_valid`
- `sc_core::sc_out<bool> disp_roflag_ready`
- `sc_core::sc_out<bool> rspq_valid`
- `sc_core::sc_in<bool> rspq_ready`

## 数据结构

### wbq_rsp_info
```cpp
struct wbq_rsp_info {
  std::array<sc_dt::sc_uint<NPUConfig::RV_XLEN>, NPUConfig::FU_NUM> wbq_rsp_data;
  wbq_rsp_info();
};
```

### disp_roflag_info
```cpp
struct disp_roflag_info {
  sc_dt::sc_uint<8 + NPUConfig::FU_ID_WD> disp_roflag_data;
  disp_roflag_info();
};
```

### rspq_rsp_info
```cpp
struct rspq_rsp_info {
  sc_dt::sc_uint<NPUConfig::RV_XLEN + 5> rspq_rsp_data;
  rspq_rsp_info();
};
```

## 测试


示例测试代码：

```cpp
#include "../include/rsp_rob.h"
#include "npu_config.h"
#include "sysc/communication/sc_signal_ports.h"
#include "sysc/kernel/sc_simcontext.h"
#include "sysc/kernel/sc_time.h"
#include "systemc.h"
#include "tlm.h"
#include "tlm_utils/simple_initiator_socket.h"
#include "tlm_utils/simple_target_socket.h"
#include "utils/systemc_logger.h"
#include <cstdint>
#include <iostream>
#include <random>
class TestModule : public sc_module {
public:
  tlm_utils::simple_initiator_socket<TestModule> disp_socket;
  tlm_utils::simple_initiator_socket<TestModule> wbq_socket;
  tlm_utils::simple_target_socket<TestModule> rspq_socket;

  sc_core::sc_out<bool> disp_roflag_valid;
  sc_core::sc_in<bool> disp_roflag_ready;

  sc_core::sc_in<bool> term_sync;

  sc_core::sc_in<bool> rspq_valid;
  sc_core::sc_out<bool> rspq_ready;

  std::array<sc_core::sc_out<bool>, NPUConfig::FU_NUM> wbq_rsp_valid;
  std::array<sc_core::sc_in<bool>, NPUConfig::FU_NUM> wbq_rsp_ready;

  // SC_HAS_PROCESS(TestModule);
  TestModule(const sc_module_name &name)
      : sc_module(name), term_sync("term_sync") {
    rspq_socket.register_b_transport(this, &TestModule::b_transport_rspq);
    SC_THREAD(test_thread);
  }

  void send_disp_roflag(const disp_roflag_info &disp_roflag) {
    debug_sc("Sending DISP ROFLAG");
    tlm::tlm_generic_payload trans;
    sc_time delay = SC_ZERO_TIME;
    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_data_ptr(reinterpret_cast<unsigned char *>(
        const_cast<disp_roflag_info *>(&disp_roflag)));
    trans.set_data_length(sizeof(disp_roflag_info));
    trans.set_streaming_width(sizeof(disp_roflag_info));
    trans.set_byte_enable_ptr(0);
    trans.set_dmi_allowed(false);
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
    disp_socket->b_transport(trans, delay);
    if (trans.is_response_error()) {
      SC_REPORT_ERROR("TLM-2", "Response error from b_transport");
    }
  }

  void send_wbq_rsp(const wbq_rsp_info &wbq_rsp) {
    tlm::tlm_generic_payload trans;
    sc_time delay = SC_ZERO_TIME;

    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_data_ptr(reinterpret_cast<unsigned char *>(
        const_cast<wbq_rsp_info *>(&wbq_rsp)));
    trans.set_data_length(sizeof(wbq_rsp_info));
    trans.set_streaming_width(sizeof(wbq_rsp_info));
    trans.set_byte_enable_ptr(0);
    trans.set_dmi_allowed(false);
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
    debug_sc("Sending WBQ response");
    wbq_socket->b_transport(trans, delay);

    if (trans.is_response_error()) {
      SC_REPORT_ERROR("TLM-2", "Response error from b_transport");
    }
  }

  void b_transport_rspq(tlm::tlm_generic_payload &trans, sc_time &delay) {
    if (trans.get_command() == tlm::TLM_WRITE_COMMAND) {
      auto *rsp = reinterpret_cast<rspq_rsp_info *>(trans.get_data_ptr());
      cout << "Received RSPQ response: " << hex << rsp->rspq_rsp_data << endl;
      trans.set_response_status(tlm::TLM_OK_RESPONSE);
    } else {
      trans.set_response_status(tlm::TLM_COMMAND_ERROR_RESPONSE);
    }
  }

  void test_thread() {
    // Implement test scenarios here
    test_wbq_blocking_sync_flag();
    test_wbq_blocking_fu_ena();
    test_rspq_blocking_sync_flag();
    test_rspq_blocking_no_sync_flag();
  }

private:
  // 为结构体写入随机值的方法
  void randomize_wbq_rsp_info(wbq_rsp_info &wbq_rsp) {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint64_t> dis(
        0, (1ULL << NPUConfig::RV_XLEN) - 1);

    for (auto &data : wbq_rsp.wbq_rsp_data) {
      data = sc_uint<NPUConfig::RV_XLEN>(dis(gen));
    }
  }
  void test_wbq_blocking_sync_flag() {
    cout << "Testing WBQ blocking with sync flag" << endl;

    // 设置roflag，sync_flag=1
    disp_roflag_info disp_roflag;
    disp_roflag.disp_roflag_data[NPUConfig::FU_ID_WD + 8 - 1] = 1; // sync_flag
    disp_roflag.disp_roflag_data[5] = 1;                           // inst_xd
    rspq_ready.write(true);
    wait(SC_ZERO_TIME);
    send_disp_roflag(disp_roflag);
    // 非rspq堵塞
    // 设置wbq_rsp_valid不全为有效
    wbq_rsp_info wbq_rsp;
    randomize_wbq_rsp_info(wbq_rsp);
    for (auto &valid : wbq_rsp_valid) {
      valid->write(std::rand() % 2 == 0);
    }
    wbq_rsp_valid[0]->write(false);
    send_wbq_rsp(wbq_rsp);
    wait(SC_ZERO_TIME);

    wait(10, SC_NS);
    // 解除堵塞
    for (auto &valid : wbq_rsp_valid) {
      valid->write(true);
    }
    wait(10, SC_NS);
    cout << "WBQ blocking with sync flag test completed" << endl;
  }

  void test_wbq_blocking_fu_ena() {
    cout << "Testing WBQ blocking with fu_ena" << endl;

    // 设置roflag，sync=0, fu_ena=1, inst.xd=1
    disp_roflag_info disp_roflag;
    disp_roflag.disp_roflag_data[NPUConfig::FU_ID_WD + 8 - 1] = 0; // sync_flag
    disp_roflag.disp_roflag_data[NPUConfig::FU_ID_WD + 8 - 2] = 1; // fu_ena
    disp_roflag.disp_roflag_data[5] = 1;                           // inst_xd
    disp_roflag.disp_roflag_data.range(NPUConfig::FU_ID_WD + 8 - 3, 6) =
        2;                         // fuid = 2
    wbq_rsp_valid[2].write(false); // 设置fu_id=2的为false
    wait(SC_ZERO_TIME);
    send_disp_roflag(disp_roflag);

    // 设置wbq_rsp_valid[fu_id]为false
    wbq_rsp_info wbq_rsp;
    randomize_wbq_rsp_info(wbq_rsp);
    send_wbq_rsp(wbq_rsp);

    wait(100, SC_NS); // 等待一段时间

    // 解除堵塞
    wbq_rsp_valid[2].write(true);
    wait(SC_ZERO_TIME);
    cout << "WBQ blocking with fu_ena test completed" << endl;
  }

  void test_rspq_blocking_sync_flag() {
    cout << "Testing RSPQ blocking with sync flag" << endl;

    for (auto &valid : wbq_rsp_valid) {
      valid->write(true);
    }
    // 设置roflag，sync_flag=1, inst.xd=1
    disp_roflag_info disp_roflag;
    disp_roflag.disp_roflag_data[NPUConfig::FU_ID_WD + 8 - 1] = 1; // sync_flag
    disp_roflag.disp_roflag_data[5] = 1;                           // inst_xd

    // 设置rspq_ready为false
    rspq_ready.write(false);
    wait(SC_ZERO_TIME);
    send_disp_roflag(disp_roflag);


    wbq_rsp_info wbq_rsp;
    randomize_wbq_rsp_info(wbq_rsp);
    send_wbq_rsp(wbq_rsp);

    wait(100, SC_NS); // 等待一段时间

    // 解除堵塞
    rspq_ready.write(true);
    wait(SC_ZERO_TIME);
    cout << "RSPQ blocking with sync flag test completed" << endl;
  }

  void test_rspq_blocking_no_sync_flag() {
    cout << "Testing RSPQ blocking without sync flag" << endl;
    for (auto &valid : wbq_rsp_valid) {
      valid->write(true);
    }
    wait(SC_ZERO_TIME);
    // 设置roflag，sync_flag=0, inst.xd=1
    disp_roflag_info disp_roflag;
    disp_roflag.disp_roflag_data[NPUConfig::FU_ID_WD + 8 - 1] = 0; // sync_flag
    disp_roflag.disp_roflag_data[5] = 1;                           // inst_xd
    // 设置rspq_ready为false
    rspq_ready.write(false);
    wait(SC_ZERO_TIME);
    send_disp_roflag(disp_roflag);



    wait(100, SC_NS); // 等待一段时间

    // 解除堵塞
    rspq_ready.write(true);

    wait(100, SC_NS); // 等待处理
    cout << "RSPQ blocking without sync flag test completed" << endl;
  }
};

int sc_main(int argc, char *argv[]) {
  g_logger.setLogLevel(SystemCLogger::SC_INFO);
  // Create modules
  RSP_ROB rsp_rob("rsp_rob", NPUConfig::ROB_QUEUE_SIZE);
  TestModule test_module("test_module");

  // Create signals
  sc_signal<bool> term_sync_signal("term_sync_signal");
  std::array<sc_signal<bool>, NPUConfig::FU_NUM> wbq_rsp_valid_signal;
  std::array<sc_signal<bool>, NPUConfig::FU_NUM> wbq_rsp_ready_signal;
  sc_signal<bool> rspq_valid_signal("rspq_valid_signal");
  sc_signal<bool> rspq_ready_signal("rspq_ready_signal");
  sc_signal<bool> disp_roflag_valid_signal("disp_roflag_valid_signal");
  sc_signal<bool> disp_roflag_ready_signal("disp_roflag_ready_signal");

  // Connect modules
  test_module.disp_socket.bind(rsp_rob.disp_roflag_socket);
  test_module.wbq_socket.bind(rsp_rob.wbq_rsp_socket);
  rsp_rob.rspq_socket.bind(test_module.rspq_socket);

  rsp_rob.term_sync.bind(term_sync_signal);
  test_module.term_sync.bind(term_sync_signal);

  // Connect the rspq_ready signal
  rsp_rob.rspq_ready.bind(rspq_ready_signal);
  test_module.rspq_ready.bind(rspq_ready_signal);
  // connect to rspq_valid_signal
  rsp_rob.rspq_valid.bind(rspq_valid_signal);
  test_module.rspq_valid.bind(rspq_valid_signal);
  // connect to disp_roflag_valid_signal
  rsp_rob.disp_roflag_valid.bind(disp_roflag_valid_signal);
  test_module.disp_roflag_valid.bind(disp_roflag_valid_signal);

  // connect to disp_roflag_ready_signal
  rsp_rob.disp_roflag_ready.bind(disp_roflag_ready_signal);
  test_module.disp_roflag_ready.bind(disp_roflag_ready_signal);

  // connect to wbq_rsp_valid_signa
  for (int i = 0; i < NPUConfig::FU_NUM; i++) {
    rsp_rob.wbq_rsp_valid[i].bind(wbq_rsp_valid_signal[i]);
    test_module.wbq_rsp_valid[i].bind(wbq_rsp_valid_signal[i]);
  }
  // connect to wbq_rsp_ready_signal
  for (int i = 0; i < NPUConfig::FU_NUM; i++) {
    rsp_rob.wbq_rsp_ready[i].bind(wbq_rsp_ready_signal[i]);
    test_module.wbq_rsp_ready[i].bind(wbq_rsp_ready_signal[i]);
  }
  // Start simulation
  sc_start();

  return 0;
}
```
测试输出
```log
Testing WBQ blocking with sync flag
[INFO   ] [0 s       ] [RSP_ROB] disp_roflag: sync_flag=1, fu_ena=0, fuid=0, inst_xd=1, inst_rd=0
WBQ blocking with sync flag test completed
Testing WBQ blocking with fu_ena
WBQ blocking with fu_ena test completed
Testing RSPQ blocking with sync flag
Received RSPQ response: 0000000020
RSPQ blocking with sync flag test completed
Testing RSPQ blocking without sync flag
[INFO   ] [221 ns    ] [RSP_ROB] decoder_thread one round
[INFO   ] [221 ns    ] [RSP_ROB] disp_roflag: sync_flag=0, fu_ena=1, fuid=2, inst_xd=1, inst_rd=0
Received RSPQ response: 1ec8143e80
[INFO   ] [320 ns    ] [RSP_ROB] decoder_thread one round
[INFO   ] [320 ns    ] [RSP_ROB] disp_roflag: sync_flag=1, fu_ena=0, fuid=0, inst_xd=1, inst_rd=0
Received RSPQ response: 0000000020
[INFO   ] [321 ns    ] [RSP_ROB] decoder_thread one round
[INFO   ] [321 ns    ] [RSP_ROB] disp_roflag: sync_flag=0, fu_ena=0, fuid=0, inst_xd=1, inst_rd=0
Received RSPQ response: 0000000020
[INFO   ] [321 ns    ] [RSP_ROB] decoder_thread one round
RSPQ blocking without sync flag test completed
```
## 注意事项

- 确保正确设置 ROB 队列大小。
- 注意处理所有可能的阻塞情况，以防止死锁。
- 在多功能单元环境中使用时，需要仔细协调各单元的响应时序。
- 定期检查和更新 `NPUConfig` 中的常量，以确保与整体系统配置一致。