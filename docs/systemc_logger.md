# SystemC Logger 文档

## 目录

1. [简介](#简介)
2. [特性](#特性)
3. [安装](#安装)
4. [使用方法](#使用方法)
5. [API 参考](#api-参考)
6. [宏定义](#宏定义)
7. [示例](#示例)
8. [注意事项](#注意事项)
9. [贡献指南](#贡献指南)

## 简介

SystemC Logger 是一个为 SystemC 仿真环境设计的轻量级、高效的日志记录库。它提供了灵活的日志级别控制、彩色控制台输出和文件输出功能，使得在 SystemC 项目中进行调试和日志记录变得简单而强大。

## 特性

- 支持多个日志级别（DEBUG, INFO, WARNING, ERROR）
- 彩色控制台输出，提高可读性
- 可选的文件输出
- 线程安全设计
- 使用宏定义，易于集成和使用
- 支持断言和异常捕获

## 安装

将 `systemc_logger.h` 和 `systemc_logger.cpp` 文件添加到项目中，并确保它们在编译路径中。

## 使用方法

1. 包含头文件：
```c++
#include "systemc_logger.h"
```

2. 在代码中使用日志宏：
```cpp
info_sc("This is an info message");
debug_sc("Debug information: %d", someValue);
warning_sc("Warning: %s", warningMessage);
error_sc("An error occurred: %s", errorMessage);
```

3. 配置日志输出：
```cpp
// 设置日志输出到文件
LOG_TO_FILE("simulation.log");
// 设置日志输出到控制台
LOG_TO_CONSOLE();
// 同时输出到文件和控制台
LOG_TO_BOTH("simulation.log");
```

## API 参考

### SystemCLogger 类

#### 构造函数
```cpp
SystemCLogger(LogLevel level = SC_INFO);
```

创建一个 SystemCLogger 实例，默认日志级别为 INFO。

#### 公共方法

- `void enableConsoleOutput(bool enable)`: 启用或禁用控制台输出
- `void enableFileOutput(bool enable)`: 启用或禁用文件输出
- `void setLogLevel(LogLevel level)`: 设置日志级别
- `void setLogFile(const std::string& filename)`: 设置日志文件名
- `void setLogToConsole()`: 设置仅输出到控制台
- `void closeLogFile()`: 关闭日志文件
- `void log(LogLevel level, const char* file, int line, const char* func, const char* format, ...)`: 记录日志

## 宏定义

- `debug_sc(...)`: 记录调试级别日志
- `info_sc(...)`: 记录信息级别日志
- `warning_sc(...)`: 记录警告级别日志
- `error_sc(...)`: 记录错误级别日志并抛出异常
- `assert_sc(cond, ...)`: 断言条件，失败时记录错误并抛出异常
- `try_sc(cond, ...)`: 尝试断言条件，捕获可能的异常

## 示例
```cpp
#include "systemc_logger.h"
int sc_main(int argc, char *argv[])  {
// 设置日志输出到文件和控制台
LOG_TO_BOTH("simulation.log");
// 记录不同级别的日志
debug_sc("Debug message");
info_sc("Simulation started");
warning_sc("Potential issue detected");
// 使用断言
int x = 5;
assert_sc(x == 5, "x should be 5");
// 捕获异常
try_sc(x == 10, "x should be 10");
return 0;
}
```

输出
```
Log file opened (overwrite mode): simulation.log
[INFO   ] [0 s       ] Simulation started
[WARNING] [0 s       ] Potential issue detected
[ERROR  ] [0 s       ] Assertion failed: x == 10. x should be 10 (in test/test_log.cpp:sc_main:53)
Caught assertion failure: Error occurred
```
## 注意事项

- 确保在使用日志宏之前定义 `ENABLE_LOGGING`，否则日志调用将被忽略。
- 文件日志默认使用覆盖模式打开文件，请注意备份重要的日志文件。
- 默认为INFO等级，只会输出该日志等级以上的日志，可通过API设置日志等级
## 贡献指南

欢迎提交问题报告和拉取请求。对于重大更改，请先开issue讨论您想要更改的内容。



