# TMU (Tensor Manipulation Unit) 测试计划文档

## 1. 概述

本文档详细描述了张量操作单元（TMU）的测试计划，包括测试架构、测试用例设计、测试覆盖率要求以及测试执行策略。

## 2. 被测模块分析

### 2.1 TMU功能概述
TMU支持以下主要操作：
- **BC (Broadcast)**: 标量值广播到张量
- **MOV (Move)**: 张量移动/复制
- **TRANS (Transpose)**: 张量转置
- **MOV-ALIGN**: 带浮点对齐的张量移动
- **TRANS-ALIGN**: 带浮点对齐的张量转置
- **SET_CIMEXP/GET_CIMEXP**: CIM指数设置/获取

### 2.2 支持的数据类型
- INT4, INT8, INT16
- FP8, FP16, BF16

### 2.3 内存模式
- **SPAD**: 标准线性内存访问
- **CIMC**: 计算内存集群，具有特殊的物理布局

## 3. 测试架构

### 3.1 测试平台架构

```mermaid
graph TB
    subgraph "Test Environment"
        TB[Test Bench]
        TC[Test Cases]
        TM[Test Monitor]
        TS[Test Scorer]
    end
    
    subgraph "DUT Interface"
        CFG[Config Socket]
        MEM[Memory Socket]
    end
    
    subgraph "TMU Module"
        TMU[FeatTMU]
        DP[Data Path]
        CTRL[Control Logic]
    end
    
    subgraph "Memory Models"
        SPAD[SPAD Model]
        CIMC[CIMC Model]
    end
    
    TB --> CFG
    TB --> MEM
    CFG --> TMU
    MEM --> TMU
    TMU --> DP
    TMU --> CTRL
    MEM --> SPAD
    MEM --> CIMC
    
    TC --> TB
    TM --> TMU
    TS --> TM
```

### 3.2 测试组件设计

#### 3.2.1 测试基类 (TMUTestBase)
```cpp
class TMUTestBase {
protected:
    // 配置管理
    virtual void setup_config(const TMUConfig& cfg);
    virtual void send_command(uint32_t funct7, uint64_t rs1, uint64_t rs2);
    
    // 内存操作
    virtual void write_memory(uint64_t addr, const std::vector<uint8_t>& data);
    virtual std::vector<uint8_t> read_memory(uint64_t addr, size_t len);
    
    // 验证函数
    virtual bool verify_result(const ExpectedResult& expected);
    virtual void generate_reference(const TestInput& input, ExpectedResult& output);
};
```

#### 3.2.2 内存模型 (MemoryModel)
```cpp
class MemoryModel {
public:
    virtual void write(uint64_t addr, const uint8_t* data, size_t len) = 0;
    virtual void read(uint64_t addr, uint8_t* data, size_t len) = 0;
    virtual bool is_valid_address(uint64_t addr) = 0;
};

class SPADModel : public MemoryModel { /* SPAD实现 */ };
class CIMCModel : public MemoryModel { /* CIMC实现 */ };
```

## 4. 测试用例设计

### 4.1 基础功能测试

#### 4.1.1 BC (Broadcast) 测试
```
测试ID: TMU_BC_001 - 基础广播测试
目标: 验证标量广播到不同大小张量的正确性
配置: 
  - 数据类型: INT8, INT16, FP16
  - 张量大小: (2,4,8), (1,16,4), (4,32,2)
  - 标量值: 0x00, 0xFF, 0x5A, 随机值
验证: 输出张量所有位置均为广播值

测试ID: TMU_BC_002 - 边界值广播测试  
目标: 验证极值标量的广播
配置: 数据类型对应的最大/最小值
验证: 数值正确性和溢出处理

测试ID: TMU_BC_003 - 不同stride配置测试
目标: 验证非连续内存布局的广播
配置: 不同的stride_dim1/stride_dim2配置
验证: 按照stride正确写入
```

#### 4.1.2 MOV (Move) 测试
```
测试ID: TMU_MOV_001 - 基础移动测试
目标: 验证张量复制的正确性
配置:
  - 输入/输出张量大小匹配
  - 不同数据类型
  - 不同内存布局
验证: 输出张量与输入张量完全一致

测试ID: TMU_MOV_002 - 块处理测试
目标: 验证64x1块处理逻辑
配置: 张量大小超过块大小的情况
验证: 所有块正确处理，边界块填充正确

测试ID: TMU_MOV_003 - SPAD到CIMC移动
目标: 验证不同内存类型间的数据移动
配置: 源地址SPAD，目标地址CIMC
验证: CIMC物理地址映射正确

测试ID: TMU_MOV_004 - 填充处理测试
目标: 验证rem_dim0填充的正确处理
配置: cfg_rem_dim0 > 0的情况
验证: 最后一个chunk的填充位置为0
```

#### 4.1.3 TRANS (Transpose) 测试
```
测试ID: TMU_TRANS_001 - 基础转置测试
目标: 验证矩阵转置功能
配置:
  - 不同输入块大小 (16x4, 32x2, 64x1)
  - 不同数据类型位宽
验证: 转置后矩阵正确性

测试ID: TMU_TRANS_002 - 动态块大小测试
目标: 验证基于数据位宽的动态块大小
配置:
  - 16bit: 16x4块
  - 8bit: 32x2块  
  - 4bit: 64x1块
验证: 块大小选择和处理正确

测试ID: TMU_TRANS_003 - 非方阵转置测试
目标: 验证非正方形矩阵的转置
配置: 不同的dim1/dim0b比例
验证: 转置维度正确映射
```

#### 4.1.4 对齐操作测试
```
测试ID: TMU_ALIGN_001 - MOV-ALIGN测试
目标: 验证移动+浮点对齐功能
配置: 浮点数据类型，CIMC目标
验证: 
  - 数据正确移动到CIMC
  - 指数对齐正确
  - 指数行(row 32)写入正确

测试ID: TMU_ALIGN_002 - TRANS-ALIGN测试
目标: 验证转置+浮点对齐功能
配置: 浮点数据类型，CIMC目标
验证:
  - 转置操作正确
  - 对齐操作正确
  - 指数存储正确

测试ID: TMU_ALIGN_003 - 指数向量管理测试
目标: 验证SET_CIMEXP/GET_CIMEXP操作
配置: 8个宏的指数向量
验证: 指数正确读写CIMC第32行
```

### 4.2 边界和异常测试

#### 4.2.1 边界条件测试
```
测试ID: TMU_BOUND_001 - 最小张量测试
目标: 验证最小尺寸张量处理
配置: (1,1,1)大小张量
验证: 正确处理无需分块的情况

测试ID: TMU_BOUND_002 - 最大张量测试  
目标: 验证支持的最大张量尺寸
配置: 配置寄存器的最大值
验证: 不发生溢出，正确处理

测试ID: TMU_BOUND_003 - 填充边界测试
目标: 验证各种填充场景
配置: 
  - cfg_rem_dim0 = 1, size_dim0a-1
  - 边界块大小
验证: 填充位置全为0
```

#### 4.2.2 错误处理测试
```
测试ID: TMU_ERR_001 - 无效地址测试
目标: 验证无效内存地址的处理
配置: 超出内存范围的地址
验证: 错误检测和恢复

测试ID: TMU_ERR_002 - 配置冲突测试
目标: 验证不兼容配置的处理
配置: 不匹配的输入输出尺寸
验证: 配置验证和错误报告

测试ID: TMU_ERR_003 - 数据类型不匹配测试
目标: 验证数据类型配置错误处理
配置: 无效的cfg_type/cfg_wd组合
验证: 错误检测机制
```

### 4.3 性能和压力测试

#### 4.3.1 性能基准测试
```
测试ID: TMU_PERF_001 - 吞吐量测试
目标: 测量各操作的数据吞吐量
配置: 标准工作负载
指标: 
  - 每周期处理的字节数
  - 操作延迟
  - 内存带宽利用率

测试ID: TMU_PERF_002 - 块处理效率测试
目标: 验证块处理的性能优化
配置: 大张量，不同块大小
指标: 块处理时间对比
```

#### 4.3.2 压力测试
```
测试ID: TMU_STRESS_001 - 连续操作测试
目标: 验证连续大量操作的稳定性
配置: 1000+连续TMU操作
验证: 无性能下降，无错误累积

测试ID: TMU_STRESS_002 - 随机操作测试
目标: 验证随机操作序列的正确性
配置: 随机生成的操作序列
验证: 每个操作结果正确
```

## 5. 测试数据管理

### 5.1 测试向量生成
```cpp
class TestVectorGenerator {
public:
    // 生成不同模式的测试数据
    std::vector<uint8_t> generate_pattern(PatternType type, size_t size);
    std::vector<uint8_t> generate_random(DataType dtype, size_t size);
    std::vector<uint8_t> generate_boundary(DataType dtype, size_t size);
    
    // 生成预期结果
    void generate_reference_bc(const BCConfig& cfg, std::vector<uint8_t>& result);
    void generate_reference_mov(const MOVConfig& cfg, std::vector<uint8_t>& result);
    void generate_reference_trans(const TRANSConfig& cfg, std::vector<uint8_t>& result);
};
```

### 5.2 结果验证策略
```cpp
class ResultVerifier {
public:
    bool verify_exact_match(const std::vector<uint8_t>& actual, 
                           const std::vector<uint8_t>& expected);
    bool verify_with_tolerance(const std::vector<uint8_t>& actual,
                              const std::vector<uint8_t>& expected, 
                              double tolerance);
    bool verify_structure(const TransposeResult& result);
    void generate_report(const VerificationResult& result);
};
```

## 6. 测试执行计划

### 6.1 测试阶段

#### 阶段1: 单元测试 (Week 1-2)
- 基础功能测试 (BC, MOV, TRANS)
- 配置接口测试
- 内存模型验证

#### 阶段2: 集成测试 (Week 3-4)  
- 对齐操作测试 (MOV-ALIGN, TRANS-ALIGN)
- CIMC集成测试
- 跨操作组合测试

#### 阶段3: 系统测试 (Week 5-6)
- 性能基准测试
- 压力测试
- 边界和异常测试

#### 阶段4: 回归测试 (Week 7)
- 完整测试套件执行
- 性能回归检查
- 覆盖率分析

### 6.2 测试环境要求

#### 硬件环境
- SystemC仿真环境
- 足够的内存模拟SPAD和CIMC
- 支持TLM 2.0接口

#### 软件环境  
- GCC 9.0+ 或 Clang 10+
- SystemC 2.3.3+
- GoogleTest框架
- 覆盖率分析工具

### 6.3 通过标准

#### 功能正确性
- 所有基础功能测试通过率 100%
- 边界测试通过率 95%+
- 错误处理测试符合规范

#### 性能指标
- 吞吐量达到设计目标的 90%+
- 延迟在可接受范围内
- 内存效率 85%+

#### 覆盖率要求
- 代码覆盖率 95%+
- 分支覆盖率 90%+
- 功能覆盖率 100%

## 7. 风险和缓解措施

### 7.1 风险识别
1. **复杂的地址计算逻辑**: CIMC物理地址映射复杂
2. **浮点对齐算法**: 数值精度和性能平衡
3. **边界条件处理**: 填充和掩码逻辑复杂
4. **内存模型准确性**: SPAD/CIMC模型与实际硬件差异

### 7.2 缓解措施
1. **分层测试策略**: 先测试地址计算，再测试完整操作
2. **参考模型验证**: 使用独立的参考实现
3. **自动化测试生成**: 减少手工测试用例的错误
4. **硬件对比验证**: 关键路径与RTL仿真对比

## 8. 交付物

### 8.1 测试代码
- 完整的测试框架
- 所有测试用例实现
- 参考模型和验证工具

### 8.2 测试报告
- 测试执行报告
- 覆盖率分析报告  
- 性能基准报告
- 缺陷分析报告

### 8.3 文档
- 测试用户手册
- 已知问题和限制说明
- 测试维护指南

---

*本测试计划将根据项目进展和发现的问题持续更新和完善。* 