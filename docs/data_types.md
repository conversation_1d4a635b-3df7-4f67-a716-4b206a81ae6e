
## data precision
| Group             | Precision          | Type code   | Width code | Description                      | Domain Constraint                       |
| ----------------- | ------------------ | ----------- | ---------- | -------------------------------- | --------------------------------------- |
| Integer           | INT4               | 00: 'INT'  | 010: '4'   | 4位有符号整数                     |                                         |
|                   | INT8               | 00: 'INT'  | 011: '8'   | 8位有符号整数                     |                                         |
|                   | INT16              | 00: 'INT'  | 100: '16'  | 16位有符号整数                    |                                         |
|                   | INT32              | 00: 'INT'  | 101: '32'  | 32位有符号整数                    |                                         |
| Floating-point    | FP16               | 01: 'FP'   | 100: '16'  | 1位符号+5位指数+11位尾数          |                                         |
|                   | FP32               | 01: 'FP'   | 101: '32'  | 1位符号+8位指数+23位尾数          |                                         |
|                   | BF16               | 10: 'BF'   | 100: '16'  | 1位符号+8位指数+7位尾数           |                                         |
|                   | BBF16 (Block BF16) | 11: 'BBF'  | 100: '16'  | 1位符号+14位尾数                  | 仅可用于CONV Primitive的权重矩阵W        |
