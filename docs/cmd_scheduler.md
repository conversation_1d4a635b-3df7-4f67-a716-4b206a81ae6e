# CMD_DISPATCH 模块文档

## 目录

1. [简介](#简介)
2. [架构](#架构)
3. [特性](#特性)
4. [安装](#安装)
5. [使用方法](#使用方法)
6. [API 参考](#api-参考)
7. [数据结构](#数据结构)
8. [测试](#测试)
9. [注意事项](#注意事项)

## 简介

CMD_DISPATCH（Command Dispatcher）是一个为 SystemC 仿真环境设计的模块，用于管理和分发来自 Command Queue 的指令。它使用 TLM-2.0 进行通信，提供了处理指令分发、Scoreboard 更新和与 Issue Queue 交互的方法。

## 架构
### 整体处理逻辑
```mermaid
graph TD
    A[开始] --> B{是否无效?}
    B -->|是| C[无效处理:<br>不产生任何行为,<br>继续取下一条命令]
    B -->|否| D{是否阻塞?}
    D -->|是| E[阻塞处理:<br>1. 检查sync阻塞条件<br>2. 检查rob阻塞条件<br>3. 检查isq阻塞条件<br>4. 检查sbd阻塞条件]
    D -->|否| F[活跃处理:<br>1. sync陷入<br>2. rob写入<br>3. isq写入<br>4. sbd更新]
    E --> F
    C --> G[结束]
    F --> G
```
### 无效处理流程
```mermaid
graph TD
    A[开始] --> B{is_command_invalid?}
    B -->|是| C[handle_invalid_command]
    B -->|否| D[继续其他处理]
    
    B --> E{检查指令类型}
    E -->|SYNC, TLD_CFG, 等| F[永远有效]
    E -->|SW_CIMC| G{is_cimc_locked?}
    E -->|LK_LMEM, WR_LMEM, 等| H{is_lmem_locked?}
    E -->|UNLK_LMEM| I{!is_lmem_locked?}
    E -->|MOV_DRV, TRANS_DRV, 等| J{rs1或rs2 LMEM locked?}
    E -->|V_S_DRV| K{rs2 LMEM locked?}
    E -->|其他| L[视为无效]
    
    G -->|是| M[无效]
    G -->|否| N[有效]
    H -->|是| M
    H -->|否| N
    I -->|是| M
    I -->|否| N
    J -->|是| M
    J -->|否| N
    K -->|是| M
    K -->|否| N
    
    F --> N
    L --> M
    
    M --> C
    N --> D
    
    C --> O[结束]
    D --> O

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#f9f,stroke:#333,stroke-width:2px
```
### 堵塞处理流程
```mermaid
graph TD
    A[开始] --> B{is_command_blocked?}
    B -->|是| C[handle_blocked_command]
    B -->|否| D[继续其他处理]
    
    B --> E{检查阻塞原因}
    E --> F{is_rob_blocked?}
    E --> G{is_sbd_blocked?}
    E --> H{is_isq_blocked?}
    E --> I{is_sync_blocked?}
    
    F -->|是| J[ROB阻塞]
    G -->|是| K[SBD阻塞]
    H -->|是| L[ISQ阻塞]
    I -->|是| M[SYNC阻塞]
    
    J --> C
    K --> C
    L --> C
    M --> C
    
    C --> N[handle_active_command]
    D --> N
    
    N --> O[结束]

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#f9f,stroke:#333,stroke-width:2px
```
```mermaid
graph TD
    A[开始 handle_blocked_command] --> B{检查阻塞原因}
    B -->|ROB阻塞| C[检查ROB队列状态]
    B -->|SBD阻塞| D[检查Scoreboard状态]
    B -->|ISQ阻塞| E[检查Issue Queue状态]
    B -->|SYNC阻塞| F[检查同步状态]
    C --> G[更新阻塞状态]
    D --> G
    E --> G
    F --> G
    G --> H[结束]

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
    style F fill:#bbf,stroke:#333,stroke-width:2px
    style G fill:#bbf,stroke:#333,stroke-width:2px
```

### 活跃处理流程
```mermaid
graph TD
    A[开始 handle_active_command] --> B{命令类型判断}
    B --> C{需要SYNC处理?}
    C -->|是| D[handle_sync_trap]
    B --> E{需要ROB写入?}
    E -->|是| F[handle_rob_write]
    B --> G{需要ISQ写入?}
    G -->|是| H[handle_isq_write]
    B --> I{需要SBD更新?}
    I -->|是| J[handle_sbd_update]
    
    D --> K[更新同步状态]
    F --> L[写入ROB队列]
    H --> M[写入Issue Queue]
    J --> N[更新Scoreboard]
    
    K --> O[结束]
    L --> O
    M --> O
    N --> O

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
    style G fill:#bbf,stroke:#333,stroke-width:2px
    style I fill:#bbf,stroke:#333,stroke-width:2px
```

## 特性

- 支持多种指令类型的处理和分发
- 使用 TLM-2.0 进行通信
- 提供 Scoreboard 更新功能
- 实现了指令无效、阻塞和活跃状态的处理机制
- 支持同步指令处理

## 安装

将 `cmd_dispatch.h` 和 `cmd_dispatch.cpp` 文件添加到项目中，并确保它们在编译路径中。

## 使用方法

1. 包含头文件:
   ```cpp
   #include "cmd_dispatch.h"
   ```

2. 创建 CMD_DISPATCH 实例:
   ```cpp
   CmdDispatch cmd_dispatch("cmd_dispatch_instance");
   ```

3. 连接 TLM-2.0 socket 和信号:
   ```cpp
   your_module.cmd_queue_socket.bind(cmd_dispatch.cmd_queue_socket);
   cmd_dispatch.rob_rsp_socket.bind(your_rob_module.target_socket);
   cmd_dispatch.info_sbd_socket.bind(your_scoreboard_module.target_socket);
   cmd_dispatch.isq_cmd_socket.bind(your_issue_queue_module.target_socket);
   // 连接其他信号...
   ```

4. 在仿真中使用 CMD_DISPATCH:
   ```cpp
   // 发送指令到 Command Queue
   tlm::tlm_generic_payload trans;
   sc_time delay = SC_ZERO_TIME;
   Command cmd;
   // 设置 cmd...
   trans.set_data_ptr(reinterpret_cast<unsigned char*>(&cmd));
   trans.set_data_length(sizeof(Command));
   cmd_dispatch.cmd_queue_socket->b_transport(trans, delay);
   ```

## API 参考

### CmdDispatch 类

#### 构造函数
```cpp
CmdDispatch(const sc_core::sc_module_name& name);
```

#### 公共接口

- `tlm_utils::simple_target_socket<CmdDispatch> cmd_queue_socket`
- `tlm_utils::simple_initiator_socket<CmdDispatch> rob_rsp_socket`
- `tlm_utils::simple_initiator_socket<CmdDispatch> info_sbd_socket`
- `tlm_utils::simple_initiator_socket<CmdDispatch> isq_cmd_socket`
- `sc_core::sc_out<bool> rob_rsp_valid`
- `sc_core::sc_in<bool> rob_rsp_ready`
- `sc_core::sc_in<bool> term_sync`
- `sc_core::sc_in<bool> cmd_queue_valid`
- `sc_core::sc_out<bool> cmd_queue_ready`
- `std::array<sc_core::sc_out<bool>, NPUConfig::FU_NUM> isq_cmd_valid`
- `std::array<sc_core::sc_in<bool>, NPUConfig::FU_NUM> isq_cmd_ready`

## 数据结构

### Command
```cpp
cpp
struct Command {
std::conditional_t<NPUConfig::RV_XLEN == 32, sc_dt::sc_uint<32>, sc_dt::sc_uint<64>> rs2val;
std::conditional_t<NPUConfig::RV_XLEN == 32, sc_dt::sc_uint<32>, sc_dt::sc_uint<64>> rs1val;
sc_dt::sc_uint<7> func7;
sc_dt::sc_uint<5> rs2;
sc_dt::sc_uint<5> rs1;
sc_dt::sc_uint<1> xd;
sc_dt::sc_uint<1> xs1;
sc_dt::sc_uint<1> xs2;
sc_dt::sc_uint<5> rd;
sc_dt::sc_uint<7> opcode;
};
```

### ISQ_CMD
```cpp
cpp
struct ISQ_CMD {
struct CmdData {
std::conditional_t<NPUConfig::RV_XLEN == 32, sc_dt::sc_uint<32>, sc_dt::sc_uint<64>> rs2val;
std::conditional_t<NPUConfig::RV_XLEN == 32, sc_dt::sc_uint<32>, sc_dt::sc_uint<64>> rs1val;
sc_dt::sc_uint<10> inst;
};
std::array<CmdData, NPUConfig::FU_NUM> isq_cmd_data;
};
```

## 测试

示例测试代码,见test/test_cmddispatch.cpp
注:仅测试无效指令，由于模块间依赖，需注释掉cmd_dispatch中process_cmd的堵塞和活跃处理。完整测试位于cmd_scheduler中
测试结果：
```log
[INFO   ] [5 ns      ] 测试无效指令: SYNC
  LMEM索引: rs1=0, rs2=0
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [10 ns     ] 测试无效指令: SW_CIMC
  LMEM索引: rs1=4, rs2=0
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [15 ns     ] 测试无效指令: SW_CIMC
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [20 ns     ] 测试无效指令: LK_LMEM
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [25 ns     ] 测试无效指令: LK_LMEM
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [30 ns     ] 测试无效指令: UNLK_LMEM
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [35 ns     ] 测试无效指令: UNLK_LMEM
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [40 ns     ] 测试无效指令: WR_LMEM
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [45 ns     ] 测试无效指令: WR_LMEM
  LMEM索引: rs1=0, rs2=0
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [50 ns     ] 测试无效指令: RD_LMEM
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [55 ns     ] 测试无效指令: RD_LMEM
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [60 ns     ] 测试无效指令: TLD_CFG
  LMEM索引: rs1=0, rs2=0
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [65 ns     ] 测试无效指令: TLD_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [70 ns     ] 测试无效指令: TLD_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [75 ns     ] 测试无效指令: TST_CFG
  LMEM索引: rs1=0, rs2=0
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [80 ns     ] 测试无效指令: TST_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [85 ns     ] 测试无效指令: TST_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [90 ns     ] 测试无效指令: TM_CFG
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [95 ns     ] 测试无效指令: BC_PRE
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [100 ns    ] 测试无效指令: BC_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [105 ns    ] 测试无效指令: BC_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [110 ns    ] 测试无效指令: MOV_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [115 ns    ] 测试无效指令: MOV_DRV
  LMEM索引: rs1=1, rs2=2
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [120 ns    ] 测试无效指令: MOV_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [125 ns    ] 测试无效指令: TRANS_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [130 ns    ] 测试无效指令: TRANS_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [135 ns    ] 测试无效指令: MP_CFG
  LMEM索引: rs1=0, rs2=0
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [140 ns    ] 测试无效指令: CONV_PRE
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [145 ns    ] 测试无效指令: CONV_PRE
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [150 ns    ] 测试无效指令: CONV_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [155 ns    ] 测试无效指令: CONV_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [160 ns    ] 测试无效指令: DWCONV_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [165 ns    ] 测试无效指令: DWCONV_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [170 ns    ] 测试无效指令: VP_CFG
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [175 ns    ] 测试无效指令: VV_V_PRE
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [180 ns    ] 测试无效指令: VV_V_PRE
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [185 ns    ] 测试无效指令: VV_V_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [190 ns    ] 测试无效指令: VV_V_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [195 ns    ] 测试无效指令: VS_V_PRE
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [200 ns    ] 测试无效指令: VS_V_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [205 ns    ] 测试无效指令: VS_V_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [210 ns    ] 测试无效指令: V_S_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [215 ns    ] 测试无效指令: V_S_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [220 ns    ] 测试无效指令: V_V_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 无效
  实际结果: 无效
测试结果: 通过 ✓
----------------------------------------

[INFO   ] [225 ns    ] 测试无效指令: V_V_DRV
  LMEM索引: rs1=0, rs2=1
  预期结果: 有效
  实际结果: 有效
测试结果: 通过 ✓
----------------------------------------
```


## 注意事项

- 确保正确处理所有指令类型，包括无效、阻塞和活跃状态。
- 注意同步指令的特殊处理，以确保正确的执行顺序。
- 在更新 Scoreboard 时，需要仔细考虑数据依赖关系。
- 定期检查和更新 `NPUConfig` 中的常量，以确保与整体系统配置一致。
- 在多功能单元环境中使用时，需要协调各单元的指令分发和执行时序。