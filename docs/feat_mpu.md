# 指令内容
```mermaid
flowchart TD
    subgraph MV_V[Matrix-Vector Multiplication]
        MV1[Step1: Read input Vector I1 from LMEM based on byte_base_in]
        MV2[Step2: Read weight Matrix M2 from LMEM based on byte_base_wt]
        MV3[Step3: Configure operation mode and perform matrix-vector calculation]
        MV4[Step4: Write Vector O to Local Memory based on byte_base_out]
        
        MV1 --> MV2 --> MV3 --> MV4
        
        subgraph MV_Support[Supported Operation Types]
            MVS1[a. Thin layer results]
            MVS2[b. Add on original matrix basis]
            MVS3[c. Process calculation results with activation function]
            MVS4[d. Perform position operations on calculation results]
        end
    end
    
    subgraph MM_M[Matrix-Matrix Multiplication]
        MM1[Step1: Read input Matrix M1 from LMEM based on byte_base_in]
        MM2[Step2: Read weight Matrix M2 from LMEM based on byte_base_wt]
        MM3[Step3: Configure operation mode and perform matrix-matrix calculation]
        MM4[Step3: Write Matrix O to Local Memory based on byte_base_out]
        
        MM1 --> MM2 --> MM3 --> MM4
        
        subgraph MM_Support[Supported Operation Types]
            MMS1[a. Thin layer results]
            MMS2[b. Add on original matrix basis]
            MMS3[c. Process calculation results with activation function]
            MMS4[d. Perform position operations on calculation results]
        end
    end
```

## GEMV
**所需配置寄存器列表如下:**

| Address[31:16] | Address[15:0] | Group            | Name              | Width | Description                                                                                                                                 | Field   |
| -------------- | ------------- | ---------------- | ----------------- | ----- | ------------------------------------------------------------------------------------------------------------------------------------------- | ------- |
| 3              | 0             | Precision        | cfg_type_out      | 2     | Element data type of output Tensor O.                                                                                                       | [1:0]   |
| 3              | 0             | Precision        | cfg_type_orig     | 2     | Element data type of original Tensor OR.                                                                                                    | [3:2]   |
| 3              | 0             | Precision        | cfg_type_in       | 2     | Element data type of input Tensor I.                                                                                                        | [5:4]   |
| 3              | 0             | Precision        | cfg_type_wt       | 2     | Element data type of weight Matrix W.                                                                                                       | [7:6]   |
| 3              | 0             | Precision        | cfg_wd_out        | 3     | Element data width of output Tensor O;                                                                                                      | [10:8]  |
| 3              | 0             | Precision        | cfg_wd_orig       | 3     | Element data width of original Tensor OR;                                                                                                   | [13:11] |
| 3              | 0             | Precision        | cfg_wd_in         | 3     | Element data width of input Tensor I.                                                                                                       | [16:14] |
| 3              | 0             | Precision        | cfg_wd_wt         | 3     | Element data width of weight Matrix W;                                                                                                      | [19:17] |
| 3              | 0             | Operation        | cfg_accu          | 1     | Accumulation Configuration:<br>1'b0: no historical partial sum to be accumulated;<br>1'b1: historical partial sum to be accumulated exists. | [20]    |
| 3              | 0             | Operation        | cfg_act           | 1     | Activation function configuration.<br>1'b0: do not conduct activation function.<br>1'b1: conduct the ReLU function.                         | [21]    |
| 3              | 0             | Operation        | cfg_shift         | 5     | Right-shift configuration.<br>Only used for integer output.                                                                                 | [26:22] |
| 3              | 1             | REM of Tensor O  | cfg_rem_dim0_out  | 6     | Dim0 remainder of output Tensor O.                                                                                                          | [5:0]   |
| 3              | 1             | SIZE of Tensor O | cfg_size_dim0_out | 11    | Dim0b size of output Tensor O.                                                                                                              | [16:6]  |
| 3              | 5             | REM of Tensor I  | cfg_rem_dim0_in   | 6     | Dim0 remainder of input Tensor I.                                                                                                           | [5:0]   |
| 3              | 5             | SIZE of Tensor I | cfg_size_dim0_in  | 11    | Dim0b size of input Tensor I.                                                                                                               | [16:6]  |

1. 根据byte_base_in, 以及<REM/SIZE/STRIDE of Tensor I>配置值，从Local Memory中读出一个一维Tensor I，其数据精度为prec_in；
2. 根据byte_base_wt，将Tensor I 送入某个CIM Cluster中，与其存储的二维Matrix W 做矩阵乘法，输出一个向量Vector O_uf，vector O_uf的数据精度为prec_orig；
3. 若<Operation>.cfg_accu配置值为1, 则根据byte_base_out, 以及<REM/SIZE of Tensor O>配置值, 从Local Memory读出向量Vector OR, 其精度为prec_orig; 将vector OR累加到Vector O上;
 4. 根据<Operation>.cfg_act配置值，向量Vector O执行相应的激活函数；
5. 若Tensor O的数据精度为定点，根据<Operation>.cfg_shift配置值，vector O执行相应右移；需要注意，这一步之后，vector O的数据精度仍然为prec_orig;
6. 若vector O的数据精度与<Precision>中不一致，则需要对vector O进行精度格式转换，将数据精度转换为prec_out;
7. 根据byte_base_out, 以及<REM/SIZE of Vector O>配置值，将Vector O写入Local Memory，之后结束执行。

> **注意**：Tensor_out结果既可以覆盖原有的Tensor_ori, 也可以保存至与Tensor_ori不同地址。

| Inst Name | Inst Type | funct7   | xd, xs1, xs2 | rdval | rs1val        | rs2val        |
|-----------|-----------|----------|--------------|-------|---------------|---------------|
| gemv_pre  | NICE      | 100_0010 | 0, 1, 1      | /     | page_idx      | base_addr_in  |
| gemv_drv  | NICE      | 100_0011 | 0, 1, 1      | /     | base_addr_out | base_addr_ori |


 ## GEMM Primitive

| Inst Name | Inst Type | funct7   | xd, xs1, xs2 | rdval | rs1val        | rs2val        |
|-----------|-----------|----------|--------------|-------|---------------|---------------|
| gemm_pre  | NICE      | 100_0100 | 0, 1, 1      | /     | page_idx      | base_addr_in  |
| gemm_drv  | NICE      | 100_0101 | 0, 1, 1      | /     | base_addr_out | base_addr_ori |

与GEMV过程，精度参数列表与寄存器操作，GEMV一致，这里不再进行赘述。区别在于输入，输出均为矩阵。因此，需要dim1相关配置信息，具体列表如下：

| Address[31:16] | Address[15:0] | Group          | Name              | Width | Description                                                                                                                               | Field   |
|----------------|---------------|----------------|-------------------|-------|-------------------------------------------------------------------------------------------------------------------------------------------|---------|
| 3              | 0             | Precision      | cfg_type_out      | 2     | Element data type of output Tensor O.                                                                                                     | [1:0]   |
| 3              | 0             | Precision      | cfg_type_orig     | 2     | Element data type of original Tensor OR.                                                                                                  | [3:2]   |
| 3              | 0             | Precision      | cfg_type_in       | 2     | Element data type of input Tensor I.                                                                                                      | [5:4]   |
| 3              | 0             | Precision      | cfg_type_wt       | 2     | Element data type of weight Matrix W.                                                                                                     | [7:6]   |
| 3              | 0             | Precision      | cfg_wd_out        | 3     | Element data width of output Tensor O;                                                                                                    | [10:8]  |
| 3              | 0             | Precision      | cfg_wd_orig       | 3     | Element data width of original Tensor OR;                                                                                                 | [13:11] |
| 3              | 0             | Precision      | cfg_wd_in         | 3     | Element data width of input Tensor I.                                                                                                     | [16:14] |
| 3              | 0             | Precision      | cfg_wd_wt         | 3     | Element data width of weight Matrix W;                                                                                                    | [19:17] |
| 3              | 0             | Operation      | cfg_accu          | 1     | Accumulation Configuration:<br>1'b0: no historical partial sum to be accumulated;<br>1'b1: historical partial sum to be accumulated exists. | [20]    |
| 3              | 0             | Operation      | cfg_act           | 1     | Activation function configuration.<br>1'b0: do not conduct activation function.<br>1'b1: conduct the ReLU function.                       | [21]    |
| 3              | 0             | Operation      | cfg_shift         | 5     | Right-shift configuration.<br>Only used for integer output.                                                                               | [26:22] |
| 3              | 1             | REM of Tensor O | cfg_rem_dim0_out  | 6     | Dim0 remainder of output Tensor O.                                                                                                        | [5:0]   |
| 3              | 1             | SIZE of Tensor O | cfg_size_dim0_out | 11    | Dim0 size of output Tensor O.                                                                                                       | [16:6]  |
| 3              | 1             | STRIDE of Tensor O | cfg_stride_dim1_out | 13    | Dim1 stride of output Tensor O.                                                                                                       | [12:0]  |
| 3              | 5             | REM of Tensor I | cfg_rem_dim0_in   | 6     | Dim0 remainder of input Tensor I.                                                                                                         | [5:0]   |
| 3              | 5             | SIZE of Tensor I | cfg_size_dim0_in  | 11    | Dim0 size of input Tensor I.                                                                                                       | [16:6]  |
| 3              | 7             | STRIDE of Tensor I | cfg_stride_dim1_in  | 13    | Dim1 stride of input Tensor I.                                                                                                        | [12:0]  |