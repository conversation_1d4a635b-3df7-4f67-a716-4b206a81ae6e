| Group | Inst Name | Target Primitive | Field |||||
|-------|-----------|------------------|-------|-------|-------|-------|-------|
| | | | funct7 | xd, xs1, xs2 | rdval | rs1val | rs2val |
| Control | sync | SYNC | 000_0000 | Optional, 00 | default:1 | \ | \ |
| | sw_cimc | SW_CIMC | 000_0001 | Optional, 11 | default: 1 | byte_base_cimc | cimc_addr_mode |
| | lk_lmem | LK_LMEM | 000_0100 | 110 | lock_flag: 1'b1 | byte_base_lmem | \ |
| | unlk_lmem | UNLK_LMEM | 000_0101 | Optional, 10 | default: 1 | byte_base_lmem | \ |
| | wr_lmem | WR_LMEM | 000_0010 | Optional, 11 | default: 1 | byte_base_lmem | wr_data_lmem |
| | rd_lmem | RD_LMEM | 000_0011 | 110 | rd_data_lmem | byte_base_lmem | \ |
| Data Transfer | tld_cfg | TLD | 001_0_000 | Optional, 11 | default: 1 | cfg_addr | cfg_val |
| | tld_drv | TLD | 001_1_000 | Optional, 11 | default: 1 | byte_base_lmem | byte_base_gmem |
| | tst_cfg | TST | 010_0_000 | Optional, 11 | default: 1 | cfg_addr | cfg_val |
| | tst_drv | TST | 010_1_000 | Optional, 11 | default: 1 | byte_base_lmem | byte_base_gmem |
| Tensor Manipulation | tm_cfg | MOV, BC | 011_0_000 | Optional, 11 | default: 1 | cfg_addr | cfg_val |
| | bc_pre | BC | 011_0_001 | Optional, 10 | default: 1 | val_in | \ |
| | bc_drv | BC | 011_1_001 | Optional, 10 | default: 1 | byte_base_out | \ |
| | mov_drv | MOV | 011_1_000 | Optional, 11 | default: 1 | byte_base_out | byte_base_in |
| | trans_drv | TRANS | 011_1_010 | Optional, 11 | default: 1 | byte_base_out | byte_base_in |
| Matrix Processing | mp_cfg | CONV, DWCONV | 100_0_000 | Optional, 11 | default: 1 | cfg_addr | cfg_val |
| | conv_pre | CONV | 100_0_001 | Optional, 10 | default: 1 | byte_base_wt | \ |
| | conv_drv | CONV | 100_1_000 | Optional, 11 | default: 1 | byte_base_out | byte_base_in |
| | dwconv_pre | DWCONV | 100_0_001 | Optional, 10 | default: 1 | byte_base_wt | \ |
| | dwconv_drv | DWCONV | 100_1_001 | Optional, 11 | default: 1 | byte_base_out | byte_base_in |
| Vector Processing | vp_cfg | VV_V, VS_V, V_S, V_V | 101_0_000 | Optional, 11 | default: 1 | cfg_addr | cfg_addr |
| | vv_v_pre | VV_V | 101_0_001 | Optional, 10 | default: 1 | byte_base_in2 | \ |
| | vv_v_drv | VV_V | 101_1_000 | Optional, 11 | default: 1 | byte_base_out | byte_base_in1 |
| | vs_v_pre | VS_V | 101_0_010 | Optional, 10 | default: 1 | val_in2 | \ |
| | vs_v_drv | VS_V | 101_1_001 | Optional, 11 | default: 1 | byte_base_out | byte_base_in1 |
| | v_s_drv | V_S | 101_1_010 | 101 | val_out | \ | byte_base_in1 |
| | v_v_drv | V_V | 101_1_011 | Optional, 11 | default: 1 | byte_base_out | byte_base_in1 |


命令格式（从MSB到LSB）

| Command Field | Width | Description |
| --- | --- | --- |
| rs2val | RV_XLEN |  |
| rs1val | RV_XLEN |  |
| inst.func7 | 7 |  |
| inst.rs2 | 5 |  |
| inst.rs1 | 5 |  |
| inst.xd | 1 |  |
| inst.xs1 | 1 |  |
| inst.xs2 | 1 |  |
| inst.rd | 5 |  |
| inst.opcode | 7 |  |