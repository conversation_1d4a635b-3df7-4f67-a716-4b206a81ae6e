## Hardware instruction definition

| Group                | Inst Name  | Target Primitive     | funct7    | xd, xs1, xs2 | rdval           | rs1val         | rs2val         |
| -------------------- | ---------- | -------------------- | --------- | ------------ | --------------- | -------------- | -------------- |
| Control              | sync       | SYNC                 | 000_0000  | Optional, 00 | default:1       | \              | \              |
|                      | sw_cimc    | SW_CIMC              | 000_0001  | Optional, 11 | default: 1      | byte_base_cimc | cimc_addr_mode |
|                      | lk_lmem    | LK_LMEM              | 000_0100  | 110          | lock_flag: 1'b1 | byte_base_lmem | \              |
|                      | unlk_lmem  | UNLK_LMEM            | 000_0101  | Optional, 10 | default: 1      | byte_base_lmem | \              |
|                      | wr_lmem    | WR_LMEM              | 000_0010  | Optional, 11 | default: 1      | byte_base_lmem | wr_data_lmem   |
|                      | rd_lmem    | RD_LMEM              | 000_0011  | 110          | rd_data_lmem    | byte_base_lmem | \              |
| Data Transfer        | tld_cfg    | TLD                  | 001_0_000 | Optional, 11 | default: 1      | cfg_addr       | cfg_val        |
|                      | tld_drv    | TLD                  | 001_1_000 | Optional, 11 | default: 1      | byte_base_lmem | byte_base_gmem |
|                      | tst_cfg    | TST                  | 010_0_000 | Optional, 11 | default: 1      | cfg_addr       | cfg_val        |
|                      | tst_drv    | TST                  | 010_1_000 | Optional, 11 | default: 1      | byte_base_lmem | byte_base_gmem |
| Tensor Manipulatio n | tm_cfg     | MOV, BC              | 011_0_000 | Optional, 11 | default: 1      | cfg_addr       | cfg_val        |
|                      | bc_pre     | BC                   | 011_0_001 | Optional, 10 | default: 1      | val_in         | \              |
|                      | bc_drv     | BC                   | 011_1_001 | Optional, 10 | default: 1      | byte_base_out  | \              |
|                      | mov_drv    | MOV                  | 011_1_000 | Optional, 11 | default: 1      | byte_base_out  | byte_base_in   |
|                      | trans_drv  | TRANS                | 011_1_010 | Optional, 11 | default: 1      | byte_base_out  | byte_base_in   |
| Matrix Processing    | mp_cfg     | CONV, DWCONV         | 100_0_000 | Optional, 11 | default: 1      | cfg_addr       | cfg_val        |
|                      | conv_pre   | CONV                 | 100_0_001 | Optional, 10 | default: 1      | byte_base_wt   | \              |
|                      | conv_drv   | CONV                 | 100_1_000 | Optional, 11 | default: 1      | byte_base_out  | byte_base_in   |
|                      | dwconv_pre | DWCONV               | 100_0_001 | Optional, 10 | default: 1      | byte_base_wt   | \              |
|                      | dwconv_drv | DWCONV               | 100_1_001 | Optional, 11 | default: 1      | byte_base_out  | byte_base_in   |
| Vector Processing    | vp_cfg     | VV_V, VS_V, V_S, V_V | 101_0_000 | Optional, 11 | default: 1      | cfg_addr       | cfg_addr       |
|                      | vv_v_pre   | VV_V                 | 101_0_001 | Optional, 10 | default: 1      | byte_base_in2  | \              |
|                      | vv_v_drv   | VV_V                 | 101_1_000 | Optional, 11 | default: 1      | byte_base_out  | byte_base_in1  |
|                      | vs_v_pre   | VS_V                 | 101_0_010 | Optional, 10 | default: 1      | val_in2        | \              |
|                      | vs_v_drv   | VS_V                 | 101_1_001 | Optional, 11 | default: 1      | byte_base_out  | byte_base_in1  |
|                      | v_s_drv    | V_S                  | 101_1_010 | 101          | val_out         | \              | byte_base_in1  |
|                      | v_v_drv    | V_V                  | 101_1_011 | Optional, 11 | default: 1      | byte_base_out  | byte_base_in1  |
注:xd字段标注为optional表示不强制该指令必须返回response，若需要返回，则返回默认值1.

## CFG Address & Field Allocation 
###  TLD_CFG

| Group | Name | Width | Description | Address | Field |
|-------|------|-------|-------------|----------|--------|
| Precision | cfg_type | 2 | Element data type. | 0 | [1:0] |
| | cfg_wd | 3 | Element data width. | 0 | [4:2] |
| REM of the transferred Tensor | cfg_rem_dim0 | 6 | Dim0 remainder of the transferred Tensor. | 1 | [5:0] |
| SIZE of the transferred Tensor | cfg_size_dim0b | 11 | Dim0b size of the transferred Tensor. | 1 | [16:6] |
| | cfg_size_dim1 | 13 | Dim1 size of the transferred Tensor. | 2 | [12:0] |
| | cfg_size_dim2 | 13 | Dim2 size of the transferred Tensor. | 2 | [25:13] |
| STRIDE of the Tensor in Global Memory | cfg_stride_dim1_gmem | 13 | Dim1 stride of the Tensor stored in Global Memory. | 3 | [12:0] |
| | cfg_stride_dim2_gmem | 25 | Dim2 stride of the Tensor stored in Global Memory. | 4 | [24:0] |
| STRIDE of the Tensor in Local Memory | cfg_stride_dim1_lmem | 13 | Dim1 stride of the Tensor stored in Local Memory. | 5 | [12:0] |
| | cfg_stride_dim2_lmem | 25 | Dim2 stride of the Tensor stored in Local Memory. | 6 | [24:0] |
### ISI_CFG

| Group | Name | Width | Description | Address | Field |
|-------|------|-------|-------------|----------|--------|
| Precision | cfg_type | 2 | Element data type. | 0 | [1:0] |
| | cfg_wd | 3 | Element data width. | 0 | [4:2] |
| REM of the transferred Tensor | cfg_rem_dim0 | 6 | Dim0 remainder of the transferred Tensor. | 1 | [5:0] |
| SIZE of the transferred Tensor | cfg_size_dim0b | 11 | Dim0b size of the transferred Tensor. | 1 | [16:6] |
| | cfg_size_dim1 | 13 | Dim1 size of the transferred Tensor. | 2 | [12:0] |
| | cfg_size_dim2 | 13 | Dim2 size of the transferred Tensor. | 2 | [25:13] |
| STRIDE of the Tensor in Global Memory | cfg_stride_dim1_gmem | 13 | Dim1 stride of the Tensor stored in Global Memory. | 3 | [12:0] |
| | cfg_stride_dim2_gmem | 25 | Dim2 stride of the Tensor stored in Global Memory. | 4 | [24:0] |
| STRIDE of the Tensor in Local Memory | cfg_stride_dim1_lmem | 13 | Dim1 stride of the Tensor stored in Local Memory. | 5 | [12:0] |
| | cfg_stride_dim2_lmem | 25 | Dim2 stride of the Tensor stored in Local Memory. | 6 | [24:0] |

###  TM_CFG

| Group              | Name                | Width | Description              | Address | Field   |
| ------------------ | ------------------- | ----- | ------------------------ | ------- | ------- |
| Precision          | cfg_type            | 2     | Element data type.       | 0       | [1:0]   |
|                    | cfg_wd              | 3     | Element data width.      | 0       | [4:2]   |
| REM                | cfg_rem_dim0        | 6     | Dim0 remainder.          | 1       | [5:0]   |
| SIZE               | cfg_size_dim0b      | 11    | Dim0b size.              | 1       | [16:6]  |
|                    | cfg_size_dim1       | 13    | Dim1 size.               | 2       | [12:0]  |
|                    | cfg_size_dim2       | 13    | Dim2 size.               | 2       | [25:13] |
| STRIDE of Tensor O | cfg_stride_dim1_out | 13    | Dim1 stride of Tensor O. | 3       | [12:0]  |
|                    | cfg_stride_dim2_out | 25    | Dim2 stride of Tensor O. | 4       | [24:0]  |
| STRIDE of Tensor I | cfg_stride_dim1_in  | 13    | Dim1 stride of Tensor I. | 5       | [12:0]  |
|                    | cfg_stride_dim2_in  | 25    | Dim2 stride of Tensor I. | 6       | [24:0]  |

### MP_CFG

| Group              | Name                | Width | Description                                                                                                                                 | Address | Field   |
| ------------------ | ------------------- | ----- | ------------------------------------------------------------------------------------------------------------------------------------------- | ------- | ------- |
| Precision          | cfg_type_out        | 2     | Element data type of output Tensor O.                                                                                                       | 0       | [1:0]   |
|                    | cfg_type_orig       | 2     | Element data type of original Tensor OR.                                                                                                    | 0       | [3:2]   |
|                    | cfg_type_in         | 2     | Element data type of input Tensor I.                                                                                                        | 0       | [5:4]   |
|                    | cfg_type_wt         | 2     | Element data type of weight Matrix W.                                                                                                       | 0       | [7:6]   |
|                    | cfg_wd_ref          | 3     | Element data width for reference.<br>(Element data width of O, OR, I, and W is inferred).                                                   | 0       | [10:8]  |
|                    | cfg_wd_sc_out       | 2     | Element data width scale of output Tensor O;<br>wd_out = wd_ref * 2^cfg_wd_sc_out.                                                          | 0       | [12:11] |
|                    | cfg_wd_sc_orig      | 2     | Element data width scale of original Tensor OR;<br>wd_orig = wd_ref * 2^cfg_wd_sc_orig.                                                     | 0       | [14:13] |
|                    | cfg_wd_sc_in        | 2     | Element data width scale of input Tensor I;<br>wd_in = wd_ref * 2^cfg_wd_sc_in.                                                             | 0       | [16:15] |
|                    | cfg_wd_sc_wt        | 2     | Element data width scale of weight Matrix W;<br>wd_wt = wd_ref * 2^cfg_wd_sc_wt.                                                            | 0       | [18:17] |
| Operation          | cfg_accu            | 1     | Accumulation Configuration:<br>1'b0: no historical partial sum to be accumulated;<br>1'b1: historical partial sum to be accumulated exists. | 0       | [19]    |
|                    | cfg_act             | 1     | Activation function configuration.<br>1'b0: do not conduct activation function.<br>1'b1: conduct the ReLU function.                         | 0       | [20]    |
|                    | cfg_shift           | 5     | Right-shift configuration.<br>Only used for integer output.                                                                                 | 0       | [25:21] |
| REM of Tensor O    | cfg_rem_dim0_out    | 6     | Dim0 remainder of output Tensor O.                                                                                                          | 1       | [5:0]   |
| SIZE of Tensor O   | cfg_size_dim0b_out  | 11    | Dim0b size of output Tensor O.                                                                                                              | 1       | [16:6]  |
|                    | cfg_size_dim1_out   | 13    | Dim1 size of output Tensor O.                                                                                                               | 2       | [12:0]  |
|                    | cfg_size_dim2_out   | 13    | Dim2 size of output Tensor O.                                                                                                               | 2       | [25:13] |
| STRIDE of Tensor O | cfg_stride_dim1_out | 13    | Dim1 stride of output Tensor O.                                                                                                             | 3       | [12:0]  |
|                    | cfg_stride_dim2_out | 25    | Dim1 stride of output Tensor O.                                                                                                             | 4       | [24:0]  |
| REM of Tensor I    | cfg_rem_dim0_in     | 6     | Dim0 remainder of input Tensor I.                                                                                                           | 5       | [5:0]   |
| SIZE of Tensor I   | cfg_size_dim0b_in   | 11    | Dim0b size of input Tensor I.                                                                                                               | 5       | [16:6]  |
|                    | cfg_size_dim1_in    | 13    | Dim1 size of input Tensor I.                                                                                                                | 6       | [12:0]  |
|                    | cfg_size_dim2_in    | 13    | Dim2 size of input Tensor I.                                                                                                                | 6       | [25:13] |
| STRIDE of Tensor I | cfg_stride_dim1_in  | 13    | Dim1 stride of input Tensor I.                                                                                                              | 7       | [12:0]  |
|                    | cfg_stride_dim2_in  | 25    | Dim2 stride of input Tensor I.                                                                                                              | 8       | [24:0]  |
| Kernel             | cfg_k_x             | 5     | X-axis kernel size.                                                                                                                         | 9       | [4:0]   |
|                    | cfg_k_y             | 5     | Y-axis kernel size.                                                                                                                         | 9       | [9:5]   |
|                    | cfg_slide_x         | 5     | X-axis slide stride.<br>注: 不记是每行还是反序存，此处一律理解为滑窗步长.                                                                                          | 9       | [14:10] |
|                    | cfg_slide_y         | 5     | Y-axis slide stride.<br>注: 不记是每行还是反序存，此处一律理解为滑窗步长.                                                                                          | 9       | [19:15] |
|                    | cfg_dl_x            | 5     | X-axis dilation rate.                                                                                                                       | 9       | [24:20] |
|                    | cfg_dl_y            | 5     | Y-axis dilation rate.                                                                                                                       | 9       | [29:25] |
|                    | cfg_log2trs_x       | 3     | log2(X-axis transposed convolution stride)                                                                                                  | 10      | [2:0]   |
|                    | cfg_log2trs_y       | 3     | log2(Y-axis transposed convolution stride)                                                                                                  | 10      | [5:3]   |
| Padding            | cfg_pad_w           | 4     | West padding of input Tensor I.<br>(East padding is inferred.)<br>注: 不记是每行还是反序存,此处一律理解为在图边缘填充的零的个数.                                         | 10      | [9:6]   |
|                    | cfg_pad_n           | 4     | North padding of input Tensor I.<br>(South padding is inferred.)<br>注: 不记是每行还是反序存,此处一律理解为在图边缘填充的零的个数.                                       | 10      | [13:10] |
|                    | cfg_pad_val         | 16    | Padding value configuration; aligned at LSB;<br>The element data width is the same as input Tensor I.                                       | 10      | [29:14] |

### VP_CFG


| Group     | Name               | Width | Description                                                                               | Address | Field   |
| --------- | ------------------ | ----- | ----------------------------------------------------------------------------------------- | ------- | ------- |
| Precision | cfg_type_out       | 2     | Element data width of output Vector/Scalar O.                                             | 0       | [1:0]   |
|           | cfg_type_in1       | 2     | Element data width of input Vector I1.                                                    | 0       | [3:2]   |
|           | cfg_type_in2       | 2     | Element data width of input Vector/Scalar I2.                                             | 0       | [5:4]   |
|           | cfg_wd_ref         | 3     | Element data width for reference.<br>(Element data width of O, I1, and I2 is inferred.)   | 0       | [8:6]   |
|           | cfg_wd_sc_out      | 1     | Element data width scale of output Vector/Scalar O.<br>wd_out = wd_ref * 2^cfg_wd_sc_out. | 0       | [9]     |
|           | cfg_wd_sc_in1      | 1     | Element data width scale of input Vector I1.<br>wd_in1 = wd_ref * 2^cfg_wd_sc_in1.        | 0       | [10]    |
|           | cfg_wd_sc_in2      | 1     | Element data width scale of input Vector/Scalar I2.<br>wd_in2 = wd_ref * 2^cfg_wd_sc_in2. | 0       | [11]    |
| Operation | cfg_op             | 6     | Operation mode.                                                                           | 0       | [17:12] |
| REM       | cfg_rem_dim0_ref   | 6     | Dim0 remainder for reference.<br>(Dim0 remainder of Vector O, I1, and I2 is inferred.)    | 1       | [5:0]   |
| SIZE      | cfg_size_dim0b_ref | 11    | Dim0b size for reference.<br>(Dim0b size of Vector O, I1, and I2 is inferred.)            | 1       | [16:6]  |

