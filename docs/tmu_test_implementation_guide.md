# TMU 测试实现指南

## 1. 测试框架结构

### 1.1 目录结构
```
src/feat_tmu/test/
├── test_base/
│   ├── tmu_test_base.h
│   ├── tmu_test_base.cpp
│   ├── memory_model.h
│   └── memory_model.cpp
├── unit_tests/
│   ├── test_bc.cpp
│   ├── test_mov.cpp
│   ├── test_trans.cpp
│   ├── test_align.cpp
│   └── test_exp_mgmt.cpp
├── integration_tests/
│   ├── test_spad_to_cimc.cpp
│   ├── test_combined_ops.cpp
│   └── test_performance.cpp
├── utils/
│   ├── test_vectors.h
│   ├── test_vectors.cpp
│   ├── reference_model.h
│   └── reference_model.cpp
└── xmake.lua
```

## 2. 测试基础框架实现

### 2.1 TMU测试基类 (tmu_test_base.h)

```cpp
#pragma once
#include <gtest/gtest.h>
#include <systemc>
#include <tlm>
#include <vector>
#include <memory>
#include "feat_tmu.h"
#include "memory_model.h"

namespace feat_tmu_test {

class TMUTestBase : public ::testing::Test {
protected:
    void SetUp() override;
    void TearDown() override;
    
    // TMU模块接口
    void configure_tmu(const TMUTestConfig& config);
    void send_command(uint32_t funct7, uint64_t rs1, uint64_t rs2 = 0);
    void wait_completion();
    
    // 内存操作
    void write_memory(uint64_t addr, const std::vector<uint8_t>& data);
    std::vector<uint8_t> read_memory(uint64_t addr, size_t len);
    void clear_memory();
    
    // 验证工具
    bool verify_exact_match(const std::vector<uint8_t>& actual, 
                           const std::vector<uint8_t>& expected,
                           const std::string& test_name = "");
    bool verify_broadcast_result(uint64_t base_addr, uint64_t scalar_val, 
                                const TMUTestConfig& config);
    bool verify_transpose_result(uint64_t input_addr, uint64_t output_addr,
                                const TMUTestConfig& config);
    
    // 测试数据生成
    std::vector<uint8_t> generate_test_pattern(PatternType type, size_t size, 
                                              uint32_t bit_width);
    std::vector<uint8_t> generate_random_data(size_t size, uint32_t bit_width);
    
    // 地址计算辅助
    uint64_t get_spad_address(uint32_t unit_id, uint64_t offset);
    uint64_t get_cimc_address(uint64_t offset);
    
protected:
    std::unique_ptr<feat_tmu::FeatTMU> m_tmu;
    std::unique_ptr<MemoryModel> m_memory;
    sc_core::sc_clock m_clock;
    sc_core::sc_signal<bool> m_reset;
    
    static const uint64_t SPAD_BASE = 0x10000000;
    static const uint64_t CIMC_BASE = 0x20000000;
    static const size_t MAX_TENSOR_SIZE = 1024 * 1024; // 1MB
};

// 测试配置结构
struct TMUTestConfig {
    uint32_t cfg_type;
    uint32_t cfg_wd;
    uint32_t cfg_size_dim0b;
    uint32_t cfg_size_dim1;
    uint32_t cfg_size_dim2;
    uint32_t cfg_rem_dim0;
    uint32_t cfg_stride_dim1_in;
    uint32_t cfg_stride_dim2_in;
    uint32_t cfg_stride_dim1_out;
    uint32_t cfg_stride_dim2_out;
    
    // 便利函数
    uint32_t get_data_width() const;
    size_t get_total_elements() const;
    size_t get_total_bytes() const;
};

enum class PatternType {
    ZEROS,
    ONES,
    INCREMENTAL,
    ALTERNATING,
    RANDOM
};

} // namespace feat_tmu_test
```

### 2.2 内存模型 (memory_model.h)

```cpp
#pragma once
#include <unordered_map>
#include <vector>
#include <cstdint>
#include "npu_config.h"

namespace feat_tmu_test {

class MemoryModel {
public:
    virtual ~MemoryModel() = default;
    virtual void write(uint64_t addr, const uint8_t* data, size_t len) = 0;
    virtual void read(uint64_t addr, uint8_t* data, size_t len) = 0;
    virtual bool is_valid_address(uint64_t addr, size_t len) = 0;
    virtual void clear() = 0;
    virtual void dump_region(uint64_t start, size_t len, const std::string& filename) = 0;
};

class CombinedMemoryModel : public MemoryModel {
public:
    CombinedMemoryModel();
    ~CombinedMemoryModel() override = default;
    
    void write(uint64_t addr, const uint8_t* data, size_t len) override;
    void read(uint64_t addr, uint8_t* data, size_t len) override;
    bool is_valid_address(uint64_t addr, size_t len) override;
    void clear() override;
    void dump_region(uint64_t start, size_t len, const std::string& filename) override;
    
    // CIMC特定功能
    void write_cimc_physical(uint32_t engine, uint32_t macro, uint32_t row, 
                            uint32_t page, const uint8_t* data);
    void read_cimc_physical(uint32_t engine, uint32_t macro, uint32_t row, 
                           uint32_t page, uint8_t* data);
    bool verify_cimc_layout();
    
private:
    std::unordered_map<uint64_t, uint8_t> m_memory;
    
    // 地址范围定义
    static const uint64_t SPAD_BASE = 0x10000000;
    static const uint64_t SPAD_SIZE = NPUConfig::SPAD_NUM * NPUConfig::SPAD_CAPACITY;
    static const uint64_t CIMC_BASE = 0x20000000;
    static const uint64_t CIMC_SIZE = NPUConfig::TOTAL_WORDS * (NPUConfig::LMEM_WD / 8);
    
    bool is_spad_address(uint64_t addr) const;
    bool is_cimc_address(uint64_t addr) const;
    uint64_t cimc_logical_to_physical(uint64_t logical_offset) const;
};

} // namespace feat_tmu_test
```

## 3. 具体测试用例实现

### 3.1 BC (Broadcast) 测试 (test_bc.cpp)

```cpp
#include "tmu_test_base.h"

namespace feat_tmu_test {

class TMUBroadcastTest : public TMUTestBase {
protected:
    void run_broadcast_test(const TMUTestConfig& config, uint64_t scalar_value,
                           uint64_t output_addr) {
        // 1. 配置TMU
        configure_tmu(config);
        
        // 2. 清空输出区域
        std::vector<uint8_t> zeros(config.get_total_bytes(), 0);
        write_memory(output_addr, zeros);
        
        // 3. 执行BC_PRE和BC_DRV命令
        send_command(instruction::opcode::BC_PRE, scalar_value);
        send_command(instruction::opcode::BC_DRV, output_addr);
        wait_completion();
        
        // 4. 验证结果
        ASSERT_TRUE(verify_broadcast_result(output_addr, scalar_value, config));
    }
};

TEST_F(TMUBroadcastTest, BasicBroadcast_INT8) {
    TMUTestConfig config = {
        .cfg_type = 0,  // INT类型
        .cfg_wd = 1,    // 8bit
        .cfg_size_dim0b = 4,
        .cfg_size_dim1 = 8,
        .cfg_size_dim2 = 2,
        .cfg_rem_dim0 = 0,
        .cfg_stride_dim1_out = 4,
        .cfg_stride_dim2_out = 32
    };
    
    uint64_t test_values[] = {0x00, 0xFF, 0x5A, 0xA5};
    for (auto val : test_values) {
        run_broadcast_test(config, val, get_spad_address(0, 0));
    }
}

TEST_F(TMUBroadcastTest, BroadcastToLargeTensor) {
    TMUTestConfig config = {
        .cfg_type = 0,
        .cfg_wd = 2,    // 16bit
        .cfg_size_dim0b = 16,
        .cfg_size_dim1 = 64,
        .cfg_size_dim2 = 8,
        .cfg_rem_dim0 = 0,
        .cfg_stride_dim1_out = 16,
        .cfg_stride_dim2_out = 1024
    };
    
    run_broadcast_test(config, 0x1234, get_spad_address(1, 0));
}

TEST_F(TMUBroadcastTest, BroadcastWithRemainder) {
    TMUTestConfig config = {
        .cfg_type = 0,
        .cfg_wd = 0,    // 4bit
        .cfg_size_dim0b = 3,  // 最后一个chunk不满
        .cfg_size_dim1 = 5,
        .cfg_size_dim2 = 1,
        .cfg_rem_dim0 = 10,   // 最后chunk中有效元素数
        .cfg_stride_dim1_out = 3,
        .cfg_stride_dim2_out = 15
    };
    
    run_broadcast_test(config, 0x7, get_spad_address(0, 0x1000));
}

TEST_F(TMUBroadcastTest, BroadcastToCIMC) {
    TMUTestConfig config = {
        .cfg_type = 1,  // FP类型
        .cfg_wd = 2,    // 16bit
        .cfg_size_dim0b = 1,
        .cfg_size_dim1 = 64,
        .cfg_size_dim2 = 16,
        .cfg_rem_dim0 = 0,
        .cfg_stride_dim1_out = 1,
        .cfg_stride_dim2_out = 64
    };
    
    run_broadcast_test(config, 0x3C00, get_cimc_address(0)); // FP16: 1.0
}

} // namespace feat_tmu_test
```

### 3.2 MOV (Move) 测试 (test_mov.cpp)

```cpp
#include "tmu_test_base.h"

namespace feat_tmu_test {

class TMUMoveTest : public TMUTestBase {
protected:
    void run_move_test(const TMUTestConfig& config, 
                      uint64_t input_addr, uint64_t output_addr,
                      const std::vector<uint8_t>& input_data) {
        // 1. 配置TMU
        configure_tmu(config);
        
        // 2. 写入测试数据
        write_memory(input_addr, input_data);
        
        // 3. 清空输出区域
        std::vector<uint8_t> zeros(config.get_total_bytes(), 0);
        write_memory(output_addr, zeros);
        
        // 4. 执行MOV_DRV命令
        send_command(instruction::opcode::MOV_DRV, output_addr, input_addr);
        wait_completion();
        
        // 5. 验证结果
        std::vector<uint8_t> output_data = read_memory(output_addr, config.get_total_bytes());
        ASSERT_TRUE(verify_exact_match(output_data, input_data, "MOV operation"));
    }
};

TEST_F(TMUMoveTest, BasicMove_SameStride) {
    TMUTestConfig config = {
        .cfg_type = 0,
        .cfg_wd = 1,  // 8bit
        .cfg_size_dim0b = 2,
        .cfg_size_dim1 = 4,
        .cfg_size_dim2 = 2,
        .cfg_rem_dim0 = 0,
        .cfg_stride_dim1_in = 2,
        .cfg_stride_dim2_in = 8,
        .cfg_stride_dim1_out = 2,
        .cfg_stride_dim2_out = 8
    };
    
    auto input_data = generate_test_pattern(PatternType::INCREMENTAL, 
                                          config.get_total_bytes(), 8);
    
    run_move_test(config, get_spad_address(0, 0), get_spad_address(1, 0), input_data);
}

TEST_F(TMUMoveTest, MoveWithDifferentStrides) {
    TMUTestConfig config = {
        .cfg_type = 0,
        .cfg_wd = 1,
        .cfg_size_dim0b = 1,
        .cfg_size_dim1 = 3,
        .cfg_size_dim2 = 2,
        .cfg_rem_dim0 = 0,
        .cfg_stride_dim1_in = 2,   // 输入stride较大
        .cfg_stride_dim2_in = 6,
        .cfg_stride_dim1_out = 1,  // 输出连续存储
        .cfg_stride_dim2_out = 3
    };
    
    // 生成稀疏输入数据
    std::vector<uint8_t> sparse_input = generate_sparse_pattern(config);
    
    run_move_test(config, get_spad_address(0, 0), get_spad_address(1, 0), sparse_input);
}

TEST_F(TMUMoveTest, MoveSPADToCIMC) {
    TMUTestConfig config = {
        .cfg_type = 1,  // FP类型，触发ALIGN逻辑
        .cfg_wd = 2,    // 16bit
        .cfg_size_dim0b = 1,
        .cfg_size_dim1 = 64,
        .cfg_size_dim2 = 4,
        .cfg_rem_dim0 = 0,
        .cfg_stride_dim1_in = 1,
        .cfg_stride_dim2_in = 64,
        .cfg_stride_dim1_out = 1,
        .cfg_stride_dim2_out = 64
    };
    
    auto input_data = generate_random_data(config.get_total_bytes(), 16);
    
    // 写入SPAD
    uint64_t spad_addr = get_spad_address(0, 0);
    write_memory(spad_addr, input_data);
    
    // 配置TMU
    configure_tmu(config);
    
    // 执行MOV_DRV到CIMC（会触发ALIGN）
    uint64_t cimc_addr = get_cimc_address(0);
    send_command(instruction::opcode::MOV_DRV, cimc_addr, spad_addr);
    wait_completion();
    
    // 验证CIMC中的数据和指数行
    verify_cimc_move_result(config, input_data, cimc_addr);
}

TEST_F(TMUMoveTest, MoveWithBlockBoundary) {
    TMUTestConfig config = {
        .cfg_type = 0,
        .cfg_wd = 1,
        .cfg_size_dim0b = 1,
        .cfg_size_dim1 = 100,  // 需要2个块 (64 + 36)
        .cfg_size_dim2 = 1,
        .cfg_rem_dim0 = 0,
        .cfg_stride_dim1_in = 1,
        .cfg_stride_dim2_in = 100,
        .cfg_stride_dim1_out = 1,
        .cfg_stride_dim2_out = 100
    };
    
    auto input_data = generate_test_pattern(PatternType::ALTERNATING, 
                                          config.get_total_bytes(), 8);
    
    run_move_test(config, get_spad_address(0, 0), get_spad_address(1, 0), input_data);
}

} // namespace feat_tmu_test
```

### 3.3 TRANS (Transpose) 测试 (test_trans.cpp)

```cpp
#include "tmu_test_base.h"

namespace feat_tmu_test {

class TMUTransposeTest : public TMUTestBase {
protected:
    void run_transpose_test(const TMUTestConfig& config,
                           uint64_t input_addr, uint64_t output_addr) {
        // 1. 生成输入数据
        auto input_data = generate_transpose_input(config);
        
        // 2. 配置TMU
        configure_tmu(config);
        
        // 3. 写入输入数据
        write_memory(input_addr, input_data);
        
        // 4. 清空输出区域
        std::vector<uint8_t> zeros(calculate_transpose_output_size(config), 0);
        write_memory(output_addr, zeros);
        
        // 5. 执行TRANS_DRV命令
        send_command(instruction::opcode::TRANS_DRV, output_addr, input_addr);
        wait_completion();
        
        // 6. 验证转置结果
        ASSERT_TRUE(verify_transpose_result(input_addr, output_addr, config));
    }
    
    std::vector<uint8_t> generate_transpose_input(const TMUTestConfig& config) {
        // 生成有序的测试数据，便于验证转置结果
        std::vector<uint8_t> data;
        uint32_t element_size = config.get_data_width() / 8;
        uint32_t value = 0;
        
        for (uint32_t d2 = 0; d2 < config.cfg_size_dim2; ++d2) {
            for (uint32_t d1 = 0; d1 < config.cfg_size_dim1; ++d1) {
                for (uint32_t d0b = 0; d0b < config.cfg_size_dim0b; ++d0b) {
                    // 每个256-bit chunk填充递增值
                    for (uint32_t byte = 0; byte < 32; byte += element_size) {
                        for (uint32_t i = 0; i < element_size; ++i) {
                            data.push_back((value >> (i * 8)) & 0xFF);
                        }
                        value++;
                    }
                }
            }
        }
        return data;
    }
};

TEST_F(TMUTransposeTest, BasicTranspose_16bit) {
    TMUTestConfig config = {
        .cfg_type = 0,
        .cfg_wd = 2,    // 16bit，输入块大小16x4
        .cfg_size_dim0b = 8,
        .cfg_size_dim1 = 32,
        .cfg_size_dim2 = 2,
        .cfg_rem_dim0 = 0,
        .cfg_stride_dim1_in = 8,
        .cfg_stride_dim2_in = 256,
        .cfg_stride_dim1_out = 32,  // 转置后的stride
        .cfg_stride_dim2_out = 2048
    };
    
    run_transpose_test(config, get_spad_address(0, 0), get_spad_address(1, 0));
}

TEST_F(TMUTransposeTest, Transpose_8bit_LargeMatrix) {
    TMUTestConfig config = {
        .cfg_type = 0,
        .cfg_wd = 1,    // 8bit，输入块大小32x2
        .cfg_size_dim0b = 16,
        .cfg_size_dim1 = 128,
        .cfg_size_dim2 = 1,
        .cfg_rem_dim0 = 0,
        .cfg_stride_dim1_in = 16,
        .cfg_stride_dim2_in = 2048,
        .cfg_stride_dim1_out = 128,
        .cfg_stride_dim2_out = 16384
    };
    
    run_transpose_test(config, get_spad_address(0, 0), get_spad_address(2, 0));
}

TEST_F(TMUTransposeTest, Transpose_4bit) {
    TMUTestConfig config = {
        .cfg_type = 0,
        .cfg_wd = 0,    // 4bit，输入块大小64x1
        .cfg_size_dim0b = 4,
        .cfg_size_dim1 = 64,
        .cfg_size_dim2 = 1,
        .cfg_rem_dim0 = 20,  // 最后chunk只有20个有效元素
        .cfg_stride_dim1_in = 4,
        .cfg_stride_dim2_in = 256,
        .cfg_stride_dim1_out = 64,
        .cfg_stride_dim2_out = 256
    };
    
    run_transpose_test(config, get_spad_address(0, 0), get_spad_address(1, 0));
}

TEST_F(TMUTransposeTest, TransposeWithAlignment) {
    TMUTestConfig config = {
        .cfg_type = 1,  // FP类型，会触发TRANS_ALIGN
        .cfg_wd = 2,    // 16bit
        .cfg_size_dim0b = 4,
        .cfg_size_dim1 = 32,
        .cfg_size_dim2 = 8,
        .cfg_rem_dim0 = 0,
        .cfg_stride_dim1_in = 4,
        .cfg_stride_dim2_in = 128,
        .cfg_stride_dim1_out = 32,
        .cfg_stride_dim2_out = 1024
    };
    
    // 生成浮点测试数据
    auto fp_data = generate_fp16_test_data(config);
    
    // 写入SPAD
    uint64_t spad_addr = get_spad_address(0, 0);
    write_memory(spad_addr, fp_data);
    
    // 配置TMU
    configure_tmu(config);
    
    // 执行TRANS_DRV到CIMC（会触发TRANS_ALIGN）
    uint64_t cimc_addr = get_cimc_address(0);
    send_command(instruction::opcode::TRANS_DRV, cimc_addr, spad_addr);
    wait_completion();
    
    // 验证转置+对齐结果
    verify_transpose_align_result(config, fp_data, cimc_addr);
}

} // namespace feat_tmu_test
```

### 3.4 集成测试示例 (test_combined_ops.cpp)

```cpp
#include "tmu_test_base.h"

namespace feat_tmu_test {

class TMUIntegrationTest : public TMUTestBase {
};

TEST_F(TMUIntegrationTest, CompleteWorkflow_DataPreparation) {
    // 模拟完整的数据准备流程：
    // 1. 从DRAM读取原始数据到SPAD
    // 2. 使用BC在SPAD中准备标量
    // 3. 使用MOV将数据移动到另一个SPAD位置
    // 4. 使用TRANS转置数据并存储到CIMC
    // 5. 使用GET_CIMEXP读取指数向量
    
    TMUTestConfig config = {
        .cfg_type = 1,  // FP类型
        .cfg_wd = 2,    // 16bit
        .cfg_size_dim0b = 2,
        .cfg_size_dim1 = 32,
        .cfg_size_dim2 = 4,
        .cfg_rem_dim0 = 0,
        .cfg_stride_dim1_in = 2,
        .cfg_stride_dim2_in = 64,
        .cfg_stride_dim1_out = 32,
        .cfg_stride_dim2_out = 1024
    };
    
    // 步骤1: 准备原始数据
    auto original_data = generate_fp16_test_data(config);
    uint64_t data_addr = get_spad_address(0, 0);
    write_memory(data_addr, original_data);
    
    // 步骤2: 执行转置到CIMC
    configure_tmu(config);
    uint64_t cimc_addr = get_cimc_address(0);
    send_command(instruction::opcode::TRANS_DRV, cimc_addr, data_addr);
    wait_completion();
    
    // 步骤3: 读取指数向量
    uint64_t exp_addr = get_spad_address(1, 0);
    send_command(instruction::opcode::GET_CIMEXP, exp_addr, cimc_addr);
    wait_completion();
    
    // 步骤4: 验证完整流程
    verify_complete_workflow(config, original_data, cimc_addr, exp_addr);
}

TEST_F(TMUIntegrationTest, ErrorRecovery_InvalidAddress) {
    TMUTestConfig config = {
        .cfg_type = 0,
        .cfg_wd = 1,
        .cfg_size_dim0b = 1,
        .cfg_size_dim1 = 8,
        .cfg_size_dim2 = 1,
        .cfg_rem_dim0 = 0,
        .cfg_stride_dim1_in = 1,
        .cfg_stride_dim2_in = 8,
        .cfg_stride_dim1_out = 1,
        .cfg_stride_dim2_out = 8
    };
    
    configure_tmu(config);
    
    // 测试无效地址处理
    uint64_t invalid_addr = 0xDEADBEEF;
    uint64_t valid_addr = get_spad_address(0, 0);
    
    // 应该能够检测到错误并恢复
    send_command(instruction::opcode::MOV_DRV, invalid_addr, valid_addr);
    wait_completion();
    
    // 验证TMU仍能正常工作
    auto test_data = generate_test_pattern(PatternType::INCREMENTAL, 256, 8);
    write_memory(valid_addr, test_data);
    send_command(instruction::opcode::MOV_DRV, get_spad_address(1, 0), valid_addr);
    wait_completion();
    
    auto result = read_memory(get_spad_address(1, 0), test_data.size());
    ASSERT_TRUE(verify_exact_match(result, test_data, "Recovery test"));
}

} // namespace feat_tmu_test
```

## 4. 构建配置 (xmake.lua)

```lua
-- TMU测试目标
target("feat_tmu_test")
    set_kind("binary")
    set_languages("c++17")
    
    -- 添加测试源文件
    add_files("test_base/*.cpp")
    add_files("unit_tests/*.cpp") 
    add_files("integration_tests/*.cpp")
    add_files("utils/*.cpp")
    
    -- 添加头文件目录
    add_includedirs("test_base", {public = true})
    add_includedirs("utils", {public = true})
    
    -- 依赖项
    add_deps("feat_tmu")
    add_deps("common")
    add_deps("local_mem")
    
    -- 第三方库
    add_packages("gtest")
    add_packages("systemc")
    
    -- 编译选项
    add_cxxflags("-Wall", "-Wextra", "-O2")
    add_defines("SC_INCLUDE_DYNAMIC_PROCESSES")
    
    -- 测试运行配置
    after_build(function (target)
        os.exec(target:targetfile() .. " --gtest_output=xml:test_results.xml")
    end)

-- 性能测试目标
target("feat_tmu_perf_test")
    set_kind("binary")
    set_languages("c++17")
    
    add_files("performance_tests/*.cpp")
    add_files("test_base/*.cpp")
    add_files("utils/*.cpp")
    
    add_deps("feat_tmu")
    add_packages("gtest", "systemc")
    
    add_defines("ENABLE_PERFORMANCE_TESTING")
    add_cxxflags("-O3")

-- 覆盖率测试目标  
target("feat_tmu_coverage")
    set_kind("binary")
    set_languages("c++17")
    
    add_files("test_base/*.cpp")
    add_files("unit_tests/*.cpp")
    add_files("integration_tests/*.cpp") 
    add_files("utils/*.cpp")
    
    add_deps("feat_tmu")
    add_packages("gtest", "systemc")
    
    add_cxxflags("--coverage")
    add_ldflags("--coverage")
    
    after_build(function (target)
        os.exec("lcov --capture --directory . --output-file coverage.info")
        os.exec("genhtml coverage.info --output-directory coverage_report")
    end)
```

## 5. 测试执行和自动化

### 5.1 测试脚本 (run_tests.sh)

```bash
#!/bin/bash

# TMU测试执行脚本

set -e

echo "=== TMU测试套件 ==="

# 编译测试
echo "编译测试..."
xmake build feat_tmu_test

# 运行单元测试
echo "运行单元测试..."
./build/feat_tmu_test --gtest_filter="TMU*Test.*" --gtest_output=xml:unit_test_results.xml

# 运行集成测试
echo "运行集成测试..."
./build/feat_tmu_test --gtest_filter="TMUIntegration*" --gtest_output=xml:integration_test_results.xml

# 运行性能测试
echo "运行性能测试..."
xmake build feat_tmu_perf_test
./build/feat_tmu_perf_test --gtest_output=xml:performance_test_results.xml

# 生成覆盖率报告
echo "生成覆盖率报告..."
xmake build feat_tmu_coverage
./build/feat_tmu_coverage

# 合并测试结果
echo "合并测试结果..."
python3 merge_test_results.py unit_test_results.xml integration_test_results.xml performance_test_results.xml > final_test_report.xml

echo "测试完成！报告已生成：final_test_report.xml"
echo "覆盖率报告：coverage_report/index.html"
```

这个测试实现指南提供了：

1. **完整的测试框架结构** - 模块化、可扩展的测试架构
2. **具体的测试用例实现** - 涵盖所有主要功能的详细测试代码
3. **内存模型抽象** - 支持SPAD和CIMC的统一内存接口
4. **自动化构建和执行** - 使用xmake和脚本实现自动化测试流程
5. **覆盖率和性能分析** - 集成的代码覆盖率和性能测试支持

通过这个指南，您可以快速搭建TMU模块的完整测试环境，并根据具体需求扩展测试用例。 