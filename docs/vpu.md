# Overview
![[vpu_overview.png]]
## Parameters

| Group                  | Name         | Constraint                  | Default | Description                       |
| ---------------------- | ------------ | --------------------------- | ------- | --------------------------------- |
| RISC-V                 | RV_XLEN      | 32, 64                      | 32      | RISC-V寄存器宽度                       |
| Local Memory           | LMEM_WD      | 256                         |         | Local Memory数据宽度                  |
|                        | LMEM_DP      |                             | 2048    | Local Memory深度                    |
|                        | LMEM_ADDR_WD | log2(LMEM_DP)               |         | Local Memory地址宽度                  |
| Vector Processing Unit | VPE_NUM      | 8的整数倍                       | 16      | Vector Processing Engine (VPE) 个数 |
| Access Channel         | CH_SIZE_MAX  | VPE_NUM*32/LMEM_WD          |         |                                   |
|                        | CH_SIZE_WD   | max($clog2(CH_SIZE_MAX), 1) |         |                                   |
|                        |              |                             |         |                                   |

## VP_CFG

| Group     | Name               | Width | Description                                                                            | Address | Field   |
| --------- | ------------------ | ----- | -------------------------------------------------------------------------------------- | ------- | ------- |
| Precision | cfg_type_out       | 2     | Element data width of output Vector/Scalar O.                                          | 0       | [1:0]   |
|           | cfg_type_in1       | 2     | Element data width of input Vector I1.                                                 | 0       | [3:2]   |
|           | cfg_type_in2       | 2     | Element data width of input Vector/Scalar I2.                                          | 0       | [5:4]   |
|           | cfg_wd_ref         | 3     | Element data width for reference. (Element data width of O, I1, and I2 is inferred.)   | 0       | [8:6]   |
|           | cfg_wd_sc_out      | 1     | Element data width scale of output Vector/Scalar O. wd_out = wd_ref * 2^cfg_wd_sc_out. | 0       | [9]     |
|           | cfg_wd_sc_in1      | 1     | Element data width scale of input Vector I1. wd_in1 = wd_ref * 2^cfg_wd_sc_in1.        | 0       | [10]    |
|           | cfg_wd_sc_in2      | 1     | Element data width scale of input Vector/Scalar I2. wd_in2 = wd_ref * 2^cfg_wd_sc_in2. | 0       | [11]    |
| Operation | cfg_op             | 6     | Operation mode.                                                                        | 0       | [17:12] |
| REM       | cfg_rem_dim0_ref   | 6     | Dim0 remainder for reference. (Dim0 remainder of Vector O, I1, and I2 is inferred.)    | 1       | [5:0]   |
| SIZE      | cfg_size_dim0b_ref | 11    | Dim0b size for reference. (Dim0b size of Vector O, I1, and I2 is inferred.)            | 1       | [16:6]  |

## cfg_singals

| Configuration signals | width        | Description                                                                                                                                        |
| --------------------- | ------------ | -------------------------------------------------------------------------------------------------------------------------------------------------- |
| cfg_type_out          | 3            | Element data type of output Vector/Scalar O.                                                                                                       |
| cfg_wd_out            | 3            | Element data width scale of output Vector/Scalar O.                                                                                                |
| cfg_type_in1          | 3            | Element data type of input Vector I1.                                                                                                              |
| cfg_wd_in1            | 3            | Element data width of input Vector I1.                                                                                                             |
| cfg_type_in2          | 3            | Element data type of input Vector/Scalar I2.                                                                                                       |
| cfg_wd_in2            | 3            | Element data width of input Vector/Scalar I2.                                                                                                      |
| cfg_op                | 6            | Operation mode.                                                                                                                                    |
| cfg_size_dim0         | 16           | Dim0 size of Vector O, I1, I2.                                                                                                                     |
| cfg_funct_sel         | 2            | Function selection signal: <br> 2'b00: VectorVector-Vector; <br> 2'b01: VectorScalar-Vector; <br> 2'b10: Vector-Scalar; <br> 2'b11: Vector-Vector. |
| cfg_base_out          | LMEM_ADDR_WD | Base address of output Vector O stored in Local Memory.                                                                                            |
| cfg_base_in1          | LMEM_ADDR_WD | Base address of input Vector I1 stored in Local Memory.                                                                                            |
| cfg_base_in2          | LMEM_ADDR_WD | Base address of Vector I2 stored in Local Memory.                                                                                                  |
| cfg_val_in2           | RV_XLEN      | Value of input Scalar I2; aligned at LSB.                                                                                                          |

### prim_cfgval

| Group     | Configuration      | Width | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               | Constraint                                                                   |
| --------- | ------------------ | ----- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------- |
| Precision | cfg_type_out       | 2     | Element data type of output Vector O.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |                                                                              |
|           | cfg_type_in1       | 2     | Element data type of input Vector I1.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |                                                                              |
|           | cfg_type_in2       | 2     | Element data type of input Vector I2.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |                                                                              |
|           | cfg_wd_ref         | 3     | Element data width for reference. <br>(Element data width of O, I1, and I2 is inferred.)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |                                                                              |
|           | cfg_wd_sc_out      | 1     | Element data width scale of output Vector O. <br>wd_out = wd_ref * 2^cfg_wd_sc_out.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | Enabled option: <br>Single-width: 000; <br>Widening: 100; <br>Narrowing: 010 |
|           | cfg_wd_sc_in1      | 1     | Element data width scale of input Vector I1. <br>wd_in1 = wd_ref * 2^cfg_wd_sc_in1.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                              |
|           | cfg_wd_sc_in2      | 1     | Element data width scale of input Vector I2. <br>wd_in2 = wd_ref * 2^cfg_wd_sc_in2.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |                                                                              |
| Operation | cfg_op             | 6     | Add/Subtract: <br>6'b000_000: add; <br>6'b000_001: sub; //I1-I2 <br>6'b000_010: rsub; //reversed subtract: I2-I1 <br><br>Min/Max: <br>6'b001_000: min; <br>6'b001_001: max; <br><br>Compare: <br>6'b010_000: equal; <br>6'b010_001: not equal; <br>6'b010_010: greater; <br>6'b010_011: greater or equal; <br>6'b010_100: less; <br>6'b010_101: less or equal; <br><br>Shift: <br>6'b011_000: left-shift; <br>6'b011_001: right-shift floor; <br>6'b011_010: right-shift ceil; <br>6'b011_011: right-shift round; <br><br>Bitwise logical <br>6'b100_000: and; <br>6'b100_001: or; <br>6'b100_010: xor; <br><br>Mul: <br>6'b101_000: mul. |                                                                              |
| REM       | cfg_rem_dim0_ref   | 6     | Dim0 remainder for reference. <br>(Dim0 remainder of Vector O, I1, and I2 is inferred.)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | [0, 63]                                                                      |
| SIZE      | cfg_size_dim0b_ref | 11    | Dim0b size for reference. <br>(Dim0b size of Vector O, I1, and I2 is inferred.)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | [1, 1024]                                                                    |
# VPU_Core
## overview
![[vpu_core_overview.png]]
如上图所示，vpu_core主要由以下部分组成：
1. **vpe_arr**：Vector Processing Engine Array，是vpu的核心数据通路，由VPE_NUM个Vector Processing Engine（VPE）组成；能够根据配置作SIMD（VV_V、VS_V、V_V）或Inter-VPE Reduction（V_S）计算；
2. **intra_vpe_rdc**：辅助vpe_arr进行Reduction计算的数据通路，对单个VPE输出结果进行Intra-VPE Reduction操作，输出标量结果；仅用于V_S计算；
3. **Par2Ser & Ser2Par**：并串转换与串并转换模块，并串比例均等于LMEM_WD/(32*VPE_NUM)；
4. **vpu_rd_req_tx**：连续生成读请求，通过访存通道ch1_rd与ch2_rd送往Local Memory，用于读取vpe_arr的输入向量数据I1、I2；
5. **vpu_rd_ack_tx**：通过访存通道ch1_rd与ch2_rd从Local Memory收集vpe_arr的输入向量数据I1、I2，对齐后送往Par2Ser；
6. **vpu_wr_req_tx**：在作SIMD计算时从Ser2Par接收vpe_arr的输出向量数据O，对齐后通过访存通道ch0_wr送往Local Memory；   
7. **vpu_wr_ack_rx**：在作SIMD计算时确认输出向量O的数据已经全部写入Local Memory。
## 工作流程
### 全局-局部信号映射
| 目标子模块                          | 局部配置信号              | 全局配置信号                                                                                                   | 映射关系                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | 说明                                                                                                                                                                                               |
| ------------------------------ | ------------------- | -------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| intra_vpe_rdc                  | cfg_wd_out          | cfg_wd_ref<br>cfg_wd_sc_out                                                                              | cfg_wd_out = cfg_wd_ref + cfg_wd_sc_out                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | cfg_wd_out用于描述输出元素的宽度；<br>通过用于描述参考元素宽度的信号cfg_wd_ref，以及用于描述输出元素宽度相对大小的信号cfg_wd_sc_out进行计算。                                                                                                        |
| vpu_rd_req_tx<br>vpu_rd_ack_rx | cfg_wd_sc_inv       | cfg_wd_sc_out<br>cfg_wd_sc_in1<br>cfg_wd_sc_in2                                                          | cfg_wd_sc = {cfg_wd_sc_out, cfg_wd_sc_in1, cfg_wd_sc_in2};<br>if(cfg_wd_sc == 3'b000):<br>    cfg_wd_sc_inv = 3'b000;<br>else:<br>    cfg_wd_sc_inv = ~cfg_wd_sc;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               | 与全局配置信号cfg_wd_sc_out、cfg_wd_sc_in1以及cfg_wd_sc_in2一样，cfg_wd_sc_rec用于描述I1、I2、O的元素宽度相对大小；为了适应当前VPE设计方案，采用了分数表示方法，即：<br>当cfg_wd_sc_inv[0]为0时，输出向量/标量O元素宽度即为I1、I2、O中元素宽度中最大值；反之则为最大值的一半。<br>I1、I2亦同理。 |
| vpu_rd_req_tx<br>vpu_rd_ack_rx | cfg_size_dim0b_wide | cfg_wd_sc_out<br>cfg_wd_sc_in1<br>cfg_wd_sc_in2<br>cfg_wd_ref<br>cfg_size_dim0b_ref<br>cfg_rem_dim0b_ref | cfg_wd_sc = {cfg_wd_sc_out, cfg_wd_sc_in1, cfg_wd_sc_in2};<br>if (cfg_wd_sc == 3'b000):<br>    cfg_size_dim0b_wide = cfg_size_dim0b_ref;<br>else if (cfg_wd_ref == '4bit'):<br>    cfg_size_dim0b_wide = cfg_size_dim0b_ref * 2 - (cfg_rem_dim0b_ref <= LMEM_WD/4/2);<br>else if (cfg_wd_ref == '8bit'):<br>    cfg_size_dim0b_wide = cfg_size_dim0b_ref * 2 - (cfg_rem_dim0b_ref <= LMEM_WD/8/2);<br>else if (cfg_wd_ref == '16bit'):<br>    cfg_size_dim0b_wide = cfg_size_dim0b_ref * 2 - (cfg_rem_dim0b_ref <= LMEM_WD/16/2);<br>else if(cfg_wd_ref == '32bit'):<br>    cfg_size_dim0b_wide = cfg_size_dim0b_ref * 2 - (cfg_rem_dim0b_ref <= LMEM_WD/32/2); | cfg_size_dim0b_wide用于描述向量I1、I2、O中元素宽度最大者，在dim0b维度上的元素个数。                                                                                                                                         |
## VPE
### Ports
| Group         | Name         | Width | Direction | Description    |
| ------------- | ------------ | ----- | --------- | -------------- |
| Config signal | cfg_prec_in1 | 5     | input     | 输入操作数1的数据格式    |
|               | cfg_prec_in2 | 5     | input     | 输入操作数2的数据格式    |
|               | cfg_prec_out | 5     | input     | 输出操作数的数据格式     |
|               | cfg_op       | 6     | input     | Operation mode |
| Operands      | i_data1      | 32    | input     | 输入操作数1         |
|               | i_data2      | 32    | input     | 输入操作数2         |
|               | o_data       | 32    | output    | 输出操作数          |
### 行为描述
#### 输入/输出数据格式
![[vpe_datatype.png]]

对于Single-Width操作的Input、Output,Narrowing操作的Input，Widening操作的Output,采用FORMAT1；
对于Narrowing操作的Output，Widening的Input，采用FORMAT2；
**Format1: 直接拼接**
- **INT4:** 输入或输出可以连续拼接 8 个 INT4 数据，等效于一个 32 位数据包含 8 个 INT4 数据。
- **INT8:** 输入或输出可以连续拼接 4 个 INT8 数据，等效于一个 32 位数据包含 4 个 INT8 数据。
- **INT16/FP16/BF16:** 输入或输出可以连续拼接 2 个 INT16、FP16 或 BF16 数据，等效于一个 32 位数据包含 2 个 INT16、FP16 或 BF16 数据。
- **INT32/FP32:** 输入或输出直接使用一个 INT32 或 FP32 数据，等效于一个 32 位数据包含 1 个 INT32 或 FP32 数据。
**Format2: 间隔补0**
- **INT4:** 输入或输出通过间隔填充 0 的方式存储，即 INT4, 0, INT4, 0, INT4, 0, INT4, 0，等效于一个 32 位数据包含 4 个 INT4 数据。
- **INT8:** 输入或输出通过间隔填充 0 的方式存储，即 0, INT8, 0, INT8，等效于一个 32 位数据包含 2 个 INT8 数据。
- **INT16:** 输入或输出通过间隔填充 0 的方式存储，即 INT16,0，等效于一个 32 位数据包含 1 个 INT16 数据。这里的 0 填充在高位。
#### 并行度设定
以下表中Signle-width Integer Operation add （第一行）为例，其中两个输入精度都为INT4,按照上述输入/输出格式，则一个PE的32bit输入可以填充8个INT4（等同于下表的等效并行度）。
其中上、下溢出处理：**取饱和**。

| Group | Operation | prec_out | prec_in1 | prec_in2 | 等效并行度 | 硬件资源需求 |
|--------|-----------|-----------|-----------|-----------|------------|--------------|
| Single-width Integer Operation | add/sub/rsub | INT4 | INT4 | INT4 | 8 | 8x 4bit add |
| | | INT8 | INT8 | INT8 | 4 | 4x 8bit adder |
| | | INT16 | INT16 | INT16 | 2 | 2x 16bit adder |
| | | INT32 | INT32 | INT32 | 1 | 1x 32bit adder |
| | max/min | INT4 | INT4 | INT4 | 8 | 8x 4bit adder |
| | | INT8 | INT8 | INT8 | 4 | 4x 8bit adder |
| | Compare | INT4 | INT4 | INT4 | 8 | 8x 4bit adder |
| | | INT8 | INT8 | INT8 | 4 | 4x 8bit adder |
| | Logical | INT4 | INT4 | INT4 | 8 | |
| | | INT8 | INT8 | INT8 | 4 | |
| | shift | INT4 | INT4 | INT4 | 8 | |
| | | INT8 | INT8 | INT8 | 4 | |
| Widening Integer Operation | add/sub/rsub | INT8 | INT4 | INT4 | 4 | 4x 8bit adder |
| | | INT16 | INT8 | INT8 | 2 | 2x 16bit adder |
| | | INT32 | INT16 | INT16 | 1 | 1x 32bit adder |
| | mul | INT8 | INT4 | INT4 | 4 | 4x 4bit multiplier |
| | | INT16 | INT8 | INT8 | 2 | 2x 8bit multiplier |
| Narrowing Integer Operation | right shift | INT4 | INT8 | INT4 | 4 | |
| | | INT8 | INT16 | INT8 | 2 | |
| | | INT16 | INT32 | INT16 | 1 | |
| Single-width Floating-point Operation | add/sub/rsub | BF16 | BF16 | BF16 | 2 | 2x 8bit adder |
| | | FP16 | FP16 | FP16 | 2 | 2x 11bit adder |
| | | FP32 | FP32 | FP32 | 1 | 1x 24bit adder |
| | min/max | BF16 | BF16 | BF16 | 2 | 2x 16bit adder |
| | | FP16 | FP16 | FP16 | 2 | 2x 16bit adder |
| | Compare | BF16 | BF16 | BF16 | 2 | 2x 16bit adder |
| | | FP16 | FP16 | FP16 | 2 | 2x 16bit adder |
| | mul | BF16 | BF16 | BF16 | 2 | 2x 8bit multiplier |
| | | FP16 | FP16 | FP16 | 2 | 2x 11bit multiplier |
| Widening Floating-point Operation | add | FP32 | BF16 | BF16 | 1 | 1x 24bit adder |
| | | FP32 | FP16 | FP16 | 1 | 1x 24bit adder |
| | mul | FP32 | BF16 | BF16 | 1 | 1x 8bit multiplier |
| | | FP32 | FP16 | FP16 | 1 | 1x 11bit multiplier |
| Single-width Conversion | FP-INT | INT16 | BF16 | \ | 2 | |
| | | INT16 | FP16 | \ | 2 | |
| | | INT32 | FP32 | \ | 1 | |
| | INT-FP | BF16 | INT16 | \ | 2 | |
| | | FP16 | INT16 | \ | 2 | |
| | | FP32 | INT32 | \ | 1 | |
| | FP-FP | FP16 | BF16 | \ | 2 | |
| | | BF16 | FP16 | \ | 2 | |
| Widening Conversion | FP-INT | INT32 | BF16 | \ | 1 | |
| | | INT32 | FP16 | \ | 1 | |
| | INT-FP | BF16 | INT8 | \ | 2 | |
| | | FP16 | INT8 | \ | 2 | |
| | | FP32 | INT16 | \ | 1 | |
| | FP-FP | FP32 | BF16 | \ | 1 | |
| | | FP32 | FP16 | \ | 1 | |
| Narrowing Conversion | FP-INT | INT8 | BF16 | \ | 2 | |
| | | INT8 | FP16 | \ | 2 | |
| | | INT16 | FP32 | \ | 1 | |
| | INT-FP | BF16 | INT32 | \ | 1 | |
| | | FP16 | INT32 | \ | 1 | |
| | FP-FP | BF16 | FP32 | \ | 1 | |
| | | FP16 | FP32 | \ | 1 | |


#### V_S_Primitive
| Group                           | Operation  | prec_out | prec_in1 | prec_in2 |
| ------------------------------- | ---------- | -------- | -------- | -------- |
| **Integer**                     |            |          |          |          |
| Integer Single-width Sum        | sum        | INT4     | INT4     | \        |
|                                 |            | INT8     | INT8     | \        |
|                                 |            | INT16    | INT16    | \        |
|                                 |            | INT32    | INT32    | \        |
| Integer Widening Sum            | sum        | INT8     | INT4     | \        |
|                                 |            | INT16    | INT8     | \        |
|                                 |            | INT32    | INT16    | \        |
| Integer Min/Max                 | min/max    | INT4     | INT4     | \        |
|                                 |            | INT8     | INT8     | \        |
|                                 |            | INT16    | INT16    | \        |
|                                 |            | INT32    | INT32    | \        |
| Integer Bitwise Logical         | and/or/xor | INT4     | INT4     | \        |
|                                 |            | INT8     | INT8     | \        |
|                                 |            | INT16    | INT16    | \        |
|                                 |            | INT32    | INT32    | \        |
| **Floating-point**              |            |          |          |          |
| Floating-point Single-width Sum | sum        | FP16     | FP16     | \        |
|                                 |            | BF16     | BF16     | \        |
|                                 |            | FP32     | FP32     | \        |
| Floating-point Widening Sum     | sum        | FP32     | FP16     | \        |
|                                 |            | FP32     | BF16     | \        |
| Floating-point Min/Max          | min/max    | FP16     | FP16     | \        |
|                                 |            | BF16     | BF16     | \        |