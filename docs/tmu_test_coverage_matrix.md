# TMU 测试覆盖率矩阵

## 1. 功能覆盖率矩阵

### 1.1 操作类型覆盖

| 操作类型 | 基础功能 | 边界条件 | 错误处理 | 性能测试 | 状态 |
|---------|---------|---------|---------|---------|------|
| BC (Broadcast) | ✅ TMU_BC_001 | ✅ TMU_BC_002 | ✅ TMU_ERR_001 | ✅ TMU_PERF_001 | 完成 |
| MOV (Move) | ✅ TMU_MOV_001 | ✅ TMU_MOV_004 | ✅ TMU_ERR_002 | ✅ TMU_PERF_001 | 完成 |
| TRANS (Transpose) | ✅ TMU_TRANS_001 | ✅ TMU_TRANS_003 | ✅ TMU_ERR_002 | ✅ TMU_PERF_002 | 完成 |
| MOV-ALIGN | ✅ TMU_ALIGN_001 | ✅ TMU_BOUND_003 | ✅ TMU_ERR_003 | ✅ TMU_PERF_002 | 完成 |
| TRANS-ALIGN | ✅ TMU_ALIGN_002 | ✅ TMU_BOUND_003 | ✅ TMU_ERR_003 | ✅ TMU_PERF_002 | 完成 |
| SET_CIMEXP | ✅ TMU_ALIGN_003 | ✅ TMU_BOUND_001 | ✅ TMU_ERR_001 | - | 完成 |
| GET_CIMEXP | ✅ TMU_ALIGN_003 | ✅ TMU_BOUND_001 | ✅ TMU_ERR_001 | - | 完成 |

### 1.2 数据类型覆盖

| 数据类型 | 位宽 | BC测试 | MOV测试 | TRANS测试 | ALIGN测试 | 覆盖率 |
|---------|------|-------|--------|----------|----------|-------|
| INT4 | 4bit | ✅ TMU_BC_003 | ✅ TMU_MOV_002 | ✅ TMU_TRANS_003 | - | 75% |
| INT8 | 8bit | ✅ TMU_BC_001 | ✅ TMU_MOV_001 | ✅ TMU_TRANS_002 | - | 75% |
| INT16 | 16bit | ✅ TMU_BC_002 | ✅ TMU_MOV_003 | ✅ TMU_TRANS_001 | - | 75% |
| FP8 | 8bit | 🔄 计划中 | 🔄 计划中 | 🔄 计划中 | ✅ TMU_ALIGN_001 | 25% |
| FP16 | 16bit | ✅ TMU_BC_004 | ✅ TMU_MOV_003 | ✅ TMU_TRANS_004 | ✅ TMU_ALIGN_001 | 100% |
| BF16 | 16bit | 🔄 计划中 | 🔄 计划中 | 🔄 计划中 | ✅ TMU_ALIGN_002 | 25% |

### 1.3 内存模式覆盖

| 内存模式 | 读取测试 | 写入测试 | 地址计算 | 物理布局 | 覆盖率 |
|---------|---------|---------|---------|---------|-------|
| SPAD → SPAD | ✅ TMU_MOV_001 | ✅ TMU_MOV_001 | ✅ TMU_MOV_001 | ✅ TMU_MOV_001 | 100% |
| SPAD → CIMC | ✅ TMU_MOV_003 | ✅ TMU_MOV_003 | ✅ TMU_MOV_003 | ✅ TMU_MOV_003 | 100% |
| CIMC → SPAD | ✅ GET_CIMEXP | ✅ GET_CIMEXP | ✅ GET_CIMEXP | ✅ GET_CIMEXP | 100% |
| CIMC → CIMC | 🔄 计划中 | 🔄 计划中 | 🔄 计划中 | 🔄 计划中 | 0% |

## 2. 边界条件覆盖矩阵

### 2.1 张量尺寸边界

| 边界条件 | 测试用例 | 验证点 | 预期结果 | 状态 |
|---------|---------|-------|---------|------|
| 最小张量 (1,1,1) | TMU_BOUND_001 | 无分块处理 | 正确执行 | ✅ |
| 单个块边界 (64,1,1) | TMU_MOV_002 | 块边界处理 | 正确分块 | ✅ |
| 跨块边界 (100,1,1) | TMU_MOV_004 | 边界块填充 | 填充区域为0 | ✅ |
| 最大配置值 | TMU_BOUND_002 | 无溢出 | 正确处理 | ✅ |
| dim0余数=0 | TMU_BC_001 | 完整块 | 无掩码 | ✅ |
| dim0余数>0 | TMU_BC_003 | 最后块掩码 | 正确掩码 | ✅ |

### 2.2 Stride配置边界

| Stride配置 | 测试用例 | 验证点 | 预期结果 | 状态 |
|-----------|---------|-------|---------|------|
| 连续存储 (stride=size) | TMU_MOV_001 | 线性访问 | 地址连续 | ✅ |
| 稀疏存储 (stride>size) | TMU_MOV_002 | 跳跃访问 | 地址正确 | ✅ |
| 最小stride (stride=1) | TMU_BC_001 | 紧密排列 | 无间隙 | ✅ |
| 输入输出stride不同 | TMU_MOV_002 | 重排列 | 布局转换正确 | ✅ |

## 3. 块处理覆盖矩阵

### 3.1 输入块大小覆盖

| 数据位宽 | 块大小 (dim1×dim0b) | 测试用例 | 验证点 | 状态 |
|---------|-------------------|---------|-------|------|
| 4bit | 64×1 | TMU_TRANS_003 | 4bit转置 | ✅ |
| 8bit | 32×2 | TMU_TRANS_002 | 8bit转置 | ✅ |
| 16bit | 16×4 | TMU_TRANS_001 | 16bit转置 | ✅ |
| FP16 | 16×4 | TMU_TRANS_004 | FP16转置+对齐 | ✅ |

### 3.2 块边界处理

| 场景 | 测试用例 | 验证点 | 预期结果 | 状态 |
|------|---------|-------|---------|------|
| 完整块 | TMU_MOV_001 | 无填充 | 全部有效数据 | ✅ |
| 部分块 (dim1方向) | TMU_MOV_004 | 行填充 | 超出行为0 | ✅ |
| 部分块 (dim0b方向) | TMU_TRANS_003 | 列填充 | 超出列为0 | ✅ |
| 边角块 | TMU_BOUND_003 | 双向填充 | 填充区域为0 | ✅ |

## 4. CIMC物理地址覆盖

### 4.1 地址映射覆盖

| 物理组件 | 逻辑索引 | 测试用例 | 验证点 | 状态 |
|---------|---------|---------|-------|------|
| Engine (0-3) | block_idx_d1 | TMU_MOV_003 | 引擎映射 | ✅ |
| Macro (0-1) | rel_idx_d1/32 | TMU_ALIGN_001 | 宏映射 | ✅ |
| Row (0-31) | rel_idx_d1%32 | TMU_ALIGN_001 | 行映射 | ✅ |
| Page (0-15) | idx_d2 | TMU_MOV_003 | 页映射 | ✅ |
| 指数行 (32) | - | TMU_ALIGN_003 | 指数存储 | ✅ |

### 4.2 地址计算验证

| 计算公式组件 | 测试用例 | 验证方法 | 状态 |
|-------------|---------|---------|------|
| E × WORDS_PER_ENGINE | TMU_MOV_003 | 地址检查 | ✅ |
| M × WORDS_PER_MACRO | TMU_ALIGN_001 | 地址检查 | ✅ |
| R × PAGES_PER_MACRO_ROW | TMU_ALIGN_001 | 地址检查 | ✅ |
| + P | TMU_MOV_003 | 地址检查 | ✅ |
| 边界检查 | TMU_ERR_001 | 溢出检测 | ✅ |

## 5. 浮点对齐覆盖

### 5.1 对齐算法覆盖

| 对齐场景 | 测试用例 | 验证点 | 状态 |
|---------|---------|-------|------|
| 32元素列对齐 | TMU_ALIGN_001 | 最大指数提取 | ✅ |
| 指数向量生成 | TMU_ALIGN_001 | 指数计算 | ✅ |
| 尾数移位对齐 | TMU_ALIGN_001 | 数据对齐 | ✅ |
| 指数行写入 | TMU_ALIGN_001 | CIMC指数存储 | ✅ |
| 64块处理 | TMU_ALIGN_002 | 批量对齐 | ✅ |

### 5.2 指数管理覆盖

| 操作 | 测试用例 | 验证点 | 状态 |
|------|---------|-------|------|
| SET_CIMEXP | TMU_ALIGN_003 | SPAD→CIMC指数 | ✅ |
| GET_CIMEXP | TMU_ALIGN_003 | CIMC→SPAD指数 | ✅ |
| 8宏指数处理 | TMU_ALIGN_003 | 完整指数向量 | ✅ |
| 指数行索引(32) | TMU_ALIGN_003 | 正确行定位 | ✅ |

## 6. 错误处理覆盖

### 6.1 地址错误覆盖

| 错误类型 | 测试用例 | 检测方法 | 恢复验证 | 状态 |
|---------|---------|---------|---------|------|
| 无效SPAD地址 | TMU_ERR_001 | 范围检查 | 错误返回 | ✅ |
| 无效CIMC地址 | TMU_ERR_001 | 范围检查 | 错误返回 | ✅ |
| CIMC偏移溢出 | TMU_ERR_001 | 边界检查 | 操作跳过 | ✅ |
| 地址对齐错误 | TMU_ERR_001 | 对齐检查 | 错误返回 | 🔄 |

### 6.2 配置错误覆盖

| 配置错误 | 测试用例 | 检测点 | 状态 |
|---------|---------|-------|------|
| 无效数据类型 | TMU_ERR_003 | 类型验证 | ✅ |
| 尺寸不匹配 | TMU_ERR_002 | 尺寸检查 | ✅ |
| stride配置错误 | TMU_ERR_002 | stride验证 | 🔄 |

## 7. 性能覆盖

### 7.1 吞吐量测试覆盖

| 操作类型 | 数据量 | 测试用例 | 指标 | 状态 |
|---------|-------|---------|------|------|
| BC | 1MB | TMU_PERF_001 | 周期/字节 | ✅ |
| MOV | 1MB | TMU_PERF_001 | 周期/字节 | ✅ |
| TRANS | 1MB | TMU_PERF_002 | 周期/字节 | ✅ |
| ALIGN | 1MB | TMU_PERF_002 | 周期/字节 | ✅ |

### 7.2 压力测试覆盖

| 压力类型 | 测试用例 | 验证点 | 状态 |
|---------|---------|-------|------|
| 连续操作 | TMU_STRESS_001 | 稳定性 | ✅ |
| 随机操作序列 | TMU_STRESS_002 | 正确性 | ✅ |
| 内存满载 | TMU_STRESS_003 | 资源管理 | 🔄 |

## 8. 集成测试覆盖

### 8.1 操作组合覆盖

| 组合场景 | 测试用例 | 验证点 | 状态 |
|---------|---------|-------|------|
| BC → MOV | Integration_001 | 数据流 | ✅ |
| MOV → TRANS | Integration_001 | 数据流 | ✅ |
| TRANS → ALIGN | Integration_001 | 数据流 | ✅ |
| ALIGN → GET_EXP | Integration_001 | 完整流程 | ✅ |

### 8.2 跨内存模式覆盖

| 模式转换 | 测试用例 | 验证点 | 状态 |
|---------|---------|-------|------|
| SPAD → CIMC → SPAD | Integration_002 | 往返一致性 | 🔄 |
| 多SPAD操作 | Integration_003 | 多源数据 | 🔄 |

## 9. 测试覆盖率统计

### 9.1 总体覆盖率

| 维度 | 已覆盖 | 总计 | 覆盖率 | 目标 |
|------|-------|------|-------|------|
| 功能测试 | 35 | 40 | 87.5% | 100% |
| 边界测试 | 18 | 20 | 90% | 95% |
| 错误测试 | 8 | 12 | 66.7% | 90% |
| 性能测试 | 4 | 6 | 66.7% | 80% |
| 集成测试 | 2 | 4 | 50% | 80% |

### 9.2 代码覆盖率目标

| 覆盖类型 | 当前值 | 目标值 | 状态 |
|---------|-------|-------|------|
| 行覆盖率 | 92% | 95% | 🔄 |
| 分支覆盖率 | 88% | 90% | 🔄 |
| 函数覆盖率 | 100% | 100% | ✅ |
| 条件覆盖率 | 85% | 90% | 🔄 |

## 10. 待完善的测试项

### 10.1 高优先级

1. **FP8/BF16数据类型测试** - 完善浮点类型覆盖
2. **CIMC到CIMC操作** - 补充内存模式覆盖  
3. **地址对齐错误处理** - 完善错误处理覆盖
4. **stride配置验证** - 加强配置检查

### 10.2 中优先级

1. **内存满载压力测试** - 验证资源管理
2. **SPAD往返测试** - 验证数据一致性
3. **多源数据集成测试** - 复杂场景验证
4. **性能回归测试** - 持续性能监控

### 10.3 低优先级

1. **功耗测试** - 电源管理验证
2. **并发操作测试** - 多TMU实例
3. **热点分析** - 性能瓶颈识别

---

**说明:**
- ✅ 已完成
- 🔄 进行中
- ❌ 失败/阻塞
- 📋 计划中

此覆盖率矩阵应定期更新，确保测试覆盖的全面性和准确性。 