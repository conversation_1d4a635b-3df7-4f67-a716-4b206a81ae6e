```mermaid
sequenceDiagram
    participant CFG as Config Interface
    participant TMU as TMU Core
    participant RD as TMURdAckRx
    participant WR as TMUWrReqTx
    participant MEM as Memory System

    %% Configuration Phase
    CFG->>TMU: b_transport(cfg_cmd)
    Note over TMU: Parse funct7
    
    alt TM_CFG
        TMU->>TMU: m_cfg.writeReg(rs1val, rs2val)
    else BC_PRE
        TMU->>TMU: val_in = rs1val
    else BC_DRV
        TMU->>TMU: byte_base_out = rs1val
        TMU->>TMU: notify(drv_event)
    else MOV_DRV/TRANS_DRV
        TMU->>TMU: byte_base_out = rs1val
        TMU->>TMU: byte_base_in = rs2val
        TMU->>TMU: notify(drv_event)
    end

    %% Operation Execution Phase
    alt BC Operation
        TMU->>TMU: process_bc()
        TMU->>WR: process_write()
        WR->>MEM: b_transport(write)
    else MOV Operation
        TMU->>TMU: process_mov()
        TMU->>RD: process_read()
        RD->>MEM: b_transport(read)
        MEM-->>RD: read_data
        TMU->>WR: process_write()
        WR->>MEM: b_transport(write)
    else TRANS Operation
        TMU->>TMU: process_trans()
        TMU->>RD: process_read()
        RD->>MEM: b_transport(read)
        MEM-->>RD: read_data
        TMU->>WR: process_write()
        WR->>MEM: b_transport(write)
    end
```

```mermaid
flowchart TD
    A[Start] --> B{Parse funct7}
    
    B -->|TM_CFG| C[Write Config Register]
    B -->|BC_PRE| D[Set val_in]
    B -->|BC_DRV| E[Set byte_base_out]
    B -->|MOV/TRANS_DRV| F[Set byte_base_in/out]
    
    E & F --> G[Notify drv_event]
    G --> H{Process Operation}
    
    H -->|BC| I[Generate Broadcast Data]
    I --> J[Process Write via TMUWrReqTx]
    
    H -->|MOV| K[Calculate Addresses]
    K --> L[Read via TMURdAckRx]
    L --> M[Process Data]
    M --> J
    
    H -->|TRANS| N[Calculate Addresses]
    N --> O[Read via TMURdAckRx]
    O --> P[Transpose Data]
    P --> J
    
    J --> Q[End]
```

