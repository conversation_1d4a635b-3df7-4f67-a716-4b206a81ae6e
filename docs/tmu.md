
如上图所示, tmu_core 主要由以下六个模块组成:
1) tmu_rd_req_tx: 根据相关配置信号,以及Local Memory中存储张量的基地址(用于MOV和TRANS任务),生成读取Local Memory的地址信息和控制信号;
2) tmu_rd_ack_rx: 将接收到的来自Local Memory 的数据通过tmu 访存通道ch0_rd读取到tmu中,将读取到的信息发送到tmu_datapath单元,进行数据的后续处理。
3) tmu_wr_req_tx: 接收来自tmu_datapath 单元(BC和MOV任务)/TRANS_module单元(TRANS任务)处理后的数据,依据相关配置信号,以及local memory中存储的输出张量存储的基地址,将数据由tmu的访存通道ch0_wr存入Local Memory.
4) tmu_wr_ack_rx:用于确认数据已经完全写入Local Memory中。
5) tmu_datapath:根据当前的张量处理任务,对数据的来源和处理方式进行控制。具体而言:
    对于BC指令,输入scalar来自寄存器,产生的输出tensor O通过tum_wr_req_tx 将数据重复指定次数写入Local Memory.
    对于MOV指令,输入tensor I来自tma_rd_ack_rx,输出tensor O通过tum_wr_req_tx 写入Local Memory。
    对于TRANS指令,输入tensor I来自tma_rd_ack_rx,输出需要送往TRANS_module进行转置处理。
6) TRANS_module:用于完成转置操作,并将结果送入tmu_wr_req_tx
## ISA-TMU_CFG

| Group | Name | Width | Description | Address | Field |
|---|---|---|---|---|---|
| Precision | `cfg_type` | 2 | Element data type. | 0 | [1:0] |
|  | `cfg_wd` | 3 | Element data width. | 0 | [4:2] |
| REM | `cfg_rem_dim0` | 6 | Dim0 remainder. | 1 | [5:0] |
| SIZE | `cfg_size_dimob` | 11 | Dim0b size. | 1 | [16:6] |
|  | `cfg_size_dim1` | 13 | Dim1 size. | 2 | [12:0] |
|  | `cfg_size_dim2` | 13 | Dim2 size. | 2 | [25:13] |
| STRIDE of Tensor O | `cfg_stride_dim1_out` | 13 | Dim1 stride of Tensor O. | 3 | [12:0] |
|  | `cfg_stride_dim2_out` | 25 | Dim2 stride of Tensor O. | 4 | [24:0] |
| STRIDE of Tensor I | `cfg_stride_dim1_in` | 13 | Dim1 stride of Tensor I. | 5 | [12:0] |
|  | `cfg_stride_dim2_in` | 25 | Dim2 stride of Tensor I. | 6 | [24:0] |

## Tensor Manipulation
### BC Primitive
BC Primitive 完成如下操作:
step1:根据byte_base_out寻址到存储在Local Memory中的三维Tensor 0;跳到step2;
step2:根据相应配置信号,将Scalar I广播到Tensor O的对应位置;结束执行。
#### **prim_rs/rdval**

| Field       | Value         | Description                                           | Constraint |
| ----------- | ------------- | ----------------------------------------------------- | ---------- |
| prim_rdval  | \             | \                                                     |            |
| prim_rs3val | \             |                                                       |            |
| prim_rs2val | val_in        | Value of Scalar I.                                    |            |
| prim_rs1val |               |                                                       |            |
| prim_rsoval | byte_base_out | Base byte address of Tensor O stored in Local Memory. |            |


#### **prim_cfgval**

| Group              | Configuration       | Width | Description                 | Constraint     |
| ------------------ | ------------------- | ----- | --------------------------- | -------------- |
| Precision-related  | cfg_type            | 2     | Element data type.          |                |
|                    | cfg_wd              | 3     | Elemet data width.          |                |
| REM                | cfg_rem_dim0        | 6     | Dim0 remainder of Tensor 0. | [0, 63]        |
| SIZE of Tensor O   | cfg_size_dim0b      | 11    | Dim0b size of Tensor O.     | [1, 1024]      |
|                    | cfg_size_dim1       | 13    | Dim1 size of Tensor O.      | [1, 4096]      |
|                    | cfg_size_dim2       | 13    | Dim2 size of Tensor O.      | [1, 4096]      |
| STRIDE of Tensor O | cfg_stride_dim1_out | 13    | Dim1 stride of Tensor O.    | [1, 4096]      |
|                    | cfg_stride_dim2_out | 25    | Dim2 stride of Tensor O.    | [1, 4096*4096] |
### MOV Primitive
完成如下操作:
step1:根据byte_base_in寻址到存储在Local Memory中的Tensor I;根据byte_base_out寻址到存储在Local Memory中的Tensor 0;
step2:将Tensor I读出,再将其写入Tensor O的对应位置;结束执行。

#### prim_rs/rdval

| Field | Value | Description | Constraint |
|---|---|---|---|
| prim_rd1val |  |  |  |
| prim_rd0val |  |  |  |
| prim_rs3val |  |  |  |
| prim_rs2val |  |  |  |
| prim_rs0val | byte_base_out | Base byte address of Tensor O stored in Local Memory. |  |
| prim_rs1val | byte_base_in | Base byte address of Tensor I stored in Local Memory. |  |


### TRANS Primitive
完成如下操作:
step1:根据byte_base_in寻址到存储在Local Memory中的Tensor I,将其读出;跳到step2;
step2:对Tensor I作转置(交换dim0与dim1),根据byte_base_out将其存储到Local Memory:结束执行。
#### prim_rs/rdval

| Field | Value | Description | Constraint |
|---|---|---|---|
| prim_rd1val | \ | \ |  |
| prim_rd0val |  |  |  |
| prim_rs3val |  |  |  |
| prim_rs2val |  |  |  |
| prim_rs0val | byte_base_out | Base byte address of Tensor O stored in Local Memory. |  |
| prim_rs1val | byte_base_in | Base byte address of Tensor I stored in Local Memory. |  |

