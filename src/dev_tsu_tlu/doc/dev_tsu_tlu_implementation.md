# DEV_TSU_TLU 实现文档

## 1. 优化的实现结构

```mermaid
flowchart TD
    subgraph External
        NPU[NPU Module]
        LMEM[Local Memory]
        GMEM[Global Memory]
    end

    subgraph DEV_TSU_TLU[DEV_TSU_TLU Module]
        TLU[TLU Module]
        TSU[TSU Module]
        
        subgraph TLU Components
            TLD_CFG[TLD Config Manager]
            TLU_PROC[Transfer Processing]
        end
        
        subgraph TSU Components
            TST_CFG[TST Config Manager]
            TSU_PROC[Transfer Processing]
        end
        
        TLU --> TLD_CFG
        TLU --> TLU_PROC
        TSU --> TST_CFG
        TSU --> TSU_PROC
    end

    NPU --> |Config Socket| TLU & TSU
    TLU --> |GMEM Socket| GMEM
    TLU --> |LMEM Socket| LMEM
    TSU --> |GMEM Socket| GMEM
    TSU --> |LMEM Socket| LMEM
```

### 优化的数据类型和结构

```cpp
// 通用数据类型
using Word256b = std::array<uint8_t, NPUConfig::LMEM_WD/8>;
using IssueQueueCmd = instruction::IssueQueueCmd;

// 配置类型
using TldConfigManager = hardware_instruction::TldConfigManager;
using TstConfigManager = hardware_instruction::TstConfigManager;
using TensorTransferConfig = hardware_instruction::TensorTransferConfig;

// 数据传输结构
struct DataOut {
    bool data_valid;
    Word256b data;
};
```

### 优化的 TLU/TSU 工作流

```mermaid
sequenceDiagram
    participant NPU
    participant Config as Config Manager
    participant Proc as Transfer Processing
    participant Memory as GMEM/LMEM

    NPU->>Config: Configure (CFG Command)
    Config->>Config: Write Register
    NPU->>Proc: Drive Transfer (DRV Command)
    Proc->>Config: Get Config
    Proc->>Proc: Calculate Strides & Masks
    
    loop For each dimension
        alt TLU Process
            Proc->>Memory: Read from GMEM
            Memory-->>Proc: Return Data
            Proc->>Proc: Apply Mask if needed
            Proc->>Memory: Write to LMEM
        else TSU Process
            Proc->>Memory: Read from LMEM
            Memory-->>Proc: Return Data
            Proc->>Proc: Apply Mask if needed
            Proc->>Memory: Write to GMEM
        end
    end
```

## 2. 关键实现特性

1. 统一的数据类型：
   - 用于 256 位数据传输的 Word256b
   - 来自 NPU Config 的通用配置结构
   - 使用 IssueQueueCmd 的标准化命令接口

2. 内存传输优化：
   - 与内存的直接 socket 连接
   - 高效的 stride 计算
   - 部分传输的 mask 生成
   - 传输失败的错误处理

3. 配置管理：
   - TLU 的 TldConfigManager
   - TSU 的 TstConfigManager
   - 一致的寄存器字段结构

4. 传输处理：
   - 基于维度的迭代（dim2、dim1、dim0b）
   - 可配置的 stride 计算
   - 支持余数处理
   - 部分传输的 mask 应用

## 3. 核心组件

### TLU 实现
```cpp
class TLU {
    // 配置
    TldConfigManager config_manager;
    TensorTransferConfig m_cfg;
    
    // 传输处理
    void process_transfer();
    DataOut read_from_gmem();
    bool write_to_lmem();
    
    // 辅助函数
    void calculate_strides();
    bool send_read_request();
};
```

### TSU 实现
```cpp
class TSU {
    // 配置
    TstConfigManager config_manager;
    TensorTransferConfig m_cfg;
    
    // 传输处理
    void process_transfer();
    DataOut read_from_lmem();
    bool write_to_gmem();
    
    // 辅助函数
    void calculate_strides();
    bool send_read_request();
};
```

## 4. 关键优势

1. 简化的架构：
   - 移除了不必要的抽象层
   - 直接内存访问实现
   - 配置和处理的清晰分离

2. 改进的可维护性：
   - 标准化的数据类型和结构
   - 一致的错误处理
   - 明确定义的组件职责

3. 增强的性能：
   - 优化的内存访问模式
   - 高效的 mask 生成和应用
   - 减少数据路径的开销

4. 更好的调试支持：
   - 系统化的错误报告
   - 清晰的数据流跟踪
   - 一致的日志机制

## 5. 未来考虑

1. 性能优化：
   - 考虑添加数据预取
   - 针对特定模式优化 mask 生成
   - 评估并行传输机会

2. 错误处理：
   - 添加更详细的错误报告
   - 实现恢复机制
   - 考虑添加事务重试逻辑

3. 配置扩展：
   - 支持额外的传输模式
   - 动态 stride 计算
   - 高级 masking 方案

