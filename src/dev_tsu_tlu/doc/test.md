# DEV_TSU_TLU 测试框架文档

## 1. 测试框架概览

DEV_TSU_TLU 测试框架为验证 Transfer Load Unit (TLU) 和 Transfer Store Unit (TSU) 的功能和性能提供了全面的测试环境。本文档概述了测试架构、组件、策略和实现指南。

```mermaid
flowchart TD
    subgraph Test Framework
        TestDriver[Test Driver]
        TestBench[Test Bench]
        TestGenerator[Test Generator]
        TestDataManager[Test Data Manager]
        TestExecutor[Test Executor]
        TestVerifier[Test Verifier]
    end

    subgraph DUT[Device Under Test]
        TLU[TLU Module]
        TSU[TSU Module]
        GMEM[Global Memory]
        LMEM[Local Memory]
    end

    TestDriver --> TestGenerator
    TestDriver --> TestExecutor
    TestExecutor --> TestBench
    TestGenerator --> TestDataManager
    TestDataManager --> TestExecutor
    TestBench --> TLU & TSU
    TestBench --> GMEM & LMEM
    TestVerifier --> GMEM & LMEM
    TestExecutor --> TestVerifier
```

## 2. 测试组件

### 2.1 核心测试组件

#### 2.1.1 TestBench
TestBench 为 TLU 和 TSU 模块提供测试环境。它处理：
- 与模块的配置接口
- 测试数据的内存管理
- 命令执行和监控
- 结果验证协调

```cpp
class DevTsuTluTestBench {
public:
    // 初始化和控制
    void initialize();
    void reset();
    
    // 配置
    void configureTLU(const TldConfig& config);
    void configureTSU(const TstConfig& config);
    
    // 命令执行
    void sendTLUCommand(const IssueQueueCmd& cmd);
    void sendTSUCommand(const IssueQueueCmd& cmd);
    
    // 内存操作
    void writeGMEM(uint64_t address, const Word256b& data);
    Word256b readGMEM(uint64_t address);
    void writeLMEM(uint64_t address, const Word256b& data);
    Word256b readLMEM(uint64_t address);
    void clearGMEM();
    void clearLMEM();
    
    // 验证
    bool verifyGMEMContent(uint64_t address, const Word256b& expected);
    bool verifyLMEMContent(uint64_t address, const Word256b& expected);
    bool verifyTLUTransfer(uint64_t gmem_addr, uint64_t lmem_addr, size_t size);
    bool verifyTSUTransfer(uint64_t lmem_addr, uint64_t gmem_addr, size_t size);
};
```

#### 2.1.2 TestGenerator
TestGenerator 创建具有各种配置和场景的测试用例：

```cpp
class DevTsuTluTestGenerator {
public:
    // 配置生成
    TldConfig generateTLUConfig(uint32_t dim0, uint32_t dim1, uint32_t dim2);
    TstConfig generateTSUConfig(uint32_t dim0, uint32_t dim1, uint32_t dim2);
    
    // 测试用例生成
    TLUTestCase generateTLUTestCase(TestType type);
    TSUTestCase generateTSUTestCase(TestType type);
    
    // 测试套件生成
    TestSuite generateTestSuite();
    
    // 验证
    bool validateTLUConfig(const TldConfig& config);
    bool validateTSUConfig(const TstConfig& config);
};
```

#### 2.1.3 TestDataManager
TestDataManager 处理测试数据生成和管理：

```cpp
class DevTsuTluTestDataManager {
public:
    // 数据生成
    Word256b generatePatternData(PatternType type, uint32_t seed);
    void generateTensorData(TensorData& tensor, uint32_t dim0, uint32_t dim1, uint32_t dim2);
    
    // 期望结果生成
    TensorData generateExpectedTLUResult(const TensorData& gmem_data, const TldConfig& config);
    TensorData generateExpectedTSUResult(const TensorData& lmem_data, const TstConfig& config);
    
    // 内存填充
    void populateGMEM(DevTsuTluTestBench& testbench, uint64_t base_addr, const TensorData& data);
    void populateLMEM(DevTsuTluTestBench& testbench, uint64_t base_addr, const TensorData& data);
};
```

#### 2.1.4 TestExecutor
TestExecutor 运行测试用例并管理测试过程：

```cpp
class DevTsuTluTestExecutor {
public:
    // 测试执行
    bool executeTLUTest(const TLUTestCase& test);
    bool executeTSUTest(const TSUTestCase& test);
    TestResult executeTestSuite(const TestSuite& suite);
    
    // 测试设置和清理
    void setupTest(const TestCase& test);
    void teardownTest();
    
    // 结果处理
    TestResult verifyResult(const TestCase& test);
};
```

#### 2.1.5 TestDriver
TestDriver 是测试框架的主控制器：

```cpp
class DevTsuTluAutoTestDriver {
public:
    // 测试执行
    TestResult runBasicTests();
    TestResult runTestSuite();
    TestResult runCornerCaseTests();
    
    // 状态和报告
    bool success();
    std::string error_msg();
    void generateReport(const std::string& filename);
};
```

## 3. 测试策略

### 3.1 功能测试

#### 3.1.1 TLU 测试
- **配置测试**：验证各种 TLU 配置的正确处理
- **传输测试**：验证从 GMEM 到 LMEM 的正确数据传输
- **Mask 测试**：验证部分传输的 mask 正确应用
- **Stride 计算**：验证不同维度的 stride 正确计算

#### 3.1.2 TSU 测试
- **配置测试**：验证各种 TSU 配置的正确处理
- **传输测试**：验证从 LMEM 到 GMEM 的正确数据传输
- **Mask 测试**：验证部分传输的 mask 正确应用
- **Stride 计算**：验证不同维度的 stride 正确计算

### 3.2 边界情况测试

- **边界条件**：测试最小和最大维度值
- **对齐问题**：测试不同内存对齐场景
- **边缘情况**：测试 stride 和维度的边界条件
- **错误处理**：测试错误条件和恢复机制

### 3.3 性能测试

- **吞吐量测试**：测量不同配置下的数据传输速率
- **延迟测试**：测量命令响应时间
- **压力测试**：在高负载条件下测试

## 4. 测试序列

```mermaid
sequenceDiagram
    participant Driver as TestDriver
    participant Generator as TestGenerator
    participant Executor as TestExecutor
    participant Bench as TestBench
    participant DataMgr as TestDataManager
    participant TLU as TLU
    participant TSU as TSU
    participant GMEM as Global Memory
    participant LMEM as Local Memory

    Driver->>Generator: generateTestSuite()
    Generator-->>Driver: test_suite

    loop For each test case
        Driver->>Executor: executeTest(test_case)
        Executor->>Bench: reset()
        Bench->>TLU: reset()
        Bench->>TSU: reset()
        Bench->>GMEM: clearMemory()
        Bench->>LMEM: clearMemory()

        alt TLU Test
            Executor->>DataMgr: generateTensorData()
            DataMgr-->>Executor: gmem_data
            Executor->>Bench: populateGMEM(gmem_data)
            Bench->>GMEM: writeMemory()
            Executor->>Bench: configureTLU(tlu_config)
            Bench->>TLU: b_transport(cfg)
            Executor->>Bench: sendTLUCommand(tlu_cmd)
            Bench->>TLU: b_transport(cmd)
            TLU->>GMEM: read_request()
            GMEM-->>TLU: read_response()
            TLU->>LMEM: write_request()
            Executor->>DataMgr: generateExpectedTLUResult()
            DataMgr-->>Executor: expected_lmem_data
            Executor->>Bench: verifyTLUTransfer()
            Bench->>LMEM: readMemory()
            LMEM-->>Bench: memory_data
        else TSU Test
            Executor->>DataMgr: generateTensorData()
            DataMgr-->>Executor: lmem_data
            Executor->>Bench: populateLMEM(lmem_data)
            Bench->>LMEM: writeMemory()
            Executor->>Bench: configureTSU(tsu_config)
            Bench->>TSU: b_transport(cfg)
            Executor->>Bench: sendTSUCommand(tsu_cmd)
            Bench->>TSU: b_transport(cmd)
            TSU->>LMEM: read_request()
            LMEM-->>TSU: read_response()
            TSU->>GMEM: write_request()
            Executor->>DataMgr: generateExpectedTSUResult()
            DataMgr-->>Executor: expected_gmem_data
            Executor->>Bench: verifyTSUTransfer()
            Bench->>GMEM: readMemory()
            GMEM-->>Bench: memory_data
        end
    end
```

## 5. 测试数据流

```mermaid
graph TD
    subgraph Test Generation
        Generator[Test Generator]
        Config[Configuration Generation]
        TestData[Test Data Generation]
        ExpectedResults[Expected Results]
    end

    subgraph Test Execution
        Executor[Test Executor]
        TLUTests[TLU Tests]
        TSUTests[TSU Tests]
    end

    subgraph Data Management
        DataManager[Data Manager]
        Pattern[Pattern Generation]
        Tensor[Tensor Data]
        Verify[Data Verification]
    end

    subgraph Verification
        MemVerify[Memory Verification]
        TransferVerify[Transfer Verification]
        ResultCheck[Result Validation]
    end

    Generator --> |Generate Test Cases| Executor
    Config --> Generator
    TestData --> Generator
    
    Executor --> TLUTests & TSUTests
    Executor --> DataManager
    
    DataManager --> Pattern
    DataManager --> Tensor
    DataManager --> Verify
    
    TLUTests & TSUTests --> MemVerify
    Pattern --> TransferVerify
    Tensor --> TransferVerify
    
    MemVerify --> ResultCheck
    TransferVerify --> ResultCheck
    ExpectedResults --> ResultCheck
```

## 6. 测试用例示例

### 6.1 基本 TLU 传输测试

```cpp
// 基本 TLU 传输的测试用例
TLUTestCase basic_tlu_test;
basic_tlu_test.name = "Basic TLU Transfer";
basic_tlu_test.description = "验证从 GMEM 到 LMEM 的基本传输";
basic_tlu_test.config.dim0 = 4;
basic_tlu_test.config.dim1 = 4;
basic_tlu_test.config.dim2 = 4;
basic_tlu_test.config.gmem_base_addr = 0x1000;
basic_tlu_test.config.lmem_base_addr = 0x2000;
basic_tlu_test.config.stride_gmem_dim0 = 1;
basic_tlu_test.config.stride_gmem_dim1 = 4;
basic_tlu_test.config.stride_gmem_dim2 = 16;
basic_tlu_test.config.stride_lmem_dim0 = 1;
basic_tlu_test.config.stride_lmem_dim1 = 4;
basic_tlu_test.config.stride_lmem_dim2 = 16;
basic_tlu_test.pattern_type = SEQUENTIAL;
```

### 6.2 带 Masking 的 TSU 传输测试

```cpp
// 带 masking 的 TSU 传输测试用例
TSUTestCase tsu_mask_test;
tsu_mask_test.name = "TSU Mask Test";
tsu_mask_test.description = "验证带部分数据 masking 的 TSU 传输";
tsu_mask_test.config.dim0 = 3; // 部分 word
tsu_mask_test.config.dim1 = 4;
tsu_mask_test.config.dim2 = 4;
tsu_mask_test.config.lmem_base_addr = 0x2000;
tsu_mask_test.config.gmem_base_addr = 0x3000;
tsu_mask_test.config.stride_lmem_dim0 = 1;
tsu_mask_test.config.stride_lmem_dim1 = 4;
tsu_mask_test.config.stride_lmem_dim2 = 16;
tsu_mask_test.config.stride_gmem_dim0 = 1;
tsu_mask_test.config.stride_gmem_dim1 = 4;
tsu_mask_test.config.stride_gmem_dim2 = 16;
tsu_mask_test.pattern_type = RANDOM;
```

## 7. 实现指南

### 7.1 Test Bench 实现

Test Bench 应实现这些关键功能：
1. 与 TLU 和 TSU 配置接口的 socket 连接
2. GMEM 和 LMEM 的内存管理
3. 命令生成和传输
4. 通过内存比较进行结果验证

### 7.2 Memory Model 实现

Memory Model 应支持：
1. Word256b 存储
2. 随机访问
3. 正确的地址对齐
4. 原子读/写操作

### 7.3 配置生成

配置生成应考虑：
1. 有效的维度范围
2. 正确的地址对齐
3. 有效的 stride 计算
4. 部分传输的 mask 生成

### 7.4 验证策略

验证应实现为：
1. 传输数据的逐字节比较
2. 未触及内存区域的验证
3. 正确处理 masked 数据
4. 传输错误的检测

## 8. 参考实现

测试实现应遵循类似以下的结构：

```
dev_tsu_tlu/test/
├── dev_tsu_tlu_test_bench.h/cpp
├── dev_tsu_tlu_test_generator.h/cpp
├── dev_tsu_tlu_test_data.h/cpp
├── dev_tsu_tlu_test_executor.h/cpp
├── dev_tsu_tlu_auto_test_driver.h/cpp
├── dev_tsu_tlu_test_cases.h
├── dev_tsu_tlu_test_main.cpp
├── utils/
│   ├── memory_model.h/cpp
│   └── result_analyzer.h/cpp
└── xmake.lua
```
