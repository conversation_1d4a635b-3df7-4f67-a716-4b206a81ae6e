#include "global_memory.h"

GlobalMemory::GlobalMemory(sc_core::sc_module_name name)
    : sc_module(name), target_socket("target_socket"), m_delay(sc_core::SC_ZERO_TIME)
{
    target_socket.register_b_transport(this, &GlobalMemory::b_transport);

    memory.resize(MEMORY_SIZE, 0);
}

bool GlobalMemory::check_address(uint64_t addr, uint32_t len)
{
    if (addr + len >= MEMORY_SIZE)
    {
        return false;
    }
    return true;
}

void GlobalMemory::b_transport(int id, tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
{
    tlm::tlm_command cmd = trans.get_command();
    sc_dt::uint64 addr = trans.get_address();
    unsigned char* ptr = trans.get_data_ptr();
    unsigned int len = trans.get_data_length();

    if (!check_address(addr, len))
    {
        trans.set_response_status(tlm::TLM_ADDRESS_ERROR_RESPONSE);
        return;
    }

    if (cmd == tlm::TLM_READ_COMMAND)
    {
        memcpy(ptr, memory.data() + addr, len);
    }
    else if (cmd == tlm::TLM_WRITE_COMMAND)
    {
        memcpy(memory.data() + addr, ptr, len);
    }
    trans.set_response_status(tlm::TLM_OK_RESPONSE);
}