#pragma once

#include <tlm_utils/multi_passthrough_target_socket.h>
#include <tlm_utils/simple_target_socket.h>
#include <systemc>
#include <tlm>
#include <vector>
#include "sysc/kernel/sc_time.h"

class GlobalMemory : public sc_core::sc_module
{
  public:
    tlm_utils::multi_passthrough_target_socket<GlobalMemory> target_socket;

    SC_HAS_PROCESS(GlobalMemory);
    explicit GlobalMemory(sc_core::sc_module_name name);
    bool check_address(uint64_t addr, uint32_t len);
    void b_transport(int id, tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);

    // Add reset method
    void reset()
    {
        memory.clear();
        memory.resize(MEMORY_SIZE, 0);
    }

  private:
    std::vector<uint8_t> memory;
    sc_core::sc_time m_delay;
    static constexpr size_t MEMORY_SIZE = 16 * 1024 * 1024;  // 16MB default size
};