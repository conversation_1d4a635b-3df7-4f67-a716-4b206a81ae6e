target("dev_tsu_tlu")
    set_kind("static")
    add_includedirs(".",{public=true})
    add_files("*.cpp")
    add_deps("common")
    add_deps("utils")
    add_deps("local_mem")
    add_deps("sc_logger")
target("test_dev_tsu_tlu")
    set_kind("binary")
    add_includedirs("./test")
    add_files("test/**.cpp")
    add_deps("dev_tsu_tlu")
    add_tests("test_dev_tsu_tlu", {group = "dev_tsu_tlu"})

includes("../local_mem/xmake.lua")