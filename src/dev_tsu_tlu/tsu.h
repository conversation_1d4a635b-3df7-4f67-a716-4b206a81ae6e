#pragma once

#include <tlm_utils/simple_initiator_socket.h>
#include <tlm_utils/simple_target_socket.h>
#include <systemc>
#include <tlm>
#include "npu_config.h"
#include "utils/register.h"

using namespace hardware_instruction;
using namespace instruction;

class TSU : public sc_core::sc_module
{
  public:
    using Word256b = std::array<uint8_t, NPUConfig::LMEM_WD / 8>;
    using IssueQueueCmd = instruction::IssueQueueCmd;
    using TstConfigManager = hardware_instruction::TstConfigManager;
    using TsuConfig = hardware_instruction::TensorTransferConfig;
    struct DataOut
    {
        bool data_valid;
        Word256b data;
    };

    // Configuration socket from NPU
    tlm_utils::simple_target_socket<TSU> target_cfg;
    // Memory access sockets
    tlm_utils::simple_initiator_socket<TSU> initiator_gmem;
    tlm_utils::simple_initiator_socket<TSU> initiator_lmem;

    SC_HAS_PROCESS(TSU);
    explicit TSU(sc_core::sc_module_name name);
    void b_transport(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);

  private:
    // Address structure
    uint64_t byte_base_lmem;
    uint64_t byte_base_gmem;

    // Members
    IssueQueueCmd m_issue_queue_cmd;
    sc_core::sc_time req_lmem_delay, req_gmem_delay;
    tlm::tlm_generic_payload trans;
    Word256b data_buf;
    TstConfigManager config_manager;
    TsuConfig m_cfg;
    uint8_t m_prec;
    // Events
    sc_core::sc_event recv_event;

    // Core functions
    void thread_cmd();
    void process_transfer();
    DataOut read_from_lmem(uint64_t rd_byte_addr, Word256b& mask_rd, bool need_mask);
    bool write_to_gmem(uint64_t wr_byte_addr, Word256b& data);
    bool send_read_request(uint64_t addr, Word256b& data);

    // Helper functions
    void calculate_strides(uint32_t& stride_dim0b_bytes,
                           uint32_t& stride_dim2_bytes_gmem,
                           uint32_t& stride_dim1_bytes_gmem,
                           uint32_t& stride_dim2_bytes_lmem,
                           uint32_t& stride_dim1_bytes_lmem);
};