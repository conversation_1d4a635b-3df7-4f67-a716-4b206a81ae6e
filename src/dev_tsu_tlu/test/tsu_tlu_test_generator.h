#pragma once

#include <memory>
#include <random>
#include <string>
#include <vector>
#include "npu_config.h"
#include "tsu_tlu_test_cases.h"

namespace test
{

// Test configuration parameters
struct TestConfigParams
{
    uint32_t min_dim0;
    uint32_t max_dim0;
    uint32_t min_dim1;
    uint32_t max_dim1;
    uint32_t min_dim2;
    uint32_t max_dim2;
    uint32_t min_stride;
    uint32_t max_stride;
};

// Test generator class
class TSUTLUTestGenerator
{
  public:
    explicit TSUTLUTestGenerator(const TestConfigParams& params);

    // Basic test generation
    TensorTransferTestCase generateBasicTest(bool is_tlu);
    TensorTransferTestCase generateMaskedTest(bool is_tlu);
    TensorTransferTestCase generateDimensionTest(bool is_tlu);

    // Test suite generation
    std::vector<TensorTransferTestCase> generateTestSuite();
    std::vector<TensorTransferTestCase> generateCornerCaseTestSuite();

    // Helper methods
    hardware_instruction::TensorTransferConfig generateConfig(bool is_tlu);
    uint32_t generateDimension(uint32_t min_dim, uint32_t max_dim);
    uint32_t generateStride(uint32_t dim_size, uint32_t min_stride);
    uint64_t generateAddress(uint32_t alignment = 32);
    uint8_t generateDataType();

  private:
    bool validateConfig(const hardware_instruction::TensorTransferConfig& config);
    bool checkDimensionConstraints(const hardware_instruction::TensorTransferConfig& config);
    bool checkStrideConstraints(const hardware_instruction::TensorTransferConfig& config);
    bool checkDataTypeConstraints(const hardware_instruction::TensorTransferConfig& config);

    TestConfigParams m_params;
    std::mt19937 m_random_engine;
};

}  // namespace test