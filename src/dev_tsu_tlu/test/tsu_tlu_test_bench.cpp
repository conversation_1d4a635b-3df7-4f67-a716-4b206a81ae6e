#include "tsu_tlu_test_bench.h"
#include <chrono>
#include <cstring>
#include <random>
#include "utils/utils.h"
namespace test
{

TSUTLUTestBench::TSUTLUTestBench(sc_core::sc_module_name name)
    : sc_core::sc_module(name),
      cfg_socket("cfg_socket"),
      m_tlu(new class TLU("tlu")),
      m_tsu(new class TSU("tsu")),
      m_lmem(new npu_sc::LocalMemory("local_memory")),
      m_gmem(new GlobalMemory("global_memory")),
      m_gmem_wrapper(new GlobalMemoryWrapper(*m_gmem)),
      m_sim_time(sc_core::SC_ZERO_TIME)
{
    m_tlu->initiator_gmem.bind(m_gmem->target_socket);
    m_tlu->initiator_lmem.bind(m_lmem->target_socket);
    m_tsu->initiator_gmem.bind(m_gmem->target_socket);
    m_tsu->initiator_lmem.bind(m_lmem->target_socket);
    cfg_socket.bind(m_tlu->target_cfg);
    cfg_socket.bind(m_tsu->target_cfg);
    setupSimulation();
}

TSUTLUTestBench::~TSUTLUTestBench() = default;

void TSUTLUTestBench::initialize()
{
    reset();
}

void TSUTLUTestBench::reset()
{
    // Reset all components
    m_lmem->reset();
    m_gmem->reset();
    m_sim_time = sc_core::SC_ZERO_TIME;
}

void TSUTLUTestBench::configureOperation(const hardware_instruction::TensorTransferConfig& config,
                                         bool is_tlu)
{
    // Configure TLU/TSU through configuration interface
    std::vector<IssueQueueCmd> cmd_queue;
    TLU_TSUConfigManager cfg_manager;
    cfg_manager.setConfig(config);
    IssueQueueCmd cfg_cmd;
    // 配置10个寄存器
    for (int i = 0; i < 10; i++)
    {
        cfg_cmd.funct7 = is_tlu ? instruction::opcode::TLD_CFG : instruction::opcode::TST_CFG;
        cfg_cmd.rs1val = i;
        cfg_cmd.rs2val = cfg_manager.readReg(i);
        cmd_queue.push_back(cfg_cmd);
    }
    // 发送配置指令
    for (const auto& cmd : cmd_queue)
    {
        sendCommand(cmd, is_tlu);
    }
}

void TSUTLUTestBench::sendCommand(const instruction::IssueQueueCmd& cmd, bool is_tlu)
{
    // Send command through configuration socket
    tlm::tlm_generic_payload trans;
    sc_core::sc_time delay = sc_core::SC_ZERO_TIME;

    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_data_ptr(reinterpret_cast<uint8_t*>(const_cast<IssueQueueCmd*>(&cmd)));
    trans.set_data_length(sizeof(IssueQueueCmd));
    trans.set_streaming_width(sizeof(IssueQueueCmd));
    trans.set_byte_enable_ptr(0);
    trans.set_dmi_allowed(false);
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

    cfg_socket[is_tlu ? 0 : 1]->b_transport(trans, delay);
    if (trans.is_response_error())
    {
        SC_REPORT_ERROR("TSUTLUTestBench", "Command send failed");
    }
    advanceSimulation(delay);
}

bool TSUTLUTestBench::writeGMEM(uint64_t word_offset, const Word256b& data)
{
    return m_gmem_wrapper->write_word(word_offset, data);
}

bool TSUTLUTestBench::readGMEM(uint64_t word_offset, Word256b& data)
{
    return m_gmem_wrapper->read_word(word_offset, data);
}

bool TSUTLUTestBench::writeLMEM(uint32_t unit_index, uint64_t word_offset, const Word256b& data)
{
    return m_lmem->write_word(unit_index, word_offset, data);
}

bool TSUTLUTestBench::readLMEM(uint32_t unit_index, uint64_t word_offset, Word256b& data)
{
    return m_lmem->read_word(unit_index, word_offset, data);
}

Word256b TSUTLUTestBench::generateTestData(PatternType type, uint32_t seed)
{
    static std::mt19937 rng(std::chrono::system_clock::now().time_since_epoch().count());
    Word256b data;

    switch (type)
    {
        case PatternType::SEQUENTIAL:
            for (size_t i = 0; i < data.size(); ++i)
            {
                data[i] = static_cast<uint8_t>((seed + i) & 0xFF);
            }
            break;

        case PatternType::RANDOM:
            if (seed != 0)
                rng.seed(seed);
            for (size_t i = 0; i < data.size(); ++i)
            {
                data[i] = static_cast<uint8_t>(rng() & 0xFF);
            }
            break;

        case PatternType::CORNER_CASE:
            // Generate corner case pattern (e.g., all 0s, all 1s, alternating)
            data.fill(seed & 1 ? 0xFF : 0x00);
            break;
    }

    return data;
}

bool TSUTLUTestBench::verifyTransfer(const TensorTransferTestCase& test_case)
{
    return test_case.is_tlu ? verifyTLUTransfer(test_case) : verifyTSUTransfer(test_case);
}

void TSUTLUTestBench::setupSimulation()
{
    // Any additional simulation setup
    m_sim_time = sc_core::SC_ZERO_TIME;
}

void TSUTLUTestBench::advanceSimulation(const sc_core::sc_time& time)
{
    m_sim_time += time;
    wait(time);
}

std::pair<size_t, uint64_t> TSUTLUTestBench::decodeAddress(uint64_t byte_addr)
{
    size_t unit_index;
    uint64_t word_offset;

    if (!npu_sc::decode(byte_addr, unit_index, word_offset))
    {
        return std::make_pair(-1, 0);
    }
    return std::make_pair(unit_index, word_offset);
}

bool TSUTLUTestBench::verifyTLUTransfer(const TensorTransferTestCase& test_case)
{
    const auto& config = test_case.config;
    bool success = true;

    for (uint32_t d2 = 0; d2 < config.cfg_size_dim2 && success; d2++)
    {
        for (uint32_t d0 = 0; d0 < config.cfg_size_dim0b && success; d0++)
        {
            // 计算掩码信息
            bool need_mask = (d0 == config.cfg_size_dim0b - 1 && config.cfg_rem_dim0 != 0);
            Word256b mask;
            if (need_mask)
            {
                mask = generate_mask(config.cfg_rem_dim0, config.cfg_wd);
            }

            for (uint32_t d1 = 0; d1 < config.cfg_size_dim1 && success; d1++)
            {
                uint64_t gmem_addr =
                    test_case.gmem_addr +
                    (d0 + d1 * config.cfg_stride_dim1_gmem + d2 * config.cfg_stride_dim2_gmem) * 32;
                uint64_t lmem_addr =
                    test_case.lmem_addr +
                    (d0 + d1 * config.cfg_stride_dim1_lmem + d2 * config.cfg_stride_dim2_lmem) * 32;

                // auto [src_unit, src_offset] = decodeAddress(src_addr);
                std::pair<size_t, uint64_t> lmem_address_result = decodeAddress(lmem_addr);
                size_t lmem_unit = lmem_address_result.first;
                uint64_t lmem_offset = lmem_address_result.second;

                Word256b src_data, dst_data, expected_data;
                if (!readGMEM(gmem_addr / 32, src_data) ||
                    !readLMEM(lmem_unit, lmem_offset, dst_data))
                {
                    success = false;
                    break;
                }

                // 应用掩码
                expected_data = src_data;
                if (need_mask)
                {
                    for (size_t i = 0; i < expected_data.size(); i++)
                    {
                        expected_data[i] &= mask[i];
                    }
                }

                // 比较数据
                if (memcmp(dst_data.data(), expected_data.data(), dst_data.size()) != 0)
                {
                    success = false;
                    break;
                }
            }
        }
    }

    return success;
}

bool TSUTLUTestBench::verifyTSUTransfer(const TensorTransferTestCase& test_case)
{
    const auto& config = test_case.config;
    bool success = true;

    for (uint32_t d2 = 0; d2 < config.cfg_size_dim2 && success; d2++)
    {
        for (uint32_t d0 = 0; d0 < config.cfg_size_dim0b && success; d0++)
        {
            // 计算掩码信息
            bool need_mask = (d0 == config.cfg_size_dim0b - 1 && config.cfg_rem_dim0 != 0);
            Word256b mask;
            if (need_mask)
            {
                mask = generate_mask(config.cfg_rem_dim0, config.cfg_wd);
            }

            for (uint32_t d1 = 0; d1 < config.cfg_size_dim1 && success; d1++)
            {
                uint64_t lmem_addr =
                    test_case.lmem_addr +
                    (d0 + d1 * config.cfg_stride_dim1_lmem + d2 * config.cfg_stride_dim2_lmem) * 32;
                uint64_t gmem_addr =
                    test_case.gmem_addr +
                    (d0 + d1 * config.cfg_stride_dim1_gmem + d2 * config.cfg_stride_dim2_gmem) * 32;

                std::pair<size_t, uint64_t> lmem_address_result = decodeAddress(lmem_addr);
                size_t lmem_unit = lmem_address_result.first;
                uint64_t lmem_offset = lmem_address_result.second;
                // auto [dst_unit, dst_offset] = decodeAddress(dst_addr);

                Word256b src_data, dst_data, expected_data;
                if (!readLMEM(lmem_unit, lmem_offset, src_data) ||
                    !readGMEM(gmem_addr / 32, dst_data))
                {
                    success = false;
                    break;
                }

                // 应用掩码
                expected_data = src_data;
                if (need_mask)
                {
                    for (size_t i = 0; i < expected_data.size(); i++)
                    {
                        expected_data[i] &= mask[i];
                    }
                }

                // 比较数据
                if (memcmp(dst_data.data(), expected_data.data(), dst_data.size()) != 0)
                {
                    success = false;
                    break;
                }
            }
        }
    }

    return success;
}

}  // namespace test