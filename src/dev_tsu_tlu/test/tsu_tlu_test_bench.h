#pragma once

// Include only what we need for declarations
#include <tlm_utils/multi_passthrough_initiator_socket.h>
#include <memory>
#include <systemc>
#include <tlm>
#include <utility>
#include "../../local_mem/local_mem.h"
#include "../global_memory.h"
#include "../tlu.h"
#include "../tsu.h"
#include "tsu_tlu_test_cases.h"

namespace npu_sc
{
class LocalMemory;
using Word256b = std::array<uint8_t, 32>;  // Assuming 32 bytes (256 bits)
}  // namespace npu_sc

using TLU_TSUConfigManager = hardware_instruction::TldConfigManager;

namespace test
{

using Word256b = npu_sc::Word256b;

class TSUTLUTestBench : public sc_core::sc_module
{
  public:
    tlm_utils::multi_passthrough_initiator_socket<TSUTLUTestBench> cfg_socket;

    SC_HAS_PROCESS(TSUTLUTestBench);
    TSUTLUTestBench(sc_core::sc_module_name name);
    ~TSUTLUTestBench();

    // Initialization and reset
    void initialize();
    void reset();

    // Configuration and command interface
    void configureOperation(const hardware_instruction::TensorTransferConfig& config, bool is_tlu);
    void sendCommand(const instruction::IssueQueueCmd& cmd, bool is_tlu);

    // Memory access interface
    bool writeGMEM(uint64_t word_offset, const Word256b& data);
    bool readGMEM(uint64_t word_offset, Word256b& data);
    bool writeLMEM(uint32_t unit_index, uint64_t word_offset, const Word256b& data);
    bool readLMEM(uint32_t unit_index, uint64_t word_offset, Word256b& data);

    // Test data generation and verification
    Word256b generateTestData(PatternType type, uint32_t seed = 0);
    bool verifyTransfer(const TensorTransferTestCase& test_case);

    // Helper functions for verification
    std::pair<size_t, uint64_t> decodeAddress(uint64_t byte_addr);

  private:
    std::unique_ptr<class TLU> m_tlu;
    std::unique_ptr<class TSU> m_tsu;
    std::unique_ptr<npu_sc::LocalMemory> m_lmem;
    std::unique_ptr<GlobalMemory> m_gmem;
    std::unique_ptr<GlobalMemoryWrapper> m_gmem_wrapper;
    sc_core::sc_time m_sim_time;

    void connectModules();
    void setupSimulation();
    void advanceSimulation(const sc_core::sc_time& time);

    bool verifyTLUTransfer(const TensorTransferTestCase& test_case);
    bool verifyTSUTransfer(const TensorTransferTestCase& test_case);
};

}  // namespace test