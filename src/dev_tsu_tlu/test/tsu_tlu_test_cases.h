#pragma once

#include <memory>
#include <string>
#include <systemc>
#include <tlm>
#include <utility>
#include "global_memory.h"
#include "local_mem.h"
#include "npu_config.h"
#include "utils/register.h"

namespace test
{

using Word256b = npu_sc::Word256b;

// 数据模式类型
enum class PatternType
{
    SEQUENTIAL,   // 顺序模式
    RANDOM,       // 随机模式
    CORNER_CASE,  // 边界情况
};

// 验证模式
enum class VerificationMode
{
    EXACT_MATCH,    // 精确匹配
    PATTERN_MATCH,  // 模式匹配
    TOLERANCE,      // 容差范围内
};

// Test case structure
struct TensorTransferTestCase
{
    std::string name;
    std::string description;
    bool is_tlu;                                        // true for TLU, false for TSU
    hardware_instruction::TensorTransferConfig config;  // Using actual hardware config
    uint64_t gmem_addr;
    uint64_t lmem_addr;
    PatternType pattern_type;      // Data pattern type
    uint32_t seed;                 // For random patterns
    VerificationMode verify_mode;  // Verification method
};

// Global Memory Wrapper
class GlobalMemoryWrapper
{
  public:
    explicit GlobalMemoryWrapper(GlobalMemory& gmem) : m_gmem(gmem)
    {
    }

    bool read_word(uint64_t word_offset, Word256b& data)
    {
        tlm::tlm_generic_payload trans;
        sc_core::sc_time delay = sc_core::SC_ZERO_TIME;

        trans.set_command(tlm::TLM_READ_COMMAND);
        trans.set_address(word_offset * 32);  // 32-byte aligned
        trans.set_data_ptr(reinterpret_cast<unsigned char*>(data.data()));
        trans.set_data_length(32);

        m_gmem.b_transport(0, trans, delay);
        return trans.get_response_status() == tlm::TLM_OK_RESPONSE;
    }

    bool write_word(uint64_t word_offset, const Word256b& data)
    {
        tlm::tlm_generic_payload trans;
        sc_core::sc_time delay = sc_core::SC_ZERO_TIME;

        trans.set_command(tlm::TLM_WRITE_COMMAND);
        trans.set_address(word_offset * 32);
        trans.set_data_ptr(const_cast<unsigned char*>(data.data()));
        trans.set_data_length(32);

        m_gmem.b_transport(0, trans, delay);
        return trans.get_response_status() == tlm::TLM_OK_RESPONSE;
    }

  private:
    GlobalMemory& m_gmem;
};

}  // namespace test