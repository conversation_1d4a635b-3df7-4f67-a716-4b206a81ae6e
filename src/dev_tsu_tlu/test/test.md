# DEV_TSU_TLU Test Framework Design

## 1. Overview

The DEV_TSU_TLU test framework provides a comprehensive testing environment for verifying the functionality of both TLU (Transfer Load Unit) and TSU (Transfer Store Unit) modules. The framework follows a modular design pattern similar to the TMU test framework.

```mermaid
graph TD
    A[AutoTestDriver] --> B[TestGenerator]
    A --> C[TestBench]
    A --> D[TestExecutor]
    B --> E[TestCase Generation]
    C --> F[Memory Interface]
    C --> G[TLU/TSU Interface]
    D --> H[Test Execution]
    D --> I[Result Verification]
```

## 2. Core Components

### 2.1 Test Case Structure
```cpp
// Common configuration for both TLU and TSU
struct TensorTransferTestCase {
    std::string name;
    std::string description;
    bool is_tlu;                  // true for TLU, false for TSU
    TensorTransferConfig config;  // Using actual hardware config
    uint64_t src_addr;           // Source address (GMEM for TLU, LMEM for TSU)
    uint64_t dst_addr;           // Destination address (LMEM for TLU, GMEM for TSU)
    PatternType pattern_type;    // Data pattern type
    uint32_t seed;               // For random patterns
    VerificationMode verify_mode;// Verification method
};
```

### 2.2 Test Bench
```cpp
class DevTsuTluTestBench : public sc_core::sc_module {
public:
    // TLM sockets
    tlm_utils::simple_initiator_socket<DevTsuTluTestBench> cfg_socket;
    
    // Memory interfaces
    std::unique_ptr<npu_sc::LocalMemory> m_lmem;
    std::unique_ptr<GlobalMemory> m_gmem;
    
    // Device under test
    std::unique_ptr<TLU> m_tlu;
    std::unique_ptr<TSU> m_tsu;
    
    // Core functionality
    void configureDevice(const TensorTransferConfig& config, bool is_tlu);
    void sendCommand(const IssueQueueCmd& cmd);
    bool writeMemory(bool is_gmem, uint64_t addr, const Word256b& data);
    bool readMemory(bool is_gmem, uint64_t addr, Word256b& data);
};
```

### 2.3 Test Generator
```cpp
class DevTsuTluTestGenerator {
public:
    // Test case generation
    TensorTransferTestCase generateBasicTest(bool is_tlu);
    TensorTransferTestCase generateMaskedTest(bool is_tlu);
    TensorTransferTestCase generateStridedTest(bool is_tlu);
    TensorTransferTestCase generateCornerCaseTest(bool is_tlu);
    
    // Test suite generation
    std::vector<TensorTransferTestCase> generateTestSuite();
    std::vector<TensorTransferTestCase> generateCornerCaseTestSuite();
};
```

### 2.4 Test Executor
```cpp
class DevTsuTluTestExecutor {
public:
    bool executeTest(const TensorTransferTestCase& test);
    bool executeTestSuite(const std::vector<TensorTransferTestCase>& suite);
    
protected:
    bool setupTest(const TensorTransferTestCase& test);
    bool verifyResult(const TensorTransferTestCase& test);
};
```

### 2.5 Auto Test Driver
```cpp
class DevTsuTluAutoTestDriver : public sc_core::sc_module {
public:
    SC_HAS_PROCESS(DevTsuTluAutoTestDriver);
    
    // Test execution methods
    TestResult runBasicTests();
    TestResult runComprehensiveTests();
    TestResult runCornerCaseTests();
    TestResult runAllTests();
};
```

## 3. Test Flow

### 3.1 Basic Test Flow
1. Test case generation
2. Device configuration
3. Memory initialization
4. Command execution
5. Result verification

```mermaid
sequenceDiagram
    participant Driver as AutoTestDriver
    participant Generator as TestGenerator
    participant Bench as TestBench
    participant DUT as TLU/TSU
    participant Memory as Memory

    Driver->>Generator: Generate test case
    Generator-->>Driver: Test case
    Driver->>Bench: Configure device
    Bench->>DUT: Send configuration
    Driver->>Bench: Initialize memory
    Bench->>Memory: Write test data
    Driver->>Bench: Send command
    Bench->>DUT: Execute command
    DUT->>Memory: Perform transfer
    Driver->>Bench: Verify result
    Bench->>Memory: Read result
    Memory-->>Bench: Result data
    Bench-->>Driver: Verification result
```

## 4. Memory Interface

### 4.1 Memory Wrapper
```cpp
class MemoryWrapper {
public:
    // Common interface for both GMEM and LMEM
    bool write(uint64_t addr, const Word256b& data);
    bool read(uint64_t addr, Word256b& data);
    void reset();
    
protected:
    // TLM transaction helper methods
    bool sendTransaction(tlm::tlm_generic_payload& trans, 
                        sc_core::sc_time& delay);
};
```

## 5. Test Types

### 5.1 Basic Tests
- Simple transfers with aligned addresses
- Basic stride patterns
- Single dimension transfers

### 5.2 Comprehensive Tests
- Multi-dimensional transfers
- Various data types
- Different stride configurations
- Masked transfers

### 5.3 Corner Cases
- Boundary conditions
- Maximum dimensions
- Minimum dimensions
- Edge case strides
- Partial word transfers

## 6. Verification Methods

### 6.1 Data Verification
- Exact match comparison
- Pattern verification
- Masked data verification
- Stride pattern verification

### 6.2 Error Checking
- Configuration validation
- Address alignment checking
- Dimension boundary checking
- Transfer completion verification

## 7. Implementation Guidelines

### 7.1 Memory Access
- Use TLM sockets for memory access
- Implement proper address decoding
- Handle alignment requirements
- Support masked transfers

### 7.2 Test Case Generation
- Generate valid configurations
- Consider hardware limitations
- Include edge cases
- Support different data types

### 7.3 Result Verification
- Compare transferred data
- Verify untouched memory regions
- Check alignment requirements
- Validate stride patterns

## 8. Usage Example

```cpp
int sc_main(int argc, char* argv[]) {
    // Create test driver
    DevTsuTluAutoTestDriver driver("test_driver");
    
    // Run tests
    TestResult result = driver.runAllTests();
    
    // Report results
    if (result.success) {
        std::cout << "All tests passed\n";
    } else {
        std::cout << "Tests failed: " << result.error_msg << "\n";
    }
    
    return result.success ? 0 : 1;
}
``` 