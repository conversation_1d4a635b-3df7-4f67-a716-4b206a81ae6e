#include "tsu_tlu_test_generator.h"
#include <algorithm>
#include <chrono>
#include <sstream>

namespace test
{

TSUTLUTestGenerator::TSUTLUTestGenerator(const TestConfigParams& params)
    : m_params(params), m_random_engine(std::chrono::system_clock::now().time_since_epoch().count())
{
}

TensorTransferTestCase TSUTLUTestGenerator::generateBasicTest(bool is_tlu)
{
    TensorTransferTestCase test_case;
    test_case.name = is_tlu ? "Basic TLU Transfer" : "Basic TSU Transfer";
    test_case.description = "Basic transfer test with simple dimensions";
    test_case.is_tlu = is_tlu;
    test_case.config = generateConfig(is_tlu);
    test_case.pattern_type = PatternType::SEQUENTIAL;
    test_case.verify_mode = VerificationMode::EXACT_MATCH;

    // Generate aligned addresses
    test_case.gmem_addr = generateAddress();
    test_case.lmem_addr = generateAddress() + test_case.gmem_addr;

    return test_case;
}

TensorTransferTestCase TSUTLUTestGenerator::generateMaskedTest(bool is_tlu)
{
    TensorTransferTestCase test_case = generateBasicTest(is_tlu);
    test_case.name = is_tlu ? "Masked TLU Transfer" : "Masked TSU Transfer";
    test_case.description = "Transfer test with remainder in dimension 0";

    // Set remainder for dimension 0
    test_case.config.cfg_rem_dim0 = generateDimension(1, test_case.config.cfg_size_dim0b - 1);

    return test_case;
}

TensorTransferTestCase TSUTLUTestGenerator::generateDimensionTest(bool is_tlu)
{
    TensorTransferTestCase test_case;
    test_case.name = is_tlu ? "Dimension TLU Transfer" : "Dimension TSU Transfer";
    test_case.description = "Transfer test with various dimensions";
    test_case.is_tlu = is_tlu;
    test_case.config = generateConfig(is_tlu);
    test_case.pattern_type = PatternType::SEQUENTIAL;
    test_case.verify_mode = VerificationMode::EXACT_MATCH;

    // Generate maximum dimensions
    test_case.config.cfg_size_dim0b = m_params.max_dim0;
    test_case.config.cfg_size_dim1 = m_params.max_dim1;
    test_case.config.cfg_size_dim2 = m_params.max_dim2;

    // Generate aligned addresses
    test_case.gmem_addr = generateAddress();
    test_case.lmem_addr = generateAddress() + test_case.gmem_addr;

    return test_case;
}

std::vector<TensorTransferTestCase> TSUTLUTestGenerator::generateTestSuite()
{
    std::vector<TensorTransferTestCase> test_suite;

    // Generate TLU tests
    test_suite.push_back(generateBasicTest(true));
    test_suite.push_back(generateMaskedTest(true));
    test_suite.push_back(generateDimensionTest(true));

    // Generate TSU tests
    test_suite.push_back(generateBasicTest(false));
    test_suite.push_back(generateMaskedTest(false));
    test_suite.push_back(generateDimensionTest(false));

    return test_suite;
}

std::vector<TensorTransferTestCase> TSUTLUTestGenerator::generateCornerCaseTestSuite()
{
    std::vector<TensorTransferTestCase> test_suite;

    // Generate minimum dimension tests
    auto min_dim_test_tlu = generateBasicTest(true);
    min_dim_test_tlu.name = "TLU Minimum Dimensions";
    min_dim_test_tlu.config.cfg_size_dim0b = m_params.min_dim0;
    min_dim_test_tlu.config.cfg_size_dim1 = m_params.min_dim1;
    min_dim_test_tlu.config.cfg_size_dim2 = m_params.min_dim2;
    test_suite.push_back(min_dim_test_tlu);

    auto min_dim_test_tsu = generateBasicTest(false);
    min_dim_test_tsu.name = "TSU Minimum Dimensions";
    min_dim_test_tsu.config.cfg_size_dim0b = m_params.min_dim0;
    min_dim_test_tsu.config.cfg_size_dim1 = m_params.min_dim1;
    min_dim_test_tsu.config.cfg_size_dim2 = m_params.min_dim2;
    test_suite.push_back(min_dim_test_tsu);

    // Generate maximum remainder tests
    auto max_rem_test_tlu = generateMaskedTest(true);
    max_rem_test_tlu.name = "TLU Maximum Remainder";
    max_rem_test_tlu.config.cfg_rem_dim0 = 31;  // Maximum remainder
    test_suite.push_back(max_rem_test_tlu);

    auto max_rem_test_tsu = generateMaskedTest(false);
    max_rem_test_tsu.name = "TSU Maximum Remainder";
    max_rem_test_tsu.config.cfg_rem_dim0 = 31;  // Maximum remainder
    test_suite.push_back(max_rem_test_tsu);

    return test_suite;
}

hardware_instruction::TensorTransferConfig TSUTLUTestGenerator::generateConfig(bool is_tlu)
{
    hardware_instruction::TensorTransferConfig config;

    // Generate dimensions
    config.cfg_size_dim0b = generateDimension(m_params.min_dim0, m_params.max_dim0);
    config.cfg_size_dim1 = generateDimension(m_params.min_dim1, m_params.max_dim1);
    config.cfg_size_dim2 = generateDimension(m_params.min_dim2, m_params.max_dim2);

    // Generate strides
    config.cfg_stride_dim1_gmem = config.cfg_size_dim0b;
    config.cfg_stride_dim2_gmem = config.cfg_size_dim1 * config.cfg_stride_dim1_gmem;
    config.cfg_stride_dim1_lmem = config.cfg_size_dim0b;
    config.cfg_stride_dim2_lmem = config.cfg_size_dim1 * config.cfg_stride_dim1_lmem;

    // Set data type (for now, using a fixed type)
    config.cfg_type = 0;  // INT type
    config.cfg_wd = 0;    // 8-bit width

    // Initialize remainder to 0
    config.cfg_rem_dim0 = 0;

    return config;
}

uint32_t TSUTLUTestGenerator::generateDimension(uint32_t min_dim, uint32_t max_dim)
{
    std::uniform_int_distribution<uint32_t> dist(min_dim, max_dim);
    return dist(m_random_engine);
}

uint32_t TSUTLUTestGenerator::generateStride(uint32_t dim_size, uint32_t min_stride)
{
    return std::max(dim_size, min_stride);
}

uint64_t TSUTLUTestGenerator::generateAddress(uint32_t alignment)
{
    std::uniform_int_distribution<uint64_t> dist(0, 0x10000);  // 16MB range
    return (dist(m_random_engine) / alignment) * alignment;
}

uint8_t TSUTLUTestGenerator::generateDataType()
{
    // For now, return a fixed data type (INT8)
    return 0;
}

bool TSUTLUTestGenerator::validateConfig(const hardware_instruction::TensorTransferConfig& config)
{
    return checkDimensionConstraints(config) && checkStrideConstraints(config) &&
           checkDataTypeConstraints(config);
}

bool TSUTLUTestGenerator::checkDimensionConstraints(
    const hardware_instruction::TensorTransferConfig& config)
{
    return config.cfg_size_dim0b >= m_params.min_dim0 &&
           config.cfg_size_dim0b <= m_params.max_dim0 &&
           config.cfg_size_dim1 >= m_params.min_dim1 && config.cfg_size_dim1 <= m_params.max_dim1 &&
           config.cfg_size_dim2 >= m_params.min_dim2 && config.cfg_size_dim2 <= m_params.max_dim2;
}

bool TSUTLUTestGenerator::checkStrideConstraints(
    const hardware_instruction::TensorTransferConfig& config)
{
    return config.cfg_stride_dim1_gmem >= m_params.min_stride &&
           config.cfg_stride_dim2_gmem >= m_params.min_stride &&
           config.cfg_stride_dim1_lmem >= m_params.min_stride &&
           config.cfg_stride_dim2_lmem >= m_params.min_stride;
}

bool TSUTLUTestGenerator::checkDataTypeConstraints(
    const hardware_instruction::TensorTransferConfig& config)
{
    // Add data type validation if needed
    return true;
}

}  // namespace test