#include <cstdint>
#include <iomanip>
#include <iostream>
#include <systemc>
#include "tsu_tlu_test_bench.h"
#include "tsu_tlu_test_cases.h"
#include "tsu_tlu_test_generator.h"
#include "utils/sc_logger.h"
namespace test
{

class TSUTLUAutoTestDriver : public sc_core::sc_module
{
  public:
    SC_HAS_PROCESS(TSUTLUAutoTestDriver);
    TSUTLUAutoTestDriver(sc_core::sc_module_name name)
        : sc_core::sc_module(name), m_testbench("tsu_tlu_test_bench")
    {
        SC_THREAD(run);
    }

    bool success() const
    {
        return m_success;
    }
    const std::string& error_msg() const
    {
        return m_error_msg;
    }

  private:
    void run()
    {
        // 设置测试配置参数
        TestConfigParams params{
            1,  // min_dim0
            8,  // max_dim0
            1,  // min_dim1
            8,  // max_dim1
            1,  // min_dim2
            8,  // max_dim2
            8,  // min_stride
            32  // max_stride
        };

        // 创建测试生成器
        TSUTLUTestGenerator generator(params);

        SC_INFO("TEST_START", "\n=== Running TSU/TLU Tests ===\n");

        // 1. 运行基本功能测试
        SC_INFO("TEST_BASIC", "1. Running Basic Functionality Tests...");
        if (!runBasicTests(generator))
        {
            return;
        }

        // 2. 运行完整测试套件
        SC_INFO("TEST_SUITE", "\n2. Running Complete Test Suite...");
        if (!runTestSuite(generator))
        {
            return;
        }

        // 3. 运行边界条件测试
        SC_INFO("TEST_CORNER", "\n3. Running Corner Case Tests...");
        if (!runCornerCaseTests(generator))
        {
            return;
        }

        m_success = true;
        SC_INFO("TEST_COMPLETE", "\n=== All Tests Completed Successfully ===\n");
    }

    bool runBasicTests(TSUTLUTestGenerator& generator)
    {
        // Test TLU basic transfer
        auto tlu_test = generator.generateBasicTest(true);
        SC_INFO("TEST_TLU_BASIC", "  - Testing TLU basic transfer...");
        if (!executeTest(tlu_test))
        {
            m_error_msg = "TLU basic test failed";
            m_success = false;
            return false;
        }

        // Test TSU basic transfer
        auto tsu_test = generator.generateBasicTest(false);
        std::cout << "  - Testing TSU basic transfer..." << std::endl;
        if (!executeTest(tsu_test))
        {
            m_error_msg = "TSU basic test failed";
            m_success = false;
            return false;
        }

        return true;
    }

    bool runTestSuite(TSUTLUTestGenerator& generator)
    {
        auto test_suite = generator.generateTestSuite();
        std::cout << "  Generated " << test_suite.size() << " test cases." << std::endl;

        for (const auto& test : test_suite)
        {
            std::cout << "  - Running test: " << test.name << std::endl;
            if (!executeTest(test))
            {
                m_error_msg = "Test suite failed: " + test.name;
                m_success = false;
                return false;
            }
        }

        return true;
    }

    bool runCornerCaseTests(TSUTLUTestGenerator& generator)
    {
        auto corner_case_suite = generator.generateCornerCaseTestSuite();

        for (const auto& test : corner_case_suite)
        {
            std::cout << "  - Running corner case test: " << test.name << std::endl;
            if (!executeTest(test))
            {
                m_error_msg = "Corner case test failed: " + test.name;
                m_success = false;
                return false;
            }
        }

        return true;
    }

    bool executeTest(const TensorTransferTestCase& test_case)
    {
        // Reset testbench
        m_testbench.reset();

        // Configure the operation
        m_testbench.configureOperation(test_case.config, test_case.is_tlu);

        // Generate and write source data
        Word256b test_data = m_testbench.generateTestData(test_case.pattern_type, test_case.seed);

        if (test_case.is_tlu)
        {
            SC_DEBUG("TEST_GMEM_WRITE", "TSUTLUAutoTestDriver Write GMEM");
            // TLU: Write to GMEM
            for (uint32_t dim2 = 0; dim2 < test_case.config.cfg_size_dim2; dim2++)
            {
                for (uint32_t dim0 = 0; dim0 < test_case.config.cfg_size_dim0b; dim0++)
                {
                    for (uint32_t dim1 = 0; dim1 < test_case.config.cfg_size_dim1; dim1++)
                    {
                        uint64_t addr = test_case.gmem_addr +
                                        (dim2 * test_case.config.cfg_stride_dim2_gmem +
                                         dim1 * test_case.config.cfg_stride_dim1_gmem + dim0) *
                                            32;
                        if (!m_testbench.writeGMEM(addr / 32, test_data))
                        {
                            SC_ERROR("TEST_GMEM_ERROR", "TSUTLUAutoTestDriver Write GMEM failed at addr: %lx", addr);
                            return false;
                        }
                    }
                }
            }
        }
        else
        {
            SC_DEBUG("TEST_LMEM_WRITE", "TSUTLUAutoTestDriver Write LMEM");
            // TSU: Write to LMEM
            for (uint32_t dim2 = 0; dim2 < test_case.config.cfg_size_dim2; dim2++)
            {
                for (uint32_t dim0 = 0; dim0 < test_case.config.cfg_size_dim0b; dim0++)
                {
                    for (uint32_t dim1 = 0; dim1 < test_case.config.cfg_size_dim1; dim1++)
                    {
                        uint64_t addr = test_case.lmem_addr +
                                        (dim2 * test_case.config.cfg_stride_dim2_lmem +
                                         dim1 * test_case.config.cfg_stride_dim1_lmem + dim0) *
                                            32;
                        std::pair<size_t, uint64_t> address_result = m_testbench.decodeAddress(addr);
                        size_t unit_index = address_result.first;
                        uint64_t offset = address_result.second;
                        if (!m_testbench.writeLMEM(unit_index, offset, test_data))
                        {
                            return false;
                        }
                    }
                }
            }
        }

        // Create and send command
        instruction::IssueQueueCmd cmd;
        cmd.funct7 = test_case.is_tlu ? instruction::opcode::TLD_DRV : instruction::opcode::TST_DRV;
        cmd.rs1val = test_case.lmem_addr;
        cmd.rs2val = test_case.gmem_addr;
        m_testbench.sendCommand(cmd, test_case.is_tlu);

        // Verify transfer
        return m_testbench.verifyTransfer(test_case);
    }

    TSUTLUTestBench m_testbench;
    bool m_success{false};
    std::string m_error_msg;
};

}  // namespace test

// 主函数
int sc_main(int argc, char* argv[])
{
    // 设置SystemC仿真参数
    sc_core::sc_report_handler::set_actions(sc_core::SC_WARNING, sc_core::SC_DO_NOTHING);
    g_logger.setLogLevel(SystemCLogger::SC_DEBUG);
    // 创建测试驱动
    test::TSUTLUAutoTestDriver test_driver("auto_test_driver");

    // 启动仿真
    sc_core::sc_start(1000, sc_core::SC_NS);

    // 检查测试结果
    if (!test_driver.success())
    {
        std::cerr << "Tests failed: " << test_driver.error_msg() << std::endl;
        return 1;
    }

    std::cout << "All tests passed successfully!" << std::endl;
    sc_core::sc_stop();
    return 0;
}