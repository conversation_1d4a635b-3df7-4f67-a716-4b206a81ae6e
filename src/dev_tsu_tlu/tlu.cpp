#include "tlu.h"
#include "utils/sc_logger.h"
#include "utils/utils.h"
using namespace hardware_instruction;
using namespace instruction;
TLU::TLU(sc_core::sc_module_name name)
    : sc_module(name),
      target_cfg("target_cfg"),
      initiator_gmem("initiator_gmem"),
      initiator_lmem("initiator_lmem"),
      req_lmem_delay(sc_core::SC_ZERO_TIME),
      req_gmem_delay(sc_core::SC_ZERO_TIME)
{
    target_cfg.register_b_transport(this, &TLU::b_transport);
    SC_THREAD(thread_cmd);
}

void TLU::b_transport(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
{
    m_issue_queue_cmd = *(IssueQueueCmd*)trans.get_data_ptr();
    trans.set_response_status(tlm::TLM_OK_RESPONSE);
    SC_DEBUG("TLU_TRANSPORT", "TLU Received command, func7=0x%x, rs1val=0x%x, rs2val=0x%x",
             m_issue_queue_cmd.funct7,
             m_issue_queue_cmd.rs1val,
             m_issue_queue_cmd.rs2val);
    recv_event.notify();
}

void TLU::thread_cmd()
{
    while (true)
    {
        wait(recv_event);
        if (m_issue_queue_cmd.funct7 == opcode::TLD_CFG)
        {
            config_manager.writeReg(m_issue_queue_cmd.rs1val, m_issue_queue_cmd.rs2val);
        }
        else if (m_issue_queue_cmd.funct7 == opcode::TLD_DRV)
        {
            byte_base_lmem = m_issue_queue_cmd.rs1val;
            byte_base_gmem = m_issue_queue_cmd.rs2val;
            m_cfg = config_manager.getConfig();
            m_cfg.print();
            m_prec = static_cast<uint8_t>(m_cfg.cfg_type << 3 | m_cfg.cfg_wd);
            process_transfer();
        }
    }
}

void TLU::calculate_strides(uint32_t& stride_dim0b_bytes,
                            uint32_t& stride_dim2_bytes_gmem,
                            uint32_t& stride_dim1_bytes_gmem,
                            uint32_t& stride_dim2_bytes_lmem,
                            uint32_t& stride_dim1_bytes_lmem)
{
    stride_dim0b_bytes = NPUConfig::LMEM_WD / 8;  // 256 bits = 32 bytes
    stride_dim2_bytes_gmem = m_cfg.cfg_stride_dim2_gmem * stride_dim0b_bytes;
    stride_dim1_bytes_gmem = m_cfg.cfg_stride_dim1_gmem * stride_dim0b_bytes;
    stride_dim2_bytes_lmem = m_cfg.cfg_stride_dim2_lmem * stride_dim0b_bytes;
    stride_dim1_bytes_lmem = m_cfg.cfg_stride_dim1_lmem * stride_dim0b_bytes;
}

void TLU::process_transfer()
{
    uint32_t stride_dim0b_bytes, stride_dim2_bytes_gmem, stride_dim1_bytes_gmem,
        stride_dim2_bytes_lmem, stride_dim1_bytes_lmem;
    calculate_strides(stride_dim0b_bytes,
                      stride_dim2_bytes_gmem,
                      stride_dim1_bytes_gmem,
                      stride_dim2_bytes_lmem,
                      stride_dim1_bytes_lmem);

    for (uint32_t idx_dim2 = 0; idx_dim2 < m_cfg.cfg_size_dim2; idx_dim2++)
    {
        for (uint32_t idx_dim1 = 0; idx_dim1 < m_cfg.cfg_size_dim1; idx_dim1++)
        {
            for (uint32_t idx_dim0b = 0; idx_dim0b < m_cfg.cfg_size_dim0b; idx_dim0b++)
            {
                MaskInfo mask_info = calculate_mask_info(
                    idx_dim0b, m_cfg.cfg_size_dim0b, m_cfg.cfg_rem_dim0, m_prec);

                // Read from GMEM
                uint64_t gmem_addr = byte_base_gmem + stride_dim2_bytes_gmem * idx_dim2 +
                                     stride_dim1_bytes_gmem * idx_dim1 +
                                     stride_dim0b_bytes * idx_dim0b;
                auto data_out = read_from_gmem(gmem_addr, mask_info.mask, mask_info.need_mask);
                if (data_out.data_valid)
                {
                    // Write to LMEM
                    uint64_t lmem_addr = byte_base_lmem + stride_dim2_bytes_lmem * idx_dim2 +
                                         stride_dim1_bytes_lmem * idx_dim1 +
                                         stride_dim0b_bytes * idx_dim0b;
                    write_to_lmem(lmem_addr, data_out.data);
                }
                else
                {
                    SC_ERROR("TLU_GMEM_ERROR", "TLU Read from GMEM failed at addr: %lx", gmem_addr);
                }
            }
        }
    }
}

TLU::DataOut TLU::read_from_gmem(uint64_t rd_byte_addr, Word256b& mask_rd, bool need_mask)
{
    DataOut result = {false, {}};  // Initialize with invalid data

    // Read data through TLM
    Word256b read_data;
    if (!send_read_request(rd_byte_addr, read_data))
    {
        return result;  // Return invalid result if read fails
    }

    // Apply mask if needed
    if (need_mask)
    {
        for (size_t i = 0; i < read_data.size(); i++)
        {
            read_data[i] &= mask_rd[i];
        }
    }

    // Set output data
    result.data_valid = true;
    result.data = read_data;

    return result;
}

bool TLU::send_read_request(uint64_t addr, Word256b& data)
{
    // Create TLM payload
    trans.set_command(tlm::TLM_READ_COMMAND);
    trans.set_address(addr);
    trans.set_data_ptr(reinterpret_cast<uint8_t*>(data.data()));
    trans.set_data_length(sizeof(data));
    trans.set_streaming_width(sizeof(data));
    trans.set_byte_enable_ptr(nullptr);
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

    // Send transaction through socket
    initiator_gmem->b_transport(trans, req_gmem_delay);

    // Check response status
    return (trans.get_response_status() == tlm::TLM_OK_RESPONSE);
}

bool TLU::write_to_lmem(uint64_t wr_byte_addr, Word256b& data)
{
    // Create TLM payload
    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_address(wr_byte_addr);
    trans.set_data_ptr(reinterpret_cast<uint8_t*>(data.data()));
    trans.set_data_length(sizeof(data));  // 256 bits = 32 bytes
    trans.set_streaming_width(sizeof(data));
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

    // Send transaction through socket
    initiator_lmem->b_transport(trans, req_lmem_delay);

    // Return response status
    return (trans.get_response_status() == tlm::TLM_OK_RESPONSE);
}