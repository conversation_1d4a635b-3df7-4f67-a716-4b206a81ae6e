#pragma once

#include <cstdint>
#include "npu_config.h"

namespace vpu
{

// Helper class for VPU read request generation
class VPURdReqTx
{
  public:
    struct ReqInfo
    {
        uint64_t ch1_rd_req_addr;
        uint64_t ch2_rd_req_addr;
        bool ch2_used;
        bool mask_ch1_rd;
        bool mask_ch2_rd;
    };

    // Generate request info for current loop iteration
    static ReqInfo generate_req(uint32_t o_cnt_loop,
                                uint32_t byte_base_in1,
                                uint32_t byte_base_in2,
                                WidthMode width_mode,
                                FunctSel funcsel);
};

}  // namespace vpu