#pragma once

#include <array>
#include <systemc>
#include <tlm>
#include "npu_config.h"
#include "tlm_utils/simple_target_socket.h"
#include "utils/register.h"
#include "utils/utils.h"
#include "vpe/vpe.h"
#include "vpu_rd_ack_rx.h"
#include "vpu_rd_req_tx.h"
#include "vpu_wr_req_tx.h"
namespace vpu
{

class VPU : public sc_core::sc_module
{
  public:
    using Word256b = VPURdAckRx::Word256b;
    using IssueQueueCmd = instruction::IssueQueueCmd;
    using VPUConfigManager = hardware_instruction::VpConfigManager;
    using VPUConfig = hardware_instruction::VectorProcessConfig;
    tlm_utils::simple_target_socket<VPU> cfg_socket;

    SC_HAS_PROCESS(VPU);

    VPU(sc_core::sc_module_name name);
    uint32_t get_result_scalar() const;

    // VPU read response processing
    VPURdAckRx vpu_rd_ack_rx;
    // VPU write request processing
    VPUWrReqTx vpu_wr_req_tx;

  private:
    void b_transport_cfg(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    void thread_cfg();
    void thread_drv();

    // Helper functions for data processing
    Word256b process_vector_vector(const Word256b& data1, const Word256b& data2);
    Word256b process_vector_scalar(const Word256b& data1, uint32_t scalar_val);
    uint32_t process_vector_reduction(const Word256b& data1);
    Word256b process_vector(const Word256b& data1);
    // VPE instances
    std::array<Vpe, VPE_NUM> vpe_arr;

    // Configuration and state
    sc_core::sc_event rec_cfg_event;
    sc_core::sc_event drv_event;
    sc_core::sc_time m_delay;

    IssueQueueCmd m_issue_queue_cmd;
    VPUConfigManager m_cfg;
    VPUConfig m_cfg_vpu;
    VpeConfig m_cfg_vpe;

    // Data processing state
    uint64_t byte_base_out;
    uint64_t byte_base_in1;
    uint64_t byte_base_in2;
    uint64_t val_in2;

    FunctSel m_funcsel;
    WidthMode m_width_mode;
    OpMode m_op_mode;
    uint8_t m_width_in1;
    uint8_t m_width_in2;
    uint8_t m_width_out;
    uint8_t m_prec_in1;
    uint8_t m_prec_in2;
    uint8_t m_prec_out;
    uint8_t m_ref_width;
    uint32_t cfg_size_dim0b_wide;

    VPURdAckRx::DataOut m_data_out;

    // VPU result
    Word256b m_result;
    uint32_t m_result_scalar;
};

}  // namespace vpu