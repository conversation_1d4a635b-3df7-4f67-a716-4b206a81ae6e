#pragma once

#include <array>
#include <cstdint>
#include <systemc>
#include <tlm>
#include "npu_config.h"
#include "tlm_utils/simple_initiator_socket.h"
#include "utils/utils.h"

namespace vpu
{

// Helper class for VPU write request processing
class VPUWrReqTx : public sc_core::sc_module
{
  public:
    using Word256b = std::array<uint8_t, NPUConfig::LMEM_WD / 8>;

    SC_HAS_PROCESS(VPUWrReqTx);

    VPUWrReqTx(sc_core::sc_module_name name);

    bool process_write(uint64_t byte_addr_out,
                       uint32_t loop_cnt,
                       uint8_t width_out,
                       WidthMode width_mode,
                       uint32_t cfg_size_dim0b_wide,
                       const Word256b& indata);

    tlm_utils::simple_initiator_socket<VPUWrReqTx> init_socket;

  protected:
    // Move test-related functions to protected section
    Word256b remove_padding(const Word256b& input, uint8_t width);

  private:
    Word256b m_saved_lsb;  // For storing LSB in narrowing mode
    bool send_write_request(uint64_t addr, const Word256b& data);
    Word256b combine_data(const Word256b& msb, const Word256b& lsb);
};

}  // namespace vpu