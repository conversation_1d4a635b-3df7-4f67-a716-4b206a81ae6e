#pragma once

#include <array>
#include <cstdint>
#include <systemc>
#include <tlm>
#include "npu_config.h"
#include "tlm_utils/simple_initiator_socket.h"
#include "utils/utils.h"

namespace vpu
{

// Helper class for VPU read response processing
class VPURdAckRx : public sc_core::sc_module
{
  public:
    using Word256b = std::array<uint8_t, NPUConfig::LMEM_WD / 8>;

    struct DataOut
    {
        bool data_valid;
        Word256b ch1_data;
        Word256b ch2_data;
    };

    SC_HAS_PROCESS(VPURdAckRx);

    VPURdAckRx(sc_core::sc_module_name name);

    DataOut process_read(uint64_t ch1_addr,
                         uint64_t ch2_addr,
                         bool ch2_used,
                         bool mask_ch1_rd,
                         bool mask_ch2_rd,
                         uint32_t o_cnt_loop,
                         WidthMode width_mode,
                         uint8_t width_in1,
                         uint8_t width_in2);

    tlm_utils::simple_initiator_socket<VPURdAckRx> init_socket;

  protected:
    // Move test-related functions to protected section
    Word256b extend_data(const Word256b& input, uint8_t width);

  private:
    bool send_read_request(uint64_t addr, Word256b& data);
    void split_data(const Word256b& input, Word256b& msb, Word256b& lsb);

    Word256b m_saved_msb;  // Keep this as member since it needs to persist between calls
};

}  // namespace vpu