#include "vpu_wr_req_tx.h"
#include <cstring>
#include "npu_config.h"

using namespace dtype;
namespace vpu
{

VPUWrReqTx::VPUWrReqTx(sc_core::sc_module_name name)
    : sc_core::sc_module(name), init_socket("init_socket")
{
    m_saved_lsb.fill(0);
}

bool VPUWrReqTx::process_write(uint64_t byte_addr_out,
                               uint32_t loop_cnt,
                               uint8_t width_out,
                               WidthMode width_mode,
                               uint32_t cfg_size_dim0b_wide,
                               const Word256b& indata)
{
    Word256b write_data;
    write_data.fill(0);
    uint64_t write_addr = byte_addr_out + loop_cnt;

    if (width_mode != WidthMode::NARROWING)
    {
        // For SINGLE_WIDTH and WIDENING modes, write data directly
        return send_write_request(write_addr, indata);
    }

    // Handle NARROWING mode
    Word256b processed_data = remove_padding(indata, width_out);

    if (loop_cnt % 2 == 0)
    {
        if (loop_cnt == cfg_size_dim0b_wide - 1)
        {
            // Last data group: pad with zeros in upper half
            size_t half_size = NPUConfig::LMEM_WD / 16;  // Half size in bytes
            std::memcpy(write_data.data(), processed_data.data(), half_size);
            return send_write_request(write_addr, write_data);
        }
        else
        {
            // Save LSB for next write
            m_saved_lsb = processed_data;
            return true;  // No write in this cycle
        }
    }
    else
    {
        // Odd count: combine saved LSB with current MSB
        write_data = combine_data(processed_data, m_saved_lsb);
        return send_write_request(write_addr - 1, write_data);  // Adjust address for combined write
    }
}

bool VPUWrReqTx::send_write_request(uint64_t addr, const Word256b& data)
{
    tlm::tlm_generic_payload trans;
    sc_core::sc_time delay = sc_core::SC_ZERO_TIME;

    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_address(addr);
    trans.set_data_ptr(const_cast<uint8_t*>(data.data()));
    trans.set_data_length(NPUConfig::LMEM_WD / 8);
    trans.set_streaming_width(NPUConfig::LMEM_WD / 8);
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

    init_socket->b_transport(trans, delay);

    return trans.get_response_status() == tlm::TLM_OK_RESPONSE;
}

VPUWrReqTx::Word256b VPUWrReqTx::remove_padding(const Word256b& input, uint8_t width)
{
    Word256b result;
    result.fill(0);

    // Convert width code to actual bit width
    uint32_t bit_width = width_code_to_bits(static_cast<dtype::WidthCode>(width));
    uint32_t extended_width = bit_width * 2;                      // Width including padding
    uint32_t num_elements = NPUConfig::LMEM_WD / extended_width;  // Number of output elements

    switch (extended_width)
    {
        case 8:
        {  // 4-bit data with 4-bit padding
            for (uint32_t i = 0; i < num_elements; i++)
            {
                uint32_t element = input[i] & 0xF;  // Get low 4 bits
                uint32_t byte_pos = i / 2;          // Position in output
                if (i % 2 == 0)
                {
                    // Even elements go to low 4 bits
                    result[byte_pos] = element;
                }
                else
                {
                    // Odd elements go to high 4 bits
                    result[byte_pos] |= (element << 4);
                }
            }
            break;
        }
        case 16:
        {  // 8-bit data with 8-bit padding
            for (uint32_t i = 0; i < num_elements; i++)
            {
                result[i] = input[i * 2];  // Copy the data byte, ignore padding byte
            }
            break;
        }
        case 32:
        {  // 16-bit data with 16-bit padding
            for (uint32_t i = 0; i < num_elements; i++)
            {
                // Copy each 16-bit value, ignoring padding
                std::memcpy(&result[i * 2], &input[i * 4], 2);
            }
            break;
        }
    }

    return result;
}

VPUWrReqTx::Word256b VPUWrReqTx::combine_data(const Word256b& msb, const Word256b& lsb)
{
    Word256b result;
    size_t half_size = NPUConfig::LMEM_WD / 16;  // Half size in bytes

    // Copy LSB to first half
    std::memcpy(result.data(), lsb.data(), half_size);

    // Copy MSB to second half
    std::memcpy(result.data() + half_size, msb.data(), half_size);

    return result;
}

}  // namespace vpu
