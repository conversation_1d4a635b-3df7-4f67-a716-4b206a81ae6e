#include <gtest/gtest.h>
#include <array>
#include <cstring>
#include "../vpu_rd_ack_rx.h"
#include "../vpu_wr_req_tx.h"
#include "npu_config.h"

namespace vpu
{
namespace test
{

// Test fixture that makes private methods accessible for testing
class TestVPURdAckRx : protected VPURdAckRx
{
  public:
    TestVPURdAckRx() : VPURdAckRx("test_rd_ack_rx")
    {
    }

    using VPURdAckRx::extend_data;
    using VPURdAckRx::Word256b;
};

class TestVPUWrReqTx : protected VPUWrReqTx
{
  public:
    TestVPUWrReqTx() : VPUWrReqTx("test_wr_req_tx")
    {
    }

    using VPUWrReqTx::remove_padding;
    using VPUWrReqTx::Word256b;
};

class DataPaddingTest : public ::testing::Test
{
  protected:
    TestVPURdAckRx rd_ack_rx;
    TestVPUWrReqTx wr_req_tx;
};

// Test 4-bit width data
TEST_F(DataPaddingTest, FourBitWidth)
{
    TestVPURdAckRx::Word256b input;
    input.fill(0);

    // Set test pattern for INT4: each byte contains two 4-bit values
    // For example: byte 0 = [value1(4bits) | value2(4bits)]
    for (size_t i = 0; i < 16; i++)
    {                                        // Fill first 128 bits (16 bytes)
        uint8_t value1 = (i * 2) & 0xF;      // First 4-bit value
        uint8_t value2 = (i * 2 + 1) & 0xF;  // Second 4-bit value
        input[i] = (value2 << 4) | value1;   // Combine into one byte
    }

    // Test extend_data
    auto extended = rd_ack_rx.extend_data(input, static_cast<uint8_t>(dtype::WidthCode::W4));

    // Verify extended data - each 4-bit value should be in its own byte
    for (size_t i = 0; i < 16; i++)
    {
        uint8_t original_byte = input[i];
        uint8_t value1 = original_byte & 0xF;
        uint8_t value2 = (original_byte >> 4) & 0xF;

        // First 4-bit value and its padding
        EXPECT_EQ(extended[i * 2], value1) << "First value mismatch at byte " << i;
        EXPECT_EQ(extended[i * 2 + 1], value2) << "Second value mismatch at byte " << i;
    }

    // Test remove_padding
    auto removed = wr_req_tx.remove_padding(extended, static_cast<uint8_t>(dtype::WidthCode::W4));

    // Verify the original data is recovered
    for (size_t i = 0; i < 16; i++)
    {
        EXPECT_EQ(removed[i], input[i]) << "Data mismatch at byte " << i;
    }
}

// Test 8-bit width data
TEST_F(DataPaddingTest, EightBitWidth)
{
    TestVPURdAckRx::Word256b input;
    input.fill(0);

    // Set test pattern for INT8: each byte contains one 8-bit value
    for (size_t i = 0; i < 16; i++)
    {                  // Fill first 128 bits (16 bytes)
        input[i] = i;  // Unique 8-bit value for each byte
    }

    // Test extend_data
    auto extended = rd_ack_rx.extend_data(input, static_cast<uint8_t>(dtype::WidthCode::W8));

    // Verify extended data - each 8-bit value should be followed by padding
    for (size_t i = 0; i < 16; i++)
    {
        EXPECT_EQ(extended[i * 2], input[i]) << "Value mismatch at byte " << i;
        EXPECT_EQ(extended[i * 2 + 1], 0) << "Padding mismatch at byte " << i;
    }

    // Test remove_padding
    auto removed = wr_req_tx.remove_padding(extended, static_cast<uint8_t>(dtype::WidthCode::W8));

    // Verify the original data is recovered
    for (size_t i = 0; i < 16; i++)
    {
        EXPECT_EQ(removed[i], input[i]) << "Data mismatch at byte " << i;
    }
}

// Test 16-bit width data
TEST_F(DataPaddingTest, SixteenBitWidth)
{
    TestVPURdAckRx::Word256b input;
    input.fill(0);

    // Set test pattern for INT16: each pair of bytes contains one 16-bit value
    for (size_t i = 0; i < 8; i++)
    {  // Fill first 128 bits (16 bytes, 8 pairs)
        // Create a unique 16-bit value
        uint16_t value = 0xAA00 + i;
        input[i * 2] = value & 0xFF;    // Low byte
        input[i * 2 + 1] = value >> 8;  // High byte
    }

    // Test extend_data
    auto extended = rd_ack_rx.extend_data(input, static_cast<uint8_t>(dtype::WidthCode::W16));

    // Verify extended data - each 16-bit value should be followed by padding
    for (size_t i = 0; i < 8; i++)
    {
        EXPECT_EQ(extended[i * 4], input[i * 2]) << "Low byte mismatch at pair " << i;
        EXPECT_EQ(extended[i * 4 + 1], input[i * 2 + 1]) << "High byte mismatch at pair " << i;
        EXPECT_EQ(extended[i * 4 + 2], 0) << "First padding byte mismatch at pair " << i;
        EXPECT_EQ(extended[i * 4 + 3], 0) << "Second padding byte mismatch at pair " << i;
    }

    // Test remove_padding
    auto removed = wr_req_tx.remove_padding(extended, static_cast<uint8_t>(dtype::WidthCode::W16));

    // Verify the original data is recovered
    for (size_t i = 0; i < 16; i++)
    {
        EXPECT_EQ(removed[i], input[i]) << "Data mismatch at byte " << i;
    }
}

// Test boundary values
TEST_F(DataPaddingTest, BoundaryValues)
{
    TestVPURdAckRx::Word256b input;
    input.fill(0xFF);  // Set all bits to 1 in first 128 bits

    // Clear upper 128 bits
    for (size_t i = 16; i < 32; i++)
    {
        input[i] = 0;
    }

    // Test with different widths
    std::array<uint8_t, 3> widths = {static_cast<uint8_t>(dtype::WidthCode::W4),
                                     static_cast<uint8_t>(dtype::WidthCode::W8),
                                     static_cast<uint8_t>(dtype::WidthCode::W16)};

    for (auto width : widths)
    {
        auto extended = rd_ack_rx.extend_data(input, width);
        auto removed = wr_req_tx.remove_padding(extended, width);

        // For each width, verify that padding bits are 0 in extended data
        uint32_t bit_width = width_code_to_bits(static_cast<dtype::WidthCode>(width));
        uint32_t num_elements = 128 / bit_width;  // Number of elements in 128 bits

        for (uint32_t i = 0; i < num_elements; i++)
        {
            uint32_t pad_start = i * bit_width * 2 + bit_width;
            uint32_t pad_end = (i + 1) * bit_width * 2;

            for (uint32_t j = pad_start; j < pad_end; j++)
            {
                EXPECT_EQ((extended[j / 8] >> (j % 8)) & 0x1, 0)
                    << "Width: " << static_cast<int>(width) << ", Element: " << i << ", Bit: " << j;
            }
        }

        // Verify the original data is recovered (only first 16 bytes)
        for (size_t i = 0; i < 16; i++)
        {
            EXPECT_EQ(removed[i], input[i])
                << "Width: " << static_cast<int>(width) << ", Byte: " << i;
        }
    }
}

}  // namespace test
}  // namespace vpu

int sc_main(int argc, char* argv[])
{
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}