#include "vpu_test_executor.h"
#include <cstdint>
#include <cstring>
#include "sysc/kernel/sc_time.h"
#include "test_utils.h"

namespace vpu
{
namespace test
{

VpuTestExecutor::VpuTestExecutor(sc_core::sc_module_name name, VPU& vpu, npu_sc::LocalMemory& mem)
    : sc_core::sc_module(name),
      initiator_socket("initiator_socket"),
      m_vpu(vpu),
      m_mem(mem),
      m_delay(sc_core::SC_ZERO_TIME),
      m_last_prec_out(0),
      m_last_is_format2(false)
{
}

bool VpuTestExecutor::runTest(const VpuTestCase& test_case)
{
    // 重置上一次的测试结果
    last_result = TestResult{};

    try
    {
        // 1. 准备内存数据
        prepareMemory(test_case);
        wait(0, sc_core::SC_NS);
        // 2. 执行指令序列
        for (const auto& cmd : test_case.cmd_sequence)
        {
            executeCommand(cmd);
            wait(0, sc_core::SC_NS);
        }

        // 3. 验证结果
        return verifyResults(test_case);
    }
    catch (const std::exception& e)
    {
        last_result.passed = false;
        last_result.error_message = std::string("Exception during test execution: ") + e.what();
        return false;
    }
}

void VpuTestExecutor::prepareMemory(const VpuTestCase& test_case)
{
    // 写入输入数据
    writeToMemory(test_case.byte_base_in1, test_case.input1_data);
    if (test_case.func_sel == FunctSel::VV_V)
    {
        writeToMemory(test_case.byte_base_in2, test_case.input2_data);
    }
}

void VpuTestExecutor::executeCommand(const instruction::IssueQueueCmd& cmd)
{
    // 通过VPU的cfg_socket发送指令
    tlm::tlm_generic_payload trans;

    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_data_ptr(
        reinterpret_cast<unsigned char*>(const_cast<instruction::IssueQueueCmd*>(&cmd)));
    trans.set_data_length(sizeof(cmd));
    trans.set_streaming_width(sizeof(cmd));
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

    initiator_socket->b_transport(trans, m_delay);
}

bool VpuTestExecutor::verifyResults(const VpuTestCase& test_case)
{
    if (test_case.func_sel == FunctSel::V_S)
    {
        // 对于V_S操作，验证标量结果
        last_result.actual_scalar = m_vpu.get_result_scalar();
        // 1. 从input1_data中解析出8组数据
        std::vector<std::vector<float>> float_groups;
        std::vector<std::vector<int32_t>> int_groups;

        // 假设input1_data只有一个Word256b
        for (size_t i = 0; i < 8; ++i)
        {
            if (test_case.prec_in1 == dtype::FP32 || test_case.prec_in1 == dtype::FP16 ||
                test_case.prec_in1 == dtype::BF16)
            {
                std::vector<int32_t> input_data = test_case.raw_vpe_inputs1[i];
                std::vector<float> float_data;
                for (auto val : input_data)
                {
                    float float_val;
                    glb_mh2f_conv(val, test_case.prec_in1, &float_val);
                    float_data.push_back(float_val);
                }
                float_groups.push_back(float_data);
            }
            else
            {
                std::vector<int32_t> input_data = test_case.raw_vpe_inputs1[i];
                int_groups.push_back(input_data);
            }
        }

        // 2. 递归处理数据
        if (test_case.prec_in1 == dtype::FP32 || test_case.prec_in1 == dtype::FP16 ||
            test_case.prec_in1 == dtype::BF16)
        {
            // 处理浮点数据
            while (float_groups.size() > 1)
            {
                std::vector<std::vector<float>> next_level;
                for (size_t i = 0; i < float_groups.size(); i += 2)
                {
                    auto result = vpe_generator.calculateExpected(test_case.op_mode,
                                                                  float_groups[i],
                                                                  float_groups[i + 1],
                                                                  test_case.prec_out);
                    next_level.push_back(result);
                }
                float_groups = std::move(next_level);
            }

            // 最后一次reduction
            float final_result = vpe_generator.calculateReductionExpected<float>(
                test_case.op_mode, float_groups[0], test_case.prec_out);
            float actual_scalar_float;
            glb_mh2f_conv(last_result.actual_scalar, test_case.prec_out, &actual_scalar_float);
            if (TestUtils::isFloatEqual(final_result, actual_scalar_float, 0.99))
            {
                last_result.passed = true;
            }
            else
            {
                last_result.passed = false;
                last_result.error_message =
                    "Float result mismatch. Expected: " + std::to_string(final_result) +
                    ", Got: " + std::to_string(actual_scalar_float);
                return false;
            }
        }
        else
        {
            // 处理整数数据
            while (int_groups.size() > 1)
            {
                std::vector<std::vector<int32_t>> next_level;
                for (size_t i = 0; i < int_groups.size(); i += 2)
                {
                    // std::cout << "level " << i/2 + 1 << " input1: ";
                    // for(auto val : int_groups[i]) {
                    //     std::cout << val << " ";
                    // }
                    // std::cout << std::endl;
                    // std::cout << "          input2: ";
                    // for(auto val : int_groups[i+1]) {
                    //     std::cout << val << " ";
                    // }
                    // std::cout << std::endl;

                    auto result = vpe_generator.calculateExpected(
                        test_case.op_mode, int_groups[i], int_groups[i + 1], test_case.prec_out);
                    // std::cout <<" output: ";
                    // for(auto val : result) {
                    //     std::cout << val << " ";
                    // }
                    // std::cout << std::endl;
                    next_level.push_back(result);
                }
                int_groups = std::move(next_level);
            }

            // 最后一次reduction
            int32_t final_result = vpe_generator.calculateReductionExpected<int32_t>(
                test_case.op_mode, int_groups[0], test_case.prec_out);
            // std::cout << "final result: " << final_result << std::endl;
            int32_t actual_scalar_int32 =
                uint32_to_int32_prec(last_result.actual_scalar, test_case.prec_out);

            if (actual_scalar_int32 != final_result)
            {
                last_result.passed = false;
                last_result.error_message =
                    "Scalar result mismatch. Expected: " + std::to_string(final_result) +
                    ", Got: " + std::to_string(actual_scalar_int32);
                return false;
            }
            else
            {
                last_result.passed = true;
                return true;
            }
        }
    }
    else
    {
        // 对于其他操作，验证内存中的结果
        last_result.actual_output =
            readFromMemory(test_case.byte_base_out, test_case.expected_output.size());

        // 设置精度和格式信息
        m_last_prec_out = test_case.prec_out;
        // Format2模式需要移除填充
        m_last_is_format2 = test_case.is_format2_out;

        std::string error_msg;
        if (!compareResults(last_result.actual_output, test_case.expected_output, error_msg))
        {
            last_result.passed = false;
            last_result.error_message = "Output mismatch: " + error_msg;
            return false;
        }
    }

    last_result.passed = true;
    return true;
}

void VpuTestExecutor::writeToMemory(uint64_t addr, const std::vector<npu_sc::Word256b>& data)
{
    // 使用LocalMemory的接口写入数据
    size_t unit_index = addr >> NPUConfig::LMEM_OFFSET >> 5;
    uint64_t offset = (addr & ((1ULL << NPUConfig::LMEM_OFFSET) - 1)) >> 5;

    for (size_t i = 0; i < data.size(); ++i)
    {
        m_mem.write_word(unit_index, offset + i, data[i]);
    }
}

std::vector<npu_sc::Word256b> VpuTestExecutor::readFromMemory(uint64_t addr, size_t num_words)
{
    std::vector<npu_sc::Word256b> result(num_words);
    size_t unit_index = addr >> NPUConfig::LMEM_OFFSET >> 5;
    uint64_t offset = (addr & ((1ULL << NPUConfig::LMEM_OFFSET) - 1)) >> 5;

    for (size_t i = 0; i < num_words; ++i)
    {
        m_mem.read_word(unit_index, offset + i, result[i]);
    }

    return result;
}

bool VpuTestExecutor::compareResults(const std::vector<npu_sc::Word256b>& actual,
                                     const std::vector<npu_sc::Word256b>& expected,
                                     std::string& error_msg)
{
    if (actual.size() != expected.size())
    {
        error_msg = "Size mismatch. Expected: " + std::to_string(expected.size()) +
                    " words, Got: " + std::to_string(actual.size()) + " words";
        return false;
    }

    // 浮点数比较的容差
    constexpr float float_tolerance = 0.99;

    // 遍历每个Word256b
    for (size_t word_idx = 0; word_idx < actual.size(); ++word_idx)
    {
        // 每个Word256b包含8个uint32_t
        for (size_t i = 0; i < 8; ++i)
        {
            uint32_t actual_val = 0;
            uint32_t expected_val = 0;

            // 从Word256b中提取uint32_t，注意偏移量是i*4因为每个uint32_t占4字节
            std::memcpy(&actual_val, &actual[word_idx][i * 4], sizeof(uint32_t));
            std::memcpy(&expected_val, &expected[word_idx][i * 4], sizeof(uint32_t));

            // 根据prec_out判断数据类型
            if (m_last_prec_out == dtype::FP32 || m_last_prec_out == dtype::FP16 ||
                m_last_prec_out == dtype::BF16)
            {
                // 浮点数比较
                std::vector<float> actual_floats =
                    TestUtils::unpackFloat(actual_val, m_last_prec_out, m_last_is_format2);
                std::vector<float> expected_floats =
                    TestUtils::unpackFloat(expected_val, m_last_prec_out, m_last_is_format2);

                if (actual_floats.size() != expected_floats.size())
                {
                    error_msg = "Float array size mismatch at word " + std::to_string(word_idx) +
                                ", element " + std::to_string(i);
                    return false;
                }

                for (size_t j = 0; j < actual_floats.size(); ++j)
                {
                    if (!TestUtils::isFloatEqual(
                            actual_floats[j], expected_floats[j], float_tolerance))
                    {
                        error_msg = "Float mismatch at word " + std::to_string(word_idx) +
                                    ", element " + std::to_string(i) + "." + std::to_string(j) +
                                    ". Expected: " + std::to_string(expected_floats[j]) +
                                    ", Got: " + std::to_string(actual_floats[j]);
                        return false;
                    }
                }
            }
            else
            {
                // 整数比较
                std::vector<int32_t> actual_ints =
                    TestUtils::unpackData(actual_val, m_last_prec_out, m_last_is_format2);
                std::vector<int32_t> expected_ints =
                    TestUtils::unpackData(expected_val, m_last_prec_out, m_last_is_format2);

                if (actual_ints.size() != expected_ints.size())
                {
                    error_msg = "Integer array size mismatch at word " + std::to_string(word_idx) +
                                ", element " + std::to_string(i);
                    return false;
                }

                for (size_t j = 0; j < actual_ints.size(); ++j)
                {
                    if (actual_ints[j] != expected_ints[j])
                    {
                        error_msg = "Integer mismatch at word " + std::to_string(word_idx) +
                                    ", element " + std::to_string(i) + "." + std::to_string(j) +
                                    ". Expected: " + std::to_string(expected_ints[j]) +
                                    ", Got: " + std::to_string(actual_ints[j]);
                        return false;
                    }
                }
            }
        }
    }

    return true;
}

}  // namespace test
}  // namespace vpu