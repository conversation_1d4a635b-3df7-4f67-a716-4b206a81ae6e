#include "vpu_test_generator.h"
#include <cstdint>
#include <cstring>
#include <magic_enum.hpp>
#include "npu_config.h"
#include "test_utils.h"
#include "vpe/vpe_arithmetic.h"
namespace vpu
{
namespace test
{

VpuTestGenerator::VpuTestGenerator() : rng(std::random_device{}())
{
    next_mem_addr = npu_sc::make_address(0, 0);
}

void VpuTestGenerator::reset()
{
    next_mem_addr = npu_sc::make_address(0, 0);
}

VpuTestCase VpuTestGenerator::generateBasicTest(OpMode op,
                                                FunctSel func,
                                                uint8_t prec_in1,
                                                uint8_t prec_in2,
                                                uint8_t prec_out,
                                                uint32_t val_in2,
                                                const std::string& test_name)
{
    VpuTestCase test_case{};
    test_case.op_mode = op;
    test_case.func_sel = func;
    test_case.prec_in1 = prec_in1;
    test_case.prec_in2 = prec_in2;
    test_case.prec_out = prec_out;
    test_case.test_name =
        test_name.empty() ? "Basic_" + std::string(magic_enum::enum_name(func)) : test_name;

    // 1. 分配内存地址
    test_case.byte_base_in1 = allocateMemoryRegion();
    if (func == FunctSel::VS_V)
        test_case.byte_base_in2 = val_in2;
    else
        test_case.byte_base_in2 = allocateMemoryRegion();
    test_case.byte_base_out = allocateMemoryRegion();

    // 2. 生成VPE测试数据
    uint32_t in1_bits = width_code_to_bits(static_cast<dtype::WidthCode>(prec_in1 & 0x7));
    uint32_t out_bits = width_code_to_bits(static_cast<dtype::WidthCode>(prec_out & 0x7));
    bool input1_remove_padding = false, input2_remove_padding = false,
         output_remove_padding = false;
    WidthMode width_mode{};
    if (in1_bits == out_bits)
    {
        width_mode = WidthMode::SINGLE_WIDTH;
    }
    else if (in1_bits * 2 == out_bits)
    {
        width_mode = WidthMode::WIDENING;
        input1_remove_padding = true;
        input2_remove_padding = true;
    }
    else if (in1_bits == 2 * out_bits)
    {
        width_mode = WidthMode::NARROWING;
        input2_remove_padding = true;
        output_remove_padding = true;
    }
    else
    {
        width_mode = WidthMode::SINGLE_WIDTH;
    }

    // 生成8组VPE测试数据
    std::vector<std::vector<int32_t>> vpe_inputs1{};
    std::vector<std::vector<int32_t>> vpe_inputs2{};
    std::vector<std::vector<int32_t>> vpe_outputs{};
    std::vector<uint32_t> packed_in1{};
    std::vector<uint32_t> packed_in2{};
    std::vector<uint32_t> packed_out{};
    uint32_t packed_input1{}, packed_input2{}, result{};

    for (int i = 0; i < 8; ++i)
    {
        auto vpe_case = vpe_generator.generateTestCases(
            op, width_mode, func, prec_out, prec_in1, prec_in2, val_in2);

        if (!vpe_case.empty())
        {
            vpe_inputs1.push_back(vpe_case[0].input1);
            vpe_outputs.push_back(vpe_case[0].expected);
            packed_input1 =
                TestUtils::packData(vpe_case[0].input1, prec_in1, vpe_case[0].is_format2_in1);
            // 只在需要input2的情况下打包input2
            if (func == FunctSel::VV_V)
            {
                packed_input2 =
                    TestUtils::packData(vpe_case[0].input2, prec_in2, vpe_case[0].is_format2_in2);
                vpe_inputs2.push_back(vpe_case[0].input2);
            }
            else
            {  // VS_V case
                packed_input2 = val_in2;
            }
            if (func != FunctSel::V_S)
            {
                result =
                    TestUtils::packData(vpe_case[0].expected, prec_out, vpe_case[0].is_format2_out);
                packed_out.push_back(result);
            }
            // 输出数据
            packed_in1.push_back(packed_input1);
            packed_in2.push_back(packed_input2);

            test_case.is_format2_out = vpe_case[0].is_format2_out;
        }
    }

    // 保存原始VPE数据
    test_case.raw_vpe_inputs1 = vpe_inputs1;
    test_case.raw_vpe_inputs2 = vpe_inputs2;
    test_case.raw_vpe_outputs = vpe_outputs;
    test_case.is_format2_in1 = input1_remove_padding;
    // 3. 转换为Word256b格式
    Word256b input1_data =
        TestUtils::convertToWord256b(packed_in1, input1_remove_padding, prec_in1);
    Word256b input2_data =
        TestUtils::convertToWord256b(packed_in2, input2_remove_padding, prec_in2);
    Word256b expected_output =
        TestUtils::convertToWord256b(packed_out, output_remove_padding, prec_out);
    test_case.input1_data.push_back(input1_data);
    test_case.input2_data.push_back(input2_data);
    test_case.expected_output.push_back(expected_output);
    // 4. 生成指令序列
    test_case.cmd_sequence = generateCmdSequence(op,
                                                 func,
                                                 test_case.byte_base_in1,
                                                 test_case.byte_base_in2,
                                                 test_case.byte_base_out,
                                                 prec_in1,
                                                 prec_in2,
                                                 prec_out,
                                                 width_mode);

    return test_case;
}

VpuTestCase VpuTestGenerator::generateVVTest(OpMode op,
                                             uint8_t prec_in1,
                                             uint8_t prec_in2,
                                             uint8_t prec_out)
{
    return generateBasicTest(op,
                             FunctSel::VV_V,
                             prec_in1,
                             prec_in2,
                             prec_out,
                             0,
                             "VV_" + std::string(magic_enum::enum_name(op)));
}

VpuTestCase VpuTestGenerator::generateVSTest(OpMode op,
                                             uint8_t prec_in1,
                                             uint8_t prec_in2,
                                             uint8_t prec_out,
                                             uint32_t val_in2)
{
    return generateBasicTest(op,
                             FunctSel::VS_V,
                             prec_in1,
                             prec_in2,
                             prec_out,
                             val_in2,
                             "VS_" + std::string(magic_enum::enum_name(op)));
}

VpuTestCase VpuTestGenerator::generateVtoSTest(OpMode op, uint8_t prec_in, uint8_t prec_out)
{
    return generateBasicTest(op,
                             FunctSel::V_S,
                             prec_in,
                             prec_in,
                             prec_out,
                             0,
                             "V_S_" + std::string(magic_enum::enum_name(op)));
}

VpuTestCase VpuTestGenerator::generateVtoVTest(uint8_t prec_in, uint8_t prec_out)
{
    return generateBasicTest(OpMode::ADD,
                             FunctSel::V_V,
                             prec_in,
                             prec_in,
                             prec_out,
                             0,
                             "V_V_" + std::to_string(prec_in) + "_to_" + std::to_string(prec_out));
}

std::vector<instruction::IssueQueueCmd> VpuTestGenerator::generateCmdSequence(OpMode op,
                                                                              FunctSel func,
                                                                              uint64_t addr_in1,
                                                                              uint64_t addr_in2,
                                                                              uint64_t addr_out,
                                                                              uint8_t prec_in1,
                                                                              uint8_t prec_in2,
                                                                              uint8_t prec_out,
                                                                              WidthMode width_mode)
{
    std::vector<instruction::IssueQueueCmd> cmds{};

    // 1. 构建VectorProcessConfig
    VectorProcessConfig cfg{};
    configureVectorProcess(cfg, op, prec_in1, prec_in2, prec_out, width_mode);

    // 2. 使用ConfigManager生成配置指令
    hardware_instruction::VpConfigManager cfg_manager;
    cfg_manager.setConfig(cfg);

    // 生成两条配置指令
    instruction::IssueQueueCmd cfg_cmd1;
    cfg_cmd1.funct7 = instruction::opcode::VP_CFG;
    cfg_cmd1.rs1val = 0;                       // 第一个寄存器地址
    cfg_cmd1.rs2val = cfg_manager.readReg(0);  // 读取第一个寄存器的值
    cmds.push_back(cfg_cmd1);

    instruction::IssueQueueCmd cfg_cmd2;
    cfg_cmd2.funct7 = instruction::opcode::VP_CFG;
    cfg_cmd2.rs1val = 1;                       // 第二个寄存器地址
    cfg_cmd2.rs2val = cfg_manager.readReg(1);  // 读取第二个寄存器的值
    cmds.push_back(cfg_cmd2);

    // 2. 根据功能类型生成PRE和DRV指令
    switch (func)
    {
        case FunctSel::VV_V:
        {
            instruction::IssueQueueCmd pre_cmd;
            pre_cmd.funct7 = instruction::opcode::VV_V_PRE;
            pre_cmd.rs1val = addr_in2;
            cmds.push_back(pre_cmd);

            instruction::IssueQueueCmd drv_cmd;
            drv_cmd.funct7 = instruction::opcode::VV_V_DRV;
            drv_cmd.rs1val = addr_out;
            drv_cmd.rs2val = addr_in1;
            cmds.push_back(drv_cmd);
            break;
        }
        case FunctSel::VS_V:
        {
            instruction::IssueQueueCmd pre_cmd;
            pre_cmd.funct7 = instruction::opcode::VS_V_PRE;
            pre_cmd.rs1val = addr_in2;  // 标量值
            cmds.push_back(pre_cmd);

            instruction::IssueQueueCmd drv_cmd;
            drv_cmd.funct7 = instruction::opcode::VS_V_DRV;
            drv_cmd.rs1val = addr_out;
            drv_cmd.rs2val = addr_in1;
            cmds.push_back(drv_cmd);
            break;
        }
        case FunctSel::V_S:
        {
            instruction::IssueQueueCmd drv_cmd;
            drv_cmd.funct7 = instruction::opcode::V_S_DRV;
            drv_cmd.rs2val = addr_in1;
            cmds.push_back(drv_cmd);
            break;
        }
        case FunctSel::V_V:
        {
            instruction::IssueQueueCmd drv_cmd;
            drv_cmd.funct7 = instruction::opcode::V_V_DRV;
            drv_cmd.rs1val = addr_out;
            drv_cmd.rs2val = addr_in1;
            cmds.push_back(drv_cmd);
            break;
        }
    }

    return cmds;
}

void VpuTestGenerator::configureVectorProcess(VectorProcessConfig& cfg,
                                              OpMode op,
                                              uint8_t prec_in1,
                                              uint8_t prec_in2,
                                              uint8_t prec_out,
                                              WidthMode width_mode)
{
    // 设置数据类型
    cfg.cfg_type_in1 = prec_in1 >> 3;  // 高2位为type
    cfg.cfg_type_in2 = prec_in2 >> 3;
    cfg.cfg_type_out = prec_out >> 3;

    // 设置基准宽度（使用最小的宽度作为参考）
    cfg.cfg_wd_ref = std::min({prec_in1 & 0x7, prec_in2 & 0x7, prec_out & 0x7});

    // 直接从width_mode设置缩放因子
    cfg.cfg_wd_sc_out = (static_cast<uint32_t>(width_mode) >> 2) & 0x1;
    cfg.cfg_wd_sc_in1 = (static_cast<uint32_t>(width_mode) >> 1) & 0x1;
    cfg.cfg_wd_sc_in2 = static_cast<uint32_t>(width_mode) & 0x1;

    // 设置操作类型
    cfg.cfg_op = static_cast<uint32_t>(op);

    // 使用成员变量设置大小参数
    cfg.cfg_rem_dim0_ref = m_rem_dim0_ref;
    cfg.cfg_size_dim0b_ref = m_size_dim0b_ref;
}

uint64_t VpuTestGenerator::allocateMemoryRegion(size_t size)
{
    uint64_t addr = next_mem_addr;
    next_mem_addr += size;
    return addr;
}

}  // namespace test
}  // namespace vpu