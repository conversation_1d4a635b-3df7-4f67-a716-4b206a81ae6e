#pragma once

#include <random>
#include <string>
#include <vector>
#include "../../local_mem/local_mem.h"
#include "../vpe/test/test_case_generator.h"
#include "../vpu.h"
#include "utils/register.h"
namespace vpu
{
namespace test
{

// Forward declarations
struct VpuTestCase;

class VpuTestGenerator
{
  public:
    using VectorProcessConfig = hardware_instruction::VectorProcessConfig;
    // 复用VPE的TestCaseGenerator
    vpu::test::TestCaseGenerator vpe_generator;

    VpuTestGenerator();
    void setVectorProcessSize(uint32_t rem_dim0_ref, uint32_t size_dim0b_ref)
    {
        m_rem_dim0_ref = rem_dim0_ref;
        m_size_dim0b_ref = size_dim0b_ref;
    }
    // 生成基础功能测试用例
    VpuTestCase generateBasicTest(OpMode op,
                                  FunctSel func,
                                  uint8_t width_in1,
                                  uint8_t width_in2,
                                  uint8_t width_out,
                                  uint32_t val_in2 = 0,
                                  const std::string& test_name = "");

    // 生成特定功能的测试用例
    VpuTestCase generateVVTest(OpMode op, uint8_t width_in1, uint8_t width_in2, uint8_t width_out);
    VpuTestCase generateVSTest(OpMode op,
                               uint8_t width_in1,
                               uint8_t width_in2,
                               uint8_t width_out,
                               uint32_t val_in2);
    VpuTestCase generateVtoSTest(OpMode op, uint8_t width_in, uint8_t width_out);
    VpuTestCase generateVtoVTest(uint8_t width_in, uint8_t width_out);
    void reset();

  private:
    std::mt19937 rng;        // 随机数生成器
    uint64_t next_mem_addr;  // 用于分配内存地址
    // VectorProcess大小参数
    uint32_t m_rem_dim0_ref = 0;
    uint32_t m_size_dim0b_ref = 1;
    // 生成指令序列
    std::vector<instruction::IssueQueueCmd> generateCmdSequence(OpMode op,
                                                                FunctSel func,
                                                                uint64_t addr_in1,
                                                                uint64_t addr_in2,
                                                                uint64_t addr_out,
                                                                uint8_t width_in1,
                                                                uint8_t width_in2,
                                                                uint8_t width_out,
                                                                WidthMode width_mode);

    void configureVectorProcess(VectorProcessConfig& cfg,
                                OpMode op,
                                uint8_t width_in1,
                                uint8_t width_in2,
                                uint8_t width_out,
                                WidthMode width_mode);
    // 生成配置指令的辅助函数
    uint64_t generateCfgRs1Val(OpMode op, uint8_t width_in1, uint8_t width_in2);
    uint64_t generateCfgRs2Val(OpMode op, uint8_t width_in1, uint8_t width_in2);

    // 内存地址分配
    uint64_t allocateMemoryRegion(size_t size = 0x20);  // 默认分配1KB
};

// Test case structure definition
struct VpuTestCase
{
    // 指令序列
    std::vector<instruction::IssueQueueCmd> cmd_sequence;

    // VPU实际使用的256bit数据
    std::vector<npu_sc::Word256b> input1_data;
    std::vector<npu_sc::Word256b> input2_data;
    std::vector<npu_sc::Word256b> expected_output;
    uint32_t expected_scalar;  // 用于V_S操作

    // 原始VPE测试数据（用于调试和验证）
    std::vector<std::vector<int32_t>> raw_vpe_inputs1;  // 每个VPE的输入1
    std::vector<std::vector<int32_t>> raw_vpe_inputs2;  // 每个VPE的输入2
    std::vector<std::vector<int32_t>> raw_vpe_outputs;  // 每个VPE的预期输出

    // 内存地址（用于验证）
    uint64_t byte_base_in1;
    uint64_t byte_base_in2;
    uint64_t byte_base_out;
    bool is_format2_out;
    bool is_format2_in1;
    // 测试配置信息（用于调试和报告）
    OpMode op_mode;
    FunctSel func_sel;
    uint8_t prec_in1;
    uint8_t prec_in2;
    uint8_t prec_out;
    std::string test_name;
};

}  // namespace test
}  // namespace vpu