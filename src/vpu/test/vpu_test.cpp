#include "../vpu.h"
#include <iomanip>
#include <iostream>
#include "../../local_mem/local_mem.h"
#include "npu_config.h"
#include "sysc/kernel/sc_module.h"
#include "vpu_test_executor.h"
#include "vpu_test_generator.h"

namespace vpu
{
namespace test
{

class VpuTestRunner : public sc_core::sc_module
{
  public:
    // 测试配置结构体定义
    struct TestConfig
    {
        OpMode op;
        uint8_t prec_out;
        uint8_t prec_in1;
        uint8_t prec_in2;
        const char* group_name;
        const char* op_name;
    };
    // 整数运算测试用例定义
    constexpr static const TestConfig kIntegerVVTests[] = {
        // Integer Single-width Add/Subtract
        {OpMode::ADD, dtype::INT4, dtype::INT4, dtype::INT4, "Integer Single-width Add/Sub", "add"},
        {OpMode::SUB, dtype::INT4, dtype::INT4, dtype::INT4, "Integer Single-width Add/Sub", "sub"},
        {OpMode::ADD, dtype::INT8, dtype::INT8, dtype::INT8, "Integer Single-width Add/Sub", "add"},
        {OpMode::SUB, dtype::INT8, dtype::INT8, dtype::INT8, "Integer Single-width Add/Sub", "sub"},
        {OpMode::ADD,
         dtype::INT16,
         dtype::INT16,
         dtype::INT16,
         "Integer Single-width Add/Sub",
         "add"},
        {OpMode::SUB,
         dtype::INT16,
         dtype::INT16,
         dtype::INT16,
         "Integer Single-width Add/Sub",
         "sub"},
        {OpMode::ADD,
         dtype::INT32,
         dtype::INT32,
         dtype::INT32,
         "Integer Single-width Add/Sub",
         "add"},
        {OpMode::SUB,
         dtype::INT32,
         dtype::INT32,
         dtype::INT32,
         "Integer Single-width Add/Sub",
         "sub"},

        // Integer Widening Add/Subtract
        {OpMode::ADD, dtype::INT8, dtype::INT4, dtype::INT4, "Integer Widening Add/Sub", "add"},
        {OpMode::SUB, dtype::INT8, dtype::INT4, dtype::INT4, "Integer Widening Add/Sub", "sub"},
        {OpMode::RSUB, dtype::INT8, dtype::INT4, dtype::INT4, "Integer Widening Add/Sub", "rsub"},
        {OpMode::ADD, dtype::INT16, dtype::INT8, dtype::INT8, "Integer Widening Add/Sub", "add"},
        {OpMode::SUB, dtype::INT16, dtype::INT8, dtype::INT8, "Integer Widening Add/Sub", "sub"},
        {OpMode::RSUB, dtype::INT16, dtype::INT8, dtype::INT8, "Integer Widening Add/Sub", "rsub"},
        {OpMode::ADD, dtype::INT32, dtype::INT16, dtype::INT16, "Integer Widening Add/Sub", "add"},
        {OpMode::SUB, dtype::INT32, dtype::INT16, dtype::INT16, "Integer Widening Add/Sub", "sub"},
        {OpMode::RSUB,
         dtype::INT32,
         dtype::INT16,
         dtype::INT16,
         "Integer Widening Add/Sub",
         "rsub"},

        // Integer Min/Max
        {OpMode::MIN, dtype::INT4, dtype::INT4, dtype::INT4, "Integer Min/Max", "min"},
        {OpMode::MAX, dtype::INT4, dtype::INT4, dtype::INT4, "Integer Min/Max", "max"},
        {OpMode::MIN, dtype::INT8, dtype::INT8, dtype::INT8, "Integer Min/Max", "min"},
        {OpMode::MAX, dtype::INT8, dtype::INT8, dtype::INT8, "Integer Min/Max", "max"},
        {OpMode::MIN, dtype::INT16, dtype::INT16, dtype::INT16, "Integer Min/Max", "min"},
        {OpMode::MAX, dtype::INT16, dtype::INT16, dtype::INT16, "Integer Min/Max", "max"},
        {OpMode::MIN, dtype::INT32, dtype::INT32, dtype::INT32, "Integer Min/Max", "min"},
        {OpMode::MAX, dtype::INT32, dtype::INT32, dtype::INT32, "Integer Min/Max", "max"},

        // Integer Compare
        {OpMode::EQ, dtype::INT4, dtype::INT4, dtype::INT4, "Integer Compare", "equal"},
        {OpMode::NE, dtype::INT4, dtype::INT4, dtype::INT4, "Integer Compare", "not_equal"},
        {OpMode::GT, dtype::INT4, dtype::INT4, dtype::INT4, "Integer Compare", "greater"},
        {OpMode::GE, dtype::INT4, dtype::INT4, dtype::INT4, "Integer Compare", "greater_equal"},
        {OpMode::LT, dtype::INT4, dtype::INT4, dtype::INT4, "Integer Compare", "less"},
        {OpMode::LE, dtype::INT4, dtype::INT4, dtype::INT4, "Integer Compare", "less_equal"},
        {OpMode::EQ, dtype::INT8, dtype::INT8, dtype::INT8, "Integer Compare", "equal"},
        {OpMode::NE, dtype::INT8, dtype::INT8, dtype::INT8, "Integer Compare", "not_equal"},
        {OpMode::GT, dtype::INT8, dtype::INT8, dtype::INT8, "Integer Compare", "greater"},
        {OpMode::GE, dtype::INT8, dtype::INT8, dtype::INT8, "Integer Compare", "greater_equal"},
        {OpMode::LT, dtype::INT8, dtype::INT8, dtype::INT8, "Integer Compare", "less"},
        {OpMode::LE, dtype::INT8, dtype::INT8, dtype::INT8, "Integer Compare", "less_equal"},
        {OpMode::EQ, dtype::INT16, dtype::INT16, dtype::INT16, "Integer Compare", "equal"},
        {OpMode::NE, dtype::INT16, dtype::INT16, dtype::INT16, "Integer Compare", "not_equal"},
        {OpMode::GT, dtype::INT16, dtype::INT16, dtype::INT16, "Integer Compare", "greater"},
        {OpMode::GE, dtype::INT16, dtype::INT16, dtype::INT16, "Integer Compare", "greater_equal"},
        {OpMode::LT, dtype::INT16, dtype::INT16, dtype::INT16, "Integer Compare", "less"},
        {OpMode::LE, dtype::INT16, dtype::INT16, dtype::INT16, "Integer Compare", "less_equal"},
        {OpMode::EQ, dtype::INT32, dtype::INT32, dtype::INT32, "Integer Compare", "equal"},
        {OpMode::NE, dtype::INT32, dtype::INT32, dtype::INT32, "Integer Compare", "not_equal"},
        {OpMode::GT, dtype::INT32, dtype::INT32, dtype::INT32, "Integer Compare", "greater"},
        {OpMode::GE, dtype::INT32, dtype::INT32, dtype::INT32, "Integer Compare", "greater_equal"},
        {OpMode::LT, dtype::INT32, dtype::INT32, dtype::INT32, "Integer Compare", "less"},
        {OpMode::LE, dtype::INT32, dtype::INT32, dtype::INT32, "Integer Compare", "less_equal"},
        // Integer Single-width Shift
        {OpMode::SHL,
         dtype::INT4,
         dtype::INT4,
         dtype::INT4,
         "Integer Single-width Shift",
         "left_shift"},
        {OpMode::SHR_FLOOR,
         dtype::INT4,
         dtype::INT4,
         dtype::INT4,
         "Integer Single-width Shift",
         "right_shift_floor"},
        {OpMode::SHR_CEIL,
         dtype::INT4,
         dtype::INT4,
         dtype::INT4,
         "Integer Single-width Shift",
         "right_shift_ceil"},
        {OpMode::SHR_ROUND,
         dtype::INT4,
         dtype::INT4,
         dtype::INT4,
         "Integer Single-width Shift",
         "right_shift_round"},
        {OpMode::SHL,
         dtype::INT8,
         dtype::INT8,
         dtype::INT8,
         "Integer Single-width Shift",
         "left_shift"},
        {OpMode::SHR_FLOOR,
         dtype::INT8,
         dtype::INT8,
         dtype::INT8,
         "Integer Single-width Shift",
         "right_shift_floor"},
        {OpMode::SHR_CEIL,
         dtype::INT8,
         dtype::INT8,
         dtype::INT8,
         "Integer Single-width Shift",
         "right_shift_ceil"},
        {OpMode::SHR_ROUND,
         dtype::INT8,
         dtype::INT8,
         dtype::INT8,
         "Integer Single-width Shift",
         "right_shift_round"},
        //  Integer Narrowing Right Shift
        {OpMode::SHR_FLOOR,
         dtype::INT4,
         dtype::INT8,
         dtype::INT4,
         "Integer Narrowing Right-shift",
         "right_shift_floor"},
        {OpMode::SHR_CEIL,
         dtype::INT4,
         dtype::INT8,
         dtype::INT4,
         "Integer Narrowing Right-shift",
         "right_shift_ceil"},
        {OpMode::SHR_ROUND,
         dtype::INT4,
         dtype::INT8,
         dtype::INT4,
         "Integer Narrowing Right-shift",
         "right_shift_round"},
        {OpMode::SHR_FLOOR,
         dtype::INT8,
         dtype::INT16,
         dtype::INT8,
         "Integer Narrowing Right-shift",
         "right_shift_floor"},
        {OpMode::SHR_CEIL,
         dtype::INT8,
         dtype::INT16,
         dtype::INT8,
         "Integer Narrowing Right-shift",
         "right_shift_ceil"},
        {OpMode::SHR_ROUND,
         dtype::INT8,
         dtype::INT16,
         dtype::INT8,
         "Integer Narrowing Right-shift",
         "right_shift_round"},
        {OpMode::SHR_FLOOR,
         dtype::INT16,
         dtype::INT32,
         dtype::INT16,
         "Integer Narrowing Right-shift",
         "right_shift_floor"},
        {OpMode::SHR_CEIL,
         dtype::INT16,
         dtype::INT32,
         dtype::INT16,
         "Integer Narrowing Right-shift",
         "right_shift_ceil"},
        {OpMode::SHR_ROUND,
         dtype::INT16,
         dtype::INT32,
         dtype::INT16,
         "Integer Narrowing Right-shift",
         "right_shift_round"},
        // Integer Bitwise Logical
        {OpMode::AND, dtype::INT4, dtype::INT4, dtype::INT4, "Integer Bitwise Logical", "and"},
        {OpMode::OR, dtype::INT4, dtype::INT4, dtype::INT4, "Integer Bitwise Logical", "or"},
        {OpMode::XOR, dtype::INT4, dtype::INT4, dtype::INT4, "Integer Bitwise Logical", "xor"},
        {OpMode::AND, dtype::INT8, dtype::INT8, dtype::INT8, "Integer Bitwise Logical", "and"},
        {OpMode::OR, dtype::INT8, dtype::INT8, dtype::INT8, "Integer Bitwise Logical", "or"},
        {OpMode::XOR, dtype::INT8, dtype::INT8, dtype::INT8, "Integer Bitwise Logical", "xor"},

        // Integer Widening Multiply
        {OpMode::MUL, dtype::INT8, dtype::INT4, dtype::INT4, "Integer Widening Multiply", "mul"},
        {OpMode::MUL, dtype::INT16, dtype::INT8, dtype::INT8, "Integer Widening Multiply", "mul"}};

    // 浮点运算测试用例定义
    constexpr static const TestConfig kFloatVVTests[] = {
        // Floating-point Single-width Add/Subtract
        {OpMode::ADD,
         dtype::FP16,
         dtype::FP16,
         dtype::FP16,
         "Floating-point Single-width Add/Sub",
         "add"},
        {OpMode::SUB,
         dtype::FP16,
         dtype::FP16,
         dtype::FP16,
         "Floating-point Single-width Add/Sub",
         "sub"},
        {OpMode::ADD,
         dtype::BF16,
         dtype::BF16,
         dtype::BF16,
         "Floating-point Single-width Add/Sub",
         "add"},
        {OpMode::SUB,
         dtype::BF16,
         dtype::BF16,
         dtype::BF16,
         "Floating-point Single-width Add/Sub",
         "sub"},
        {OpMode::ADD,
         dtype::FP32,
         dtype::FP32,
         dtype::FP32,
         "Floating-point Single-width Add/Sub",
         "add"},
        {OpMode::SUB,
         dtype::FP32,
         dtype::FP32,
         dtype::FP32,
         "Floating-point Single-width Add/Sub",
         "sub"},

        // Floating-point Widening Add/Subtract
        {OpMode::ADD,
         dtype::FP32,
         dtype::FP16,
         dtype::FP16,
         "Floating-point Widening Add/Sub",
         "add"},
        {OpMode::SUB,
         dtype::FP32,
         dtype::FP16,
         dtype::FP16,
         "Floating-point Widening Add/Sub",
         "sub"},
        {OpMode::ADD,
         dtype::FP32,
         dtype::BF16,
         dtype::BF16,
         "Floating-point Widening Add/Sub",
         "add"},
        {OpMode::SUB,
         dtype::FP32,
         dtype::BF16,
         dtype::BF16,
         "Floating-point Widening Add/Sub",
         "sub"},

        // Floating-point Min/Max
        {OpMode::MIN, dtype::FP16, dtype::FP16, dtype::FP16, "Floating-point Min/Max", "min"},
        {OpMode::MAX, dtype::FP16, dtype::FP16, dtype::FP16, "Floating-point Min/Max", "max"},
        {OpMode::MIN, dtype::BF16, dtype::BF16, dtype::BF16, "Floating-point Min/Max", "min"},
        {OpMode::MAX, dtype::BF16, dtype::BF16, dtype::BF16, "Floating-point Min/Max", "max"},

        // Floating-point Compare
        {OpMode::EQ, dtype::FP16, dtype::FP16, dtype::FP16, "Floating-point Compare", "equal"},
        {OpMode::NE, dtype::FP16, dtype::FP16, dtype::FP16, "Floating-point Compare", "not_equal"},
        {OpMode::GT, dtype::FP16, dtype::FP16, dtype::FP16, "Floating-point Compare", "greater"},
        {OpMode::GE,
         dtype::FP16,
         dtype::FP16,
         dtype::FP16,
         "Floating-point Compare",
         "greater_equal"},
        {OpMode::LT, dtype::FP16, dtype::FP16, dtype::FP16, "Floating-point Compare", "less"},
        {OpMode::LE, dtype::FP16, dtype::FP16, dtype::FP16, "Floating-point Compare", "less_equal"},
        {OpMode::EQ, dtype::BF16, dtype::BF16, dtype::BF16, "Floating-point Compare", "equal"},
        {OpMode::NE, dtype::BF16, dtype::BF16, dtype::BF16, "Floating-point Compare", "not_equal"},
        {OpMode::GT, dtype::BF16, dtype::BF16, dtype::BF16, "Floating-point Compare", "greater"},
        {OpMode::GE,
         dtype::BF16,
         dtype::BF16,
         dtype::BF16,
         "Floating-point Compare",
         "greater_equal"},
        {OpMode::LT, dtype::BF16, dtype::BF16, dtype::BF16, "Floating-point Compare", "less"},
        {OpMode::LE, dtype::BF16, dtype::BF16, dtype::BF16, "Floating-point Compare", "less_equal"},

        // Floating-point Single-width Multiply
        {OpMode::MUL,
         dtype::FP16,
         dtype::FP16,
         dtype::FP16,
         "Floating-point Single-width Multiply",
         "mul"},
        {OpMode::MUL,
         dtype::BF16,
         dtype::BF16,
         dtype::BF16,
         "Floating-point Single-width Multiply",
         "mul"},

        // Floating-point Widening Multiply
        {OpMode::MUL,
         dtype::FP32,
         dtype::FP16,
         dtype::FP16,
         "Floating-point Widening Multiply",
         "mul"},
        {OpMode::MUL,
         dtype::FP32,
         dtype::BF16,
         dtype::BF16,
         "Floating-point Widening Multiply",
         "mul"}};

    struct ConversionTest
    {
        uint8_t width_in;
        uint8_t width_out;
        const char* group_name;
        const char* name;
    };

    constexpr static const ConversionTest kConversionTests[] = {
        // Single-width FP-INT conversions
        {dtype::BF16,
         dtype::INT16,
         "Single-width FP-INT conversions",
         "bf16_to_int16"},  // BF16 -> INT16
        {dtype::FP16,
         dtype::INT16,
         "Single-width FP-INT conversions",
         "fp16_to_int16"},  // FP16 -> INT16
        {dtype::FP32,
         dtype::INT32,
         "Single-width FP-INT conversions",
         "fp32_to_int32"},  // FP32 -> INT32
        {dtype::INT16,
         dtype::BF16,
         "Single-width FP-INT conversions",
         "int16_to_bf16"},  // INT16 -> BF16
        {dtype::INT16,
         dtype::FP16,
         "Single-width FP-INT conversions",
         "int16_to_fp16"},  // INT16 -> FP16
        {dtype::INT32,
         dtype::FP32,
         "Single-width FP-INT conversions",
         "int32_to_fp32"},  // INT32 -> FP32

        // Single-width FP-FP conversions
        {dtype::BF16,
         dtype::FP16,
         "Single-width FP-FP conversions",
         "bf16_to_fp16"},  // BF16 -> FP16
        {dtype::FP16,
         dtype::BF16,
         "Single-width FP-FP conversions",
         "fp16_to_bf16"},  // FP16 -> BF16

        // Widening FP-INT conversions
        {dtype::BF16,
         dtype::INT32,
         "Widening FP-INT conversions",
         "bf16_to_int32"},  // BF16 -> INT8
        {dtype::FP16,
         dtype::INT32,
         "Widening FP-INT conversions",
         "fp16_to_int32"},  // FP16 -> INT8
        // Widening INT-FP conversions
        {dtype::INT8, dtype::BF16, "Widening INT-FP conversions", "int8_to_bf16"},  // INT8 -> BF16
        {dtype::INT8, dtype::FP16, "Widening INT-FP conversions", "int8_to_fp16"},  // INT8 -> FP16
        {dtype::INT16,
         dtype::FP32,
         "Widening INT-FP conversions",
         "int16_to_fp32"},  // INT16 -> FP32
        // Widening FP-FP conversions
        {dtype::FP16, dtype::FP32, "Widening FP-FP conversions", "fp16_to_fp32"},  // FP32 -> BF16
        {dtype::BF16, dtype::FP32, "Widening FP-FP conversions", "bf16_to_fp32"},  // FP32 -> FP16
        // Narrowing FP-INT conversions
        {dtype::BF16, dtype::INT8, "Narrowing FP-INT conversions", "bf16_to_int8"},  // BF16 -> INT8
        {dtype::FP16, dtype::INT8, "Narrowing FP-INT conversions", "fp16_to_int8"},  // FP16 -> INT8
        {dtype::FP32,
         dtype::INT16,
         "Narrowing FP-INT conversions",
         "fp32_to_int16"},  // FP32 -> INT16

        // Narrowing INT-FP conversions
        {dtype::INT32,
         dtype::BF16,
         "Narrowing INT-FP conversions",
         "int32_to_bf16"},  // INT32 -> BF16
        {dtype::INT32,
         dtype::FP16,
         "Narrowing INT-FP conversions",
         "int32_to_fp16"},  // INT32 -> FP16

        // Narrowing FP-FP conversions
        {dtype::FP32, dtype::BF16, "Narrowing FP-FP conversions", "fp32_to_bf16"},  // FP32 -> BF16
        {dtype::FP32, dtype::FP16, "Narrowing FP-FP conversions", "fp32_to_fp16"}   // FP32 -> FP16
    };

    struct ReductionTest
    {
        OpMode op;
        uint8_t prec_in;
        uint8_t prec_out;
        const char* group_name;
        const char* name;
    };

    constexpr static const ReductionTest kReductionTests[] = {
        // Integer Single-width Sum
        {OpMode::ADD, dtype::INT4, dtype::INT4, "Integer Single-width Sum", "int4_sum"},
        {OpMode::ADD, dtype::INT8, dtype::INT8, "Integer Single-width Sum", "int8_sum"},
        {OpMode::ADD, dtype::INT16, dtype::INT16, "Integer Single-width Sum", "int16_sum"},
        {OpMode::ADD, dtype::INT32, dtype::INT32, "Integer Single-width Sum", "int32_sum"},

        // Integer Widening Sum
        {OpMode::ADD, dtype::INT4, dtype::INT8, "Integer Widening Sum", "int4_to_int8_sum"},
        {OpMode::ADD, dtype::INT8, dtype::INT16, "Integer Widening Sum", "int8_to_int16_sum"},
        {OpMode::ADD, dtype::INT16, dtype::INT32, "Integer Widening Sum", "int16_to_int32_sum"},

        // Integer Min
        {OpMode::MIN, dtype::INT4, dtype::INT4, "Integer Min", "int4_min"},
        {OpMode::MIN, dtype::INT8, dtype::INT8, "Integer Min", "int8_min"},
        {OpMode::MIN, dtype::INT16, dtype::INT16, "Integer Min", "int16_min"},
        {OpMode::MIN, dtype::INT32, dtype::INT32, "Integer Min", "int32_min"},

        // Integer Max
        {OpMode::MAX, dtype::INT4, dtype::INT4, "Integer Max", "int4_max"},
        {OpMode::MAX, dtype::INT8, dtype::INT8, "Integer Max", "int8_max"},
        {OpMode::MAX, dtype::INT16, dtype::INT16, "Integer Max", "int16_max"},
        {OpMode::MAX, dtype::INT32, dtype::INT32, "Integer Max", "int32_max"},

        // Integer AND
        {OpMode::AND, dtype::INT4, dtype::INT4, "Integer AND", "int4_and"},
        {OpMode::AND, dtype::INT8, dtype::INT8, "Integer AND", "int8_and"},
        {OpMode::AND, dtype::INT16, dtype::INT16, "Integer AND", "int16_and"},
        {OpMode::AND, dtype::INT32, dtype::INT32, "Integer AND", "int32_and"},

        // Integer OR
        {OpMode::OR, dtype::INT4, dtype::INT4, "Integer OR", "int4_or"},
        {OpMode::OR, dtype::INT8, dtype::INT8, "Integer OR", "int8_or"},
        {OpMode::OR, dtype::INT16, dtype::INT16, "Integer OR", "int16_or"},
        {OpMode::OR, dtype::INT32, dtype::INT32, "Integer OR", "int32_or"},

        // Integer XOR
        {OpMode::XOR, dtype::INT4, dtype::INT4, "Integer XOR", "int4_xor"},
        {OpMode::XOR, dtype::INT8, dtype::INT8, "Integer XOR", "int8_xor"},
        {OpMode::XOR, dtype::INT16, dtype::INT16, "Integer XOR", "int16_xor"},
        {OpMode::XOR, dtype::INT32, dtype::INT32, "Integer XOR", "int32_xor"},

        // Floating-point Single-width Sum
        {OpMode::ADD, dtype::FP16, dtype::FP16, "Floating-point Single-width Sum", "fp16_sum"},
        {OpMode::ADD, dtype::BF16, dtype::BF16, "Floating-point Single-width Sum", "bf16_sum"},
        {OpMode::ADD, dtype::FP32, dtype::FP32, "Floating-point Single-width Sum", "fp32_sum"},

        // Floating-point Widening Sum
        {OpMode::ADD, dtype::FP16, dtype::FP32, "Floating-point Widening Sum", "fp16_to_fp32_sum"},
        {OpMode::ADD, dtype::BF16, dtype::FP32, "Floating-point Widening Sum", "bf16_to_fp32_sum"},

        // Floating-point Min
        {OpMode::MIN, dtype::FP16, dtype::FP16, "Floating-point Min", "fp16_min"},
        {OpMode::MIN, dtype::BF16, dtype::BF16, "Floating-point Min", "bf16_min"},

        // Floating-point Max
        {OpMode::MAX, dtype::FP16, dtype::FP16, "Floating-point Max", "fp16_max"},
        {OpMode::MAX, dtype::BF16, dtype::BF16, "Floating-point Max", "bf16_max"}};

    VpuTestRunner(sc_core::sc_module_name name) : sc_core::sc_module(name)
    {
        // 创建并初始化测试环境
        vpu = std::make_unique<VPU>("vpu");
        local_mem = std::make_unique<npu_sc::LocalMemory>("local_mem");
        generator = std::make_unique<VpuTestGenerator>();
        executor = std::make_unique<VpuTestExecutor>("executor", *vpu, *local_mem);
        executor->initiator_socket.bind(vpu->cfg_socket);
        vpu->vpu_rd_ack_rx.init_socket.bind(local_mem->target_socket);
        vpu->vpu_wr_req_tx.init_socket.bind(local_mem->target_socket);
        SC_THREAD(runAllTests);
    }

    void runAllTests()
    {
        std::cout << "\nRunning VPU Tests...\n" << std::endl;

        // 运行所有测试用例
        runVVTests();    // all passed
        runVSTests();    // all passed
        runVtoSTests();  // all passed
        runVtoVTests();  // all passed

        // 打印总结
        std::cout << "\nTest Summary:\n"
                  << "Total Tests: " << total_tests << "\n"
                  << "Passed: " << passed_tests << "\n"
                  << "Failed: " << (total_tests - passed_tests) << "\n"
                  << std::endl;
    }

  private:
    std::unique_ptr<VPU> vpu;
    std::unique_ptr<npu_sc::LocalMemory> local_mem;
    std::unique_ptr<VpuTestGenerator> generator;
    std::unique_ptr<VpuTestExecutor> executor;

    int total_tests = 0;
    int passed_tests = 0;

    // 辅助函数：运行单个测试并打印结果
    bool runTest(const VpuTestCase& test_case)
    {
        total_tests++;
        std::cout << "Running test: " << test_case.test_name << "... ";
        bool result = executor->runTest(test_case);
        if (result)
        {
            passed_tests++;
            std::cout << "PASSED" << std::endl;
        }
        else
        {
            std::cout << "FAILED\n"
                      << "Error: " << executor->getLastResult().error_message << std::endl;
        }
        return result;
    }

    // Vector-Vector操作测试
    bool runVVTests()
    {
        std::cout << "\nRunning Vector-Vector Tests:" << std::endl;
        bool all_passed = true;

        // 运行整数测试
        std::cout << "\n=== Integer Operations ===" << std::endl;
        for (const auto& config : kIntegerVVTests)
        {
            std::cout << "\nTesting " << config.group_name << " - " << config.op_name << std::endl;
            std::cout << "Precision: out=" << (int)config.prec_out
                      << ", in1=" << (int)config.prec_in1 << ", in2=" << (int)config.prec_in2
                      << std::endl;
            generator->reset();

            auto test_case = generator->generateVVTest(
                config.op, config.prec_in1, config.prec_in2, config.prec_out);
            all_passed &= runTest(test_case);

            wait(1, sc_core::SC_NS);
        }

        // 运行浮点测试
        std::cout << "\n=== Floating-point Operations ===" << std::endl;
        for (const auto& config : kFloatVVTests)
        {
            std::cout << "\nTesting " << config.group_name << " - " << config.op_name << std::endl;
            std::cout << "Precision: out=" << (int)config.prec_out
                      << ", in1=" << (int)config.prec_in1 << ", in2=" << (int)config.prec_in2
                      << std::endl;
            generator->reset();
            auto test_case = generator->generateVVTest(
                config.op, config.prec_in1, config.prec_in2, config.prec_out);
            all_passed &= runTest(test_case);

            wait(1, sc_core::SC_NS);
        }

        return all_passed;
    }

    // Vector-Scalar操作测试
    bool runVSTests()
    {
        std::cout << "\nRunning Vector-Scalar Tests:" << std::endl;
        bool all_passed = true;

        // 运行整数测试
        std::cout << "\n=== Integer Operations ===" << std::endl;
        bool is_format2 = false;
        for (const auto& config : kIntegerVVTests)
        {
            std::cout << "\nTesting " << config.group_name << " - " << config.op_name << std::endl;
            std::cout << "Precision: out=" << (int)config.prec_out
                      << ", in1=" << (int)config.prec_in1 << ", in2=" << (int)config.prec_in2
                      << std::endl;
            generator->reset();
            if (config.prec_in1 == config.prec_out)
            {
                is_format2 = false;
            }
            else if (config.prec_in1 == config.prec_out * 2 ||
                     config.prec_in1 * 2 == config.prec_out)
            {
                is_format2 = true;
            }
            uint32_t val_in2 =
                generator->vpe_generator.gen_scalar_data(config.prec_in2, false, is_format2);
            auto test_case = generator->generateVSTest(
                config.op, config.prec_in1, config.prec_in2, config.prec_out, val_in2);
            all_passed &= runTest(test_case);

            wait(1, sc_core::SC_NS);
        }

        // 运行浮点测试
        std::cout << "\n=== Floating-point Operations ===" << std::endl;
        for (const auto& config : kFloatVVTests)
        {
            std::cout << "\nTesting " << config.group_name << " - " << config.op_name << std::endl;
            std::cout << "Precision: out=" << (int)config.prec_out
                      << ", in1=" << (int)config.prec_in1 << ", in2=" << (int)config.prec_in2
                      << std::endl;
            generator->reset();
            if (config.prec_in1 == config.prec_out)
            {
                is_format2 = false;
            }
            else if (config.prec_in1 == config.prec_out * 2 ||
                     config.prec_in1 * 2 == config.prec_out)
            {
                is_format2 = true;
            }
            uint32_t val_in2 =
                generator->vpe_generator.gen_scalar_data(config.prec_in2, true, is_format2);
            auto test_case = generator->generateVSTest(
                config.op, config.prec_in1, config.prec_in2, config.prec_out, val_in2);
            all_passed &= runTest(test_case);

            wait(1, sc_core::SC_NS);
        }
        return all_passed;
    }

    // Vector Reduction操作测试
    bool runVtoSTests()
    {
        std::cout << "\nRunning Vector Reduction Tests:" << std::endl;
        bool all_passed = true;

        for (const auto& config : kReductionTests)
        {
            std::cout << "\nTesting " << config.group_name << " - " << config.name << std::endl;
            std::cout << "Precision: out=" << (int)config.prec_out
                      << ", in1=" << (int)config.prec_in << std::endl;
            generator->reset();
            auto test_case =
                generator->generateVtoSTest(config.op, config.prec_in, config.prec_out);
            all_passed &= runTest(test_case);
            wait(0, sc_core::SC_NS);
        }
        return all_passed;
    }

    // Vector Conversion操作测试
    bool runVtoVTests()
    {
        std::cout << "\nRunning Vector Conversion Tests:" << std::endl;
        bool all_passed = true;
        for (const auto& conv : kConversionTests)
        {
            generator->reset();
            std::cout << "\nTesting " << conv.group_name << " - " << conv.name << std::endl;
            auto test_case = generator->generateVtoVTest(conv.width_in, conv.width_out);
            all_passed &= runTest(test_case);
            wait(0, sc_core::SC_NS);
        }
        return all_passed;
    }
};

}  // namespace test
}  // namespace vpu

int sc_main(int argc, char* argv[])
{
    vpu::test::VpuTestRunner runner("runner");
    sc_core::sc_start(1000, sc_core::SC_NS);
    return 0;
}