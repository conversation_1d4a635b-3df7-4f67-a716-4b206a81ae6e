#pragma once

#include <string>
#include <systemc>
#include <tlm>
#include "../../local_mem/local_mem.h"
#include "../vpu.h"
#include "utils/utils.h"
#include "vpu_test_generator.h"
namespace vpu
{
namespace test
{

class VpuTestExecutor : public sc_core::sc_module
{
  public:
    VpuTestExecutor(sc_core::sc_module_name name, VPU& vpu, npu_sc::LocalMemory& mem);

    tlm_utils::simple_initiator_socket<VpuTestExecutor> initiator_socket;
    vpu::test::TestCaseGenerator vpe_generator;
    // 执行单个测试用例
    bool runTest(const VpuTestCase& test_case);

    // 获取最后一次测试的详细结果
    struct TestResult
    {
        bool passed;
        std::string error_message;
        uint32_t actual_scalar;                       // 用于V_S操作
        std::vector<npu_sc::Word256b> actual_output;  // 用于其他操作
    };
    const TestResult& getLastResult() const
    {
        return last_result;
    }

  private:
    VPU& m_vpu;
    npu_sc::LocalMemory& m_mem;
    TestResult last_result;
    sc_core::sc_time m_delay;

    // 存储最后使用的精度和格式信息
    uint8_t m_last_prec_out;
    bool m_last_is_format2;

    // 内部测试步骤
    void prepareMemory(const VpuTestCase& test_case);
    void executeCommand(const instruction::IssueQueueCmd& cmd);
    bool verifyResults(const VpuTestCase& test_case);

    // 辅助函数
    void writeToMemory(uint64_t addr, const std::vector<npu_sc::Word256b>& data);
    std::vector<npu_sc::Word256b> readFromMemory(uint64_t addr, size_t num_words);
    bool compareResults(const std::vector<npu_sc::Word256b>& actual,
                        const std::vector<npu_sc::Word256b>& expected,
                        std::string& error_msg);
};

}  // namespace test
}  // namespace vpu