#include "vpu.h"
#include <cstring>
#include "npu_config.h"

using namespace instruction::opcode;
using namespace dtype;
namespace vpu
{

using Word256b = VPU::Word256b;

VPU::VPU(sc_core::sc_module_name name)
    : sc_core::sc_module(name),
      cfg_socket("cfg_socket"),
      vpu_rd_ack_rx("vpu_rd_ack_rx"),
      vpu_wr_req_tx("vpu_wr_req_tx"),
      m_delay(sc_core::SC_ZERO_TIME)
{
    cfg_socket.register_b_transport(this, &VPU::b_transport_cfg);
    SC_THREAD(thread_cfg);
    SC_THREAD(thread_drv);
}

void VPU::b_transport_cfg(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
{
    m_issue_queue_cmd = *(IssueQueueCmd*)trans.get_data_ptr();
    trans.set_response_status(tlm::TLM_OK_RESPONSE);
    rec_cfg_event.notify();
}

void VPU::thread_cfg()
{
    while (true)
    {
        wait(rec_cfg_event);
        if (m_issue_queue_cmd.funct7 == VP_CFG)
        {
            m_cfg.writeReg(m_issue_queue_cmd.rs1val, m_issue_queue_cmd.rs2val);
        }
        else if (m_issue_queue_cmd.funct7 == VV_V_PRE)
        {
            byte_base_in2 = m_issue_queue_cmd.rs1val;
        }
        else if (m_issue_queue_cmd.funct7 == VV_V_DRV)
        {
            byte_base_out = m_issue_queue_cmd.rs1val;
            byte_base_in1 = m_issue_queue_cmd.rs2val;
            m_funcsel = FunctSel::VV_V;
        }
        else if (m_issue_queue_cmd.funct7 == VS_V_PRE)
        {
            val_in2 = m_issue_queue_cmd.rs1val;
        }
        else if (m_issue_queue_cmd.funct7 == VS_V_DRV)
        {
            byte_base_out = m_issue_queue_cmd.rs1val;
            byte_base_in1 = m_issue_queue_cmd.rs2val;
            m_funcsel = FunctSel::VS_V;
        }
        else if (m_issue_queue_cmd.funct7 == V_S_DRV)
        {
            byte_base_in1 = m_issue_queue_cmd.rs2val;
            m_funcsel = FunctSel::V_S;
        }
        else if (m_issue_queue_cmd.funct7 == V_V_DRV)
        {
            byte_base_out = m_issue_queue_cmd.rs1val;
            byte_base_in1 = m_issue_queue_cmd.rs2val;
            m_funcsel = FunctSel::V_V;
        }
        // 触发驱动事件
        if (m_issue_queue_cmd.funct7 == VV_V_DRV || m_issue_queue_cmd.funct7 == VS_V_DRV ||
            m_issue_queue_cmd.funct7 == V_S_DRV || m_issue_queue_cmd.funct7 == V_V_DRV)
        {
            m_cfg_vpu = m_cfg.getConfig();
            m_width_mode =
                static_cast<WidthMode>(m_cfg_vpu.cfg_wd_sc_out << 2 | m_cfg_vpu.cfg_wd_sc_in1 << 1 |
                                       m_cfg_vpu.cfg_wd_sc_in2);
            m_op_mode = static_cast<OpMode>(m_cfg_vpu.cfg_op);
            m_ref_width = width_code_to_bits(static_cast<WidthCode>(m_cfg_vpu.cfg_wd_ref));
            m_width_in1 = static_cast<uint8_t>(
                bits_to_width_code(pow(2, m_cfg_vpu.cfg_wd_sc_in1) * m_ref_width));
            m_prec_in1 = static_cast<uint8_t>(m_cfg_vpu.cfg_type_in1 << 3 | m_width_in1);
            m_width_in2 = static_cast<uint8_t>(
                bits_to_width_code(pow(2, m_cfg_vpu.cfg_wd_sc_in2) * m_ref_width));
            m_prec_in2 = static_cast<uint8_t>(m_cfg_vpu.cfg_type_in2 << 3 | m_width_in2);
            m_width_out = static_cast<uint8_t>(
                bits_to_width_code(pow(2, m_cfg_vpu.cfg_wd_sc_out) * m_ref_width));
            m_prec_out = static_cast<uint8_t>(m_cfg_vpu.cfg_type_out << 3 | m_width_out);
            // cfg_size_dim0b_size
            if (m_width_mode == WidthMode::SINGLE_WIDTH)
            {
                cfg_size_dim0b_wide = m_cfg_vpu.cfg_size_dim0b_ref;
            }
            else if (static_cast<WidthCode>(m_cfg_vpu.cfg_wd_ref) == WidthCode::W4)
            {
                cfg_size_dim0b_wide = m_cfg_vpu.cfg_size_dim0b_ref * 2 -
                                      (m_cfg_vpu.cfg_rem_dim0_ref <= NPUConfig::LMEM_WD / 4 / 2);
            }
            else if (static_cast<WidthCode>(m_cfg_vpu.cfg_wd_ref) == WidthCode::W8)
            {
                cfg_size_dim0b_wide = m_cfg_vpu.cfg_size_dim0b_ref * 2 -
                                      (m_cfg_vpu.cfg_rem_dim0_ref <= NPUConfig::LMEM_WD / 8 / 2);
            }
            else if (static_cast<WidthCode>(m_cfg_vpu.cfg_wd_ref) == WidthCode::W16)
            {
                cfg_size_dim0b_wide = m_cfg_vpu.cfg_size_dim0b_ref * 2 -
                                      (m_cfg_vpu.cfg_rem_dim0_ref <= NPUConfig::LMEM_WD / 16 / 2);
            }
            else if (static_cast<WidthCode>(m_cfg_vpu.cfg_wd_ref) == WidthCode::W32)
            {
                cfg_size_dim0b_wide = m_cfg_vpu.cfg_size_dim0b_ref * 2 -
                                      (m_cfg_vpu.cfg_rem_dim0_ref <= NPUConfig::LMEM_WD / 32 / 2);
            }
            m_cfg_vpe.prec_in1 = m_prec_in1;
            m_cfg_vpe.prec_in2 = m_prec_in2;
            m_cfg_vpe.prec_out = m_prec_out;
            m_cfg_vpe.op_mode = m_op_mode;
            m_cfg_vpe.width_mode = m_width_mode;
            m_cfg_vpe.funcsel = m_funcsel;
            for (auto& vpe : vpe_arr)
            {
                vpe.configure(m_cfg_vpe);
            }
            drv_event.notify();
        }
    }
}

void VPU::thread_drv()
{
    while (true)
    {
        wait(drv_event);
        for (uint32_t o_cnt_loop = 0; o_cnt_loop < cfg_size_dim0b_wide; o_cnt_loop++)
        {
            auto req_info = VPURdReqTx::generate_req(
                o_cnt_loop, byte_base_in1, byte_base_in2, m_width_mode, m_funcsel);

            m_data_out = vpu_rd_ack_rx.process_read(req_info.ch1_rd_req_addr,
                                                    req_info.ch2_rd_req_addr,
                                                    req_info.ch2_used,
                                                    req_info.mask_ch1_rd,
                                                    req_info.mask_ch2_rd,
                                                    o_cnt_loop,
                                                    m_width_mode,
                                                    m_width_in1,
                                                    m_width_in2);
            if (m_data_out.data_valid)
            {
                switch (m_funcsel)
                {
                    case FunctSel::VV_V:
                    {
                        m_result = process_vector_vector(m_data_out.ch1_data, m_data_out.ch2_data);
                        vpu_wr_req_tx.process_write(byte_base_out,
                                                    o_cnt_loop,
                                                    m_width_out,
                                                    m_width_mode,
                                                    cfg_size_dim0b_wide,
                                                    m_result);
                        break;
                    }
                    case FunctSel::VS_V:
                    {
                        m_result = process_vector_scalar(m_data_out.ch1_data, val_in2);
                        vpu_wr_req_tx.process_write(byte_base_out,
                                                    o_cnt_loop,
                                                    m_width_out,
                                                    m_width_mode,
                                                    cfg_size_dim0b_wide,
                                                    m_result);
                        break;
                    }
                    case FunctSel::V_S:
                    {
                        m_result_scalar = process_vector_reduction(m_data_out.ch1_data);
                        break;
                    }
                    case FunctSel::V_V:
                    {
                        m_result = process_vector(m_data_out.ch1_data);
                        vpu_wr_req_tx.process_write(byte_base_out,
                                                    o_cnt_loop,
                                                    m_width_out,
                                                    m_width_mode,
                                                    cfg_size_dim0b_wide,
                                                    m_result);
                        break;
                    }
                }
            }
        }
    }
}

Word256b VPU::process_vector_vector(const Word256b& data1, const Word256b& data2)
{
    Word256b result{};
    // Split 256-bit data into 8 32-bit segments for each VPE
    for (size_t i = 0; i < VPE_NUM; ++i)
    {
        uint32_t data1_segment, data2_segment;
        std::memcpy(&data1_segment, &data1[i * 4], 4);
        std::memcpy(&data2_segment, &data2[i * 4], 4);

        // Process data through VPE and store result
        uint32_t vpe_result = vpe_arr[i].process(data1_segment, data2_segment);
        std::memcpy(&result[i * 4], &vpe_result, 4);
    }
    return result;
}

Word256b VPU::process_vector_scalar(const Word256b& data1, uint32_t scalar_val)
{
    Word256b result{};
    // Process vector-scalar operation
    for (size_t i = 0; i < VPE_NUM; ++i)
    {
        uint32_t data1_segment;
        std::memcpy(&data1_segment, &data1[i * 4], 4);

        // Process data through VPE and store result
        uint32_t vpe_result = vpe_arr[i].process(data1_segment, scalar_val);
        std::memcpy(&result[i * 4], &vpe_result, 4);
    }
    return result;
}

uint32_t VPU::process_vector_reduction(const Word256b& data1)
{
    for (auto& vpe : vpe_arr)
    {
        m_cfg_vpe.funcsel = FunctSel::VV_V;
        vpe.configure(m_cfg_vpe);
    }
    // First level: 4 VPEs process pairs of inputs
    uint32_t level1_results[4];
    for (size_t i = 0; i < 4; ++i)
    {
        uint32_t data1_segment1, data1_segment2;
        std::memcpy(&data1_segment1, &data1[i * 8], 4);
        std::memcpy(&data1_segment2, &data1[i * 8 + 4], 4);

        level1_results[i] = vpe_arr[i].process(data1_segment1, data1_segment2);
    }

    // Second level: 2 VPEs process results from first level
    for (auto& vpe : vpe_arr)
    {
        // 第二层开始的输入是第一层的输出，也是最终输出位宽, 所以prec_in1，in2 = prec_out,
        // width_mode = single_width
        m_cfg_vpe.prec_in1 = m_prec_out;
        m_cfg_vpe.prec_in2 = m_prec_out;
        m_cfg_vpe.width_mode = WidthMode::SINGLE_WIDTH;
        vpe.configure(m_cfg_vpe);
    }
    uint32_t level2_results[2];
    for (size_t i = 0; i < 2; ++i)
    {
        level2_results[i] =
            vpe_arr[4 + i].process(level1_results[i * 2], level1_results[i * 2 + 1]);
    }

    // Third level: 1 VPE processes results from second level
    uint32_t level3_result = vpe_arr[6].process(level2_results[0], level2_results[1]);

    // Final level: Last VPE performs vector-scalar operation
    m_cfg_vpe.funcsel = FunctSel::V_S;
    m_cfg_vpe.width_mode = WidthMode::SINGLE_WIDTH;
    vpe_arr[7].configure(m_cfg_vpe);
    return vpe_arr[7].process(level3_result, 0);
}

Word256b VPU::process_vector(const Word256b& data1)
{
    Word256b result;
    for (size_t i = 0; i < VPE_NUM; ++i)
    {
        uint32_t data1_segment;
        std::memcpy(&data1_segment, &data1[i * 4], 4);

        // Process data through VPE and store result
        uint32_t vpe_result = vpe_arr[i].process(data1_segment, 0);
        std::memcpy(&result[i * 4], &vpe_result, 4);
    }
    return result;
}

uint32_t VPU::get_result_scalar() const
{
    return m_result_scalar;
}

}  // namespace vpu