includes("vpe/xmake.lua")
includes("../local_mem/xmake.lua")


target("vpu")
    set_kind("static")
    add_files("*.cpp")
    add_includedirs(".",{public=true})
    add_deps("vpe")


target("test_func")
    set_kind("binary")
    add_files("test/test_data_padding.cpp")
    add_deps("vpu")
    add_packages("gtest")



target("test_vpu")
    set_kind("binary")
    add_files("test/*.cpp|test_data_padding.cpp")
    add_files("vpe/test/test_case_generator.cpp")
    add_headerfiles("vpe/test/test_case_generator.h")
    add_includedirs("./test")
    add_deps("vpu")
    add_deps("local_mem")
    add_packages("magic_enum")
    add_tests("test_vpu", {group = "vpu"})
    -- Add custom rule for coverage analysis
    if is_mode("coverage") then
        after_run(function (target)
        -- Create a shell script for coverage analysis
        local script = [[
    #!/bin/bash

    # Clean coverage data
    lcov --directory . --zerocounters

    # Collect coverage data
    lcov --directory build/.objs/vpu --capture --output-file vpu.info
    lcov --directory build/.objs/test_vpu --capture --output-file test_vpu.info
    lcov --directory build/.objs/vpe --capture --output-file vpe.info

    # Merge coverage files
    lcov --add-tracefile vpu.info --add-tracefile vpe.info --add-tracefile test_vpu.info --output-file coverage.info

    # Filter out system files and other unwanted coverage data
    lcov --remove coverage.info '/usr/*' '*/systemc/*' '*/.xmake/*' --output-file coverage_filtered.info

    # Generate HTML report
    genhtml coverage_filtered.info --output-directory coverage_report

    # Clean up intermediate files
    rm -f vpu.info test_vpu.info vpe.info
    ]]
            
            -- Write the script to a file
            io.writefile("run_coverage.sh", script)
            os.exec("chmod +x run_coverage.sh")
            
            -- Print usage instructions
            print("Coverage script has been created. To run coverage analysis:")
            print("1. Run: ./run_coverage.sh")
            print("2. View the results in the coverage_report directory")
            os.exec("./run_coverage.sh")
        end)

    end

