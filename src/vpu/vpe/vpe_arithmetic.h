#pragma once
#include <cstdint>
#include "dcim_com.h"
#include "npu_config.h"
#include "utils/systemc_logger.h"
#include "utils/utils.h"
namespace vpu
{

// 类型判断函数
inline bool is_fp_type(uint8_t dtype)
{
    return (dtype == dtype::FP16 || dtype == dtype::FP32 || dtype == dtype::BF16);
}

// 类型转换函数
inline uint8_t convert_dtype_to_datafmt(uint8_t dtype)
{
    switch (dtype)
    {
        case dtype::FP16:
            return FP16;
        case dtype::FP32:
            return FP32;
        case dtype::BF16:
            return BF16;
        default:
            error_sc("Unsupported data type conversion,just use float type");
    }
}

// 算术运算辅助函数声明
float convert_uint32_to_float(uint32_t val, uint8_t prec);
uint32_t handle_add(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_sub(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_min(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_max(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_multiplication(uint32_t a,
                               uint8_t prec_a,
                               uint32_t b,
                               uint8_t prec_b,
                               uint8_t prec_out);

// Compare operations
uint32_t handle_eq(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_ne(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_gt(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_ge(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_lt(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_le(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);

// Shift operations
uint32_t handle_shl(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_shr_floor(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_shr_ceil(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_shr_round(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);

// Bitwise operations
uint32_t handle_and(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_or(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);
uint32_t handle_xor(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out);

// Reduction operations
uint32_t handle_reduce_sum(const std::vector<uint32_t>& elements,
                           uint8_t prec_in,
                           uint8_t prec_out);
uint32_t handle_reduce_min(const std::vector<uint32_t>& elements,
                           uint8_t prec_in,
                           uint8_t prec_out);
uint32_t handle_reduce_max(const std::vector<uint32_t>& elements,
                           uint8_t prec_in,
                           uint8_t prec_out);
uint32_t handle_reduce_and(const std::vector<uint32_t>& elements,
                           uint8_t prec_in,
                           uint8_t prec_out);
uint32_t handle_reduce_or(const std::vector<uint32_t>& elements, uint8_t prec_in, uint8_t prec_out);
uint32_t handle_reduce_xor(const std::vector<uint32_t>& elements,
                           uint8_t prec_in,
                           uint8_t prec_out);

// Type conversion operations
uint32_t handle_convert_fp_to_int(uint32_t data, uint8_t prec_in, uint8_t prec_out);
uint32_t handle_convert_int_to_fp(uint32_t data, uint8_t prec_in, uint8_t prec_out);
uint32_t handle_convert_fp_to_fp(uint32_t data, uint8_t prec_in, uint8_t prec_out);

}  // namespace vpu