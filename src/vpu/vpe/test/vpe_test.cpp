#include "../vpe.h"
#include <gtest/gtest.h>
#include <cstdint>
#include <magic_enum.hpp>
#include <vector>
#include "../../../../include/npu_config.h"
#include "../vpe_arithmetic.h"
#include "../vpe_config.h"
#include "test_case_generator.h"
#include "utils/test_utils.h"
using namespace dtype;

namespace vpu
{
namespace test
{

class VpeTest : public ::testing::TestWithParam<VpeTestParam>
{
  protected:
    Vpe vpe;
};

// 试用例生成函数
std::vector<VpeTestParam> generateTestCases()
{
    TestCaseGenerator generator;
    std::vector<VpeTestParam> test_cases, cases;

    // 生成Single-width Integer Operation测试用例
    // ADD/SUB/RSUB
    for (auto prec : {dtype::INT4, dtype::INT8, dtype::INT16, dtype::INT32})
    {
        for (auto op : {OpMode::ADD, OpMode::SUB, OpMode::RSUB})
        {
            cases = generator.generateTestCases_VV(op, WidthMode::SINGLE_WIDTH, prec, prec, prec);
            test_cases.insert(test_cases.end(), cases.begin(), cases.end());
        }
    }

    // MAX/MIN/Compare
    for (auto prec : {dtype::INT4, dtype::INT8})
    {
        for (auto op : {OpMode::MAX,
                        OpMode::MIN,
                        OpMode::EQ,
                        OpMode::NE,
                        OpMode::GT,
                        OpMode::GE,
                        OpMode::LT,
                        OpMode::LE})
        {
            cases = generator.generateTestCases_VV(op, WidthMode::SINGLE_WIDTH, prec, prec, prec);
            test_cases.insert(test_cases.end(), cases.begin(), cases.end());
        }
    }

    // Shift operations (only for integer types)
    for (auto prec : {dtype::INT4, dtype::INT8})
    {
        for (auto op : {OpMode::SHL, OpMode::SHR_FLOOR, OpMode::SHR_CEIL, OpMode::SHR_ROUND})
        {
            cases = generator.generateTestCases_VV(op, WidthMode::SINGLE_WIDTH, prec, prec, prec);
            test_cases.insert(test_cases.end(), cases.begin(), cases.end());
        }
    }

    // Logical operations (only for integer types)
    for (auto prec : {dtype::INT4, dtype::INT8})
    {
        for (auto op : {OpMode::AND, OpMode::OR, OpMode::XOR})
        {
            cases = generator.generateTestCases_VV(op, WidthMode::SINGLE_WIDTH, prec, prec, prec);
            test_cases.insert(test_cases.end(), cases.begin(), cases.end());
        }
    }

    // Widening Integer Operation测试用例
    // ADD/SUB/RSUB
    {
        for (auto op : {OpMode::ADD, OpMode::SUB, OpMode::RSUB})
        {
            cases = generator.generateTestCases_VV(
                op, WidthMode::WIDENING, dtype::INT8, dtype::INT4, dtype::INT4);
            test_cases.insert(test_cases.end(), cases.begin(), cases.end());
            cases = generator.generateTestCases_VV(
                op, WidthMode::WIDENING, dtype::INT16, dtype::INT8, dtype::INT8);
            test_cases.insert(test_cases.end(), cases.begin(), cases.end());
            cases = generator.generateTestCases_VV(
                op, WidthMode::WIDENING, dtype::INT32, dtype::INT16, dtype::INT16);
            test_cases.insert(test_cases.end(), cases.begin(), cases.end());
        }
    }

    // MUL
    {
        cases = generator.generateTestCases_VV(
            OpMode::MUL, WidthMode::WIDENING, dtype::INT8, dtype::INT4, dtype::INT4);
        test_cases.insert(test_cases.end(), cases.begin(), cases.end());

        cases = generator.generateTestCases_VV(
            OpMode::MUL, WidthMode::WIDENING, dtype::INT16, dtype::INT8, dtype::INT8);
        test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    }

    // 浮点运算测试用例
    // Single-width Floating-point Operation
    {
        for (auto prec : {dtype::FP16, dtype::BF16})
        {
            for (auto op : {OpMode::ADD,
                            OpMode::SUB,
                            OpMode::RSUB,
                            OpMode::MUL,
                            OpMode::MAX,
                            OpMode::MIN,
                            OpMode::EQ,
                            OpMode::NE,
                            OpMode::GT,
                            OpMode::GE,
                            OpMode::LT,
                            OpMode::LE})
            {
                cases =
                    generator.generateTestCases_VV(op, WidthMode::SINGLE_WIDTH, prec, prec, prec);
                test_cases.insert(test_cases.end(), cases.begin(), cases.end());
            }
        }
        for (auto op : {OpMode::ADD, OpMode::SUB, OpMode::RSUB})
        {
            cases = generator.generateTestCases_VV(
                op, WidthMode::SINGLE_WIDTH, dtype::FP32, dtype::FP32, dtype::FP32);
            test_cases.insert(test_cases.end(), cases.begin(), cases.end());
        }
    }

    // Widening Floating-point Operation
    for (auto op : {OpMode::ADD, OpMode::MUL})
    {
        // BF16 -> FP32
        cases = generator.generateTestCases_VV(
            op, WidthMode::WIDENING, dtype::FP32, dtype::BF16, dtype::BF16);
        test_cases.insert(test_cases.end(), cases.begin(), cases.end());
        // FP16 -> FP32
        cases = generator.generateTestCases_VV(
            op, WidthMode::WIDENING, dtype::FP32, dtype::FP16, dtype::FP16);
        test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    }

    // V_S
    //  intetger reduce
    for (auto op : {OpMode::ADD, OpMode::MIN, OpMode::MAX, OpMode::AND, OpMode::OR, OpMode::XOR})
    {
        for (auto prec : {dtype::INT4, dtype::INT8, dtype::INT16, dtype::INT32})
        {
            cases =
                generator.generateTestCases_V_S(op, WidthMode::SINGLE_WIDTH, dtype::INT32, prec);
            test_cases.insert(test_cases.end(), cases.begin(), cases.end());
        }
    }
    // float reduce
    for (auto op : {OpMode::ADD, OpMode::MIN, OpMode::MAX})
    {
        for (auto prec : {dtype::FP16, dtype::BF16})
        {
            cases = generator.generateTestCases_V_S(op, WidthMode::SINGLE_WIDTH, dtype::FP32, prec);
            test_cases.insert(test_cases.end(), cases.begin(), cases.end());
        }
    }
    // V_V

    // V_V Conversion Tests
    // Single-width Conversion
    // FP-INT passed
    cases = generator.generateTestCases_V_V(WidthMode::SINGLE_WIDTH, dtype::INT16, dtype::BF16);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    cases = generator.generateTestCases_V_V(WidthMode::SINGLE_WIDTH, dtype::INT16, dtype::FP16);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    cases = generator.generateTestCases_V_V(WidthMode::SINGLE_WIDTH, dtype::INT32, dtype::FP32);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());

    // INT-FP pass
    cases = generator.generateTestCases_V_V(WidthMode::SINGLE_WIDTH, dtype::BF16, dtype::INT16);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    cases = generator.generateTestCases_V_V(WidthMode::SINGLE_WIDTH, dtype::FP16, dtype::INT16);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    cases = generator.generateTestCases_V_V(WidthMode::SINGLE_WIDTH, dtype::FP32, dtype::INT32);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());

    // FP-FP pass
    cases = generator.generateTestCases_V_V(WidthMode::SINGLE_WIDTH, dtype::FP16, dtype::BF16);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    cases = generator.generateTestCases_V_V(WidthMode::SINGLE_WIDTH, dtype::BF16, dtype::FP16);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());

    // Widening Conversion
    // FP-INT passed
    cases = generator.generateTestCases_V_V(WidthMode::WIDENING, dtype::INT32, dtype::BF16);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    cases = generator.generateTestCases_V_V(WidthMode::WIDENING, dtype::INT32, dtype::FP16);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());

    // INT-FP  test pass
    cases = generator.generateTestCases_V_V(WidthMode::WIDENING, dtype::BF16, dtype::INT8);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    cases = generator.generateTestCases_V_V(WidthMode::WIDENING, dtype::FP16, dtype::INT8);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    cases = generator.generateTestCases_V_V(WidthMode::WIDENING, dtype::FP32, dtype::INT16);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());

    // FP-FP pass
    cases = generator.generateTestCases_V_V(WidthMode::WIDENING, dtype::FP32, dtype::BF16);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    cases = generator.generateTestCases_V_V(WidthMode::WIDENING, dtype::FP32, dtype::FP16);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());

    // Narrowing Conversion
    // FP-INT pass
    cases = generator.generateTestCases_V_V(WidthMode::NARROWING, dtype::INT8, dtype::BF16);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    cases = generator.generateTestCases_V_V(WidthMode::NARROWING, dtype::INT8, dtype::FP16);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    cases = generator.generateTestCases_V_V(WidthMode::NARROWING, dtype::INT16, dtype::FP32);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());

    // INT-FP pass
    cases = generator.generateTestCases_V_V(WidthMode::NARROWING, dtype::BF16, dtype::INT32);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    cases = generator.generateTestCases_V_V(WidthMode::NARROWING, dtype::FP16, dtype::INT32);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());

    // FP-FP  pass
    cases = generator.generateTestCases_V_V(WidthMode::NARROWING, dtype::BF16, dtype::FP32);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());
    cases = generator.generateTestCases_V_V(WidthMode::NARROWING, dtype::FP16, dtype::FP32);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());

    // V_S_V
    cases = generator.generateTestCases_VS(
        OpMode::ADD, WidthMode::SINGLE_WIDTH, dtype::INT8, dtype::INT8, dtype::INT8, 0);
    test_cases.insert(test_cases.end(), cases.begin(), cases.end());

    return test_cases;
}

// 参数化测试
TEST_P(VpeTest, OperationTest)
{
    const auto& param = GetParam();

    // 配置VPE
    vpe.configure(param.config);

    uint32_t packed_input1, packed_input2 = 0, result;

    // 打包input1
    if (vpu::is_fp_type(param.config.prec_in1))
    {
        packed_input1 =
            TestUtils::packFloat(param.input1, param.config.prec_in1, param.is_format2_in1);
    }
    else
    {
        packed_input1 =
            TestUtils::packData(param.input1, param.config.prec_in1, param.is_format2_in1);
    }

    // 只在需要input2的情况下打包input2
    if (param.config.funcsel == FunctSel::VV_V || param.config.funcsel == FunctSel::VS_V)
    {
        if (vpu::is_fp_type(param.config.prec_in2))
        {
            packed_input2 =
                TestUtils::packFloat(param.input2, param.config.prec_in2, param.is_format2_in2);
        }
        else
        {
            packed_input2 =
                TestUtils::packData(param.input2, param.config.prec_in2, param.is_format2_in2);
        }
    }

    // 执行操作
    result = vpe.process(packed_input1, packed_input2);

    if (param.is_float)
    {
        // 浮点数验证
        std::vector<float> unpacked_float =
            TestUtils::unpackFloat(result, param.config.prec_out, param.is_format2_out);
        ASSERT_EQ(unpacked_float.size(), param.float_expected.size())
            << "Result size mismatch for test: " << param.test_name;

        for (size_t i = 0; i < unpacked_float.size(); ++i)
        {
            EXPECT_TRUE(TestUtils::isFloatEqual(
                unpacked_float[i], param.float_expected[i], param.tolerance))
                << "Mismatch at index " << i << " for test: " << param.test_name
                << "\nExpected: " << param.float_expected[i] << "\nActual: " << unpacked_float[i];
        }

        // Debug输出
        if (::testing::Test::HasFailure())
        {
            std::cout << "\n========== Test Case Details ==========\n";
            std::cout << "Test name: " << param.test_name << "\n";
            std::cout << "Config:\n";
            std::cout << "  Operation: " << magic_enum::enum_name(param.config.op_mode) << "\n";
            std::cout << "  Width Mode: " << magic_enum::enum_name(param.config.width_mode) << "\n";
            std::cout << "  Function Select: " << magic_enum::enum_name(param.config.funcsel)
                      << "\n";
            std::cout << "  Precision (in1/in2/out): " << getPrecisionString(param.config.prec_in1)
                      << "/" << getPrecisionString(param.config.prec_in2) << "/"
                      << getPrecisionString(param.config.prec_out) << "\n";
            std::cout << "Format2 (in1/in2/out): " << (param.is_format2_in1 ? "true" : "false")
                      << "/" << (param.is_format2_in2 ? "true" : "false") << "/"
                      << (param.is_format2_out ? "true" : "false") << "\n";

            std::cout << "\nRaw Inputs:\n";
            std::cout << "Input1: ";
            for (const auto& val : param.input1)
                std::cout << val << " ";
            if (param.config.funcsel == FunctSel::VV_V || param.config.funcsel == FunctSel::VS_V)
            {
                std::cout << "\nInput2: ";
                for (const auto& val : param.input2)
                    std::cout << val << " ";
            }
            std::cout << "\n";

            std::cout << "\nPacked Data (hex):\n";
            std::cout << "Packed input1: 0x" << std::hex << packed_input1 << "\n";
            if (param.config.funcsel == FunctSel::VV_V || param.config.funcsel == FunctSel::VS_V)
            {
                std::cout << "Packed input2: 0x" << std::hex << packed_input2 << "\n";
            }
            std::cout << "Result: 0x" << std::hex << result << std::dec << "\n";

            std::cout << "\nFloat Comparison (tolerance=" << param.tolerance << "):\n";
            std::vector<float> unpacked_float =
                TestUtils::unpackFloat(result, param.config.prec_out, param.is_format2_out);
            for (size_t i = 0; i < unpacked_float.size(); ++i)
            {
                std::cout << "Index " << i << ": Expected=" << param.float_expected[i]
                          << " Actual=" << unpacked_float[i]
                          << " Diff=" << std::abs(unpacked_float[i] - param.float_expected[i])
                          << "\n";
            }
            std::cout << "====================================\n";
        }
    }
    else
    {
        // 整数验证
        std::vector<int32_t> unpacked =
            TestUtils::unpackData(result, param.config.prec_out, param.is_format2_out);

        ASSERT_EQ(unpacked.size(), param.expected.size())
            << "Result size mismatch for test: " << param.test_name;
        for (size_t i = 0; i < unpacked.size(); ++i)
        {
            EXPECT_EQ(unpacked[i], param.expected[i])
                << "Mismatch at index " << i << " for test: " << param.test_name;
        }

        // Debug输出
        if (::testing::Test::HasFailure())
        {
            std::cout << "\n========== Test Case Details ==========\n";
            std::cout << "Test name: " << param.test_name << "\n";
            std::cout << "Config:\n";
            std::cout << "  Operation: " << magic_enum::enum_name(param.config.op_mode) << "\n";
            std::cout << "  Width Mode: " << magic_enum::enum_name(param.config.width_mode) << "\n";
            std::cout << "  Function Select: " << magic_enum::enum_name(param.config.funcsel)
                      << "\n";
            std::cout << "  Precision (in1/in2/out): " << getPrecisionString(param.config.prec_in1)
                      << "/" << getPrecisionString(param.config.prec_in2) << "/"
                      << getPrecisionString(param.config.prec_out) << "\n";
            std::cout << "Format2 (in1/in2/out): " << (param.is_format2_in1 ? "true" : "false")
                      << "/" << (param.is_format2_in2 ? "true" : "false") << "/"
                      << (param.is_format2_out ? "true" : "false") << "\n";

            std::cout << "\nRaw Inputs:\n";
            std::cout << "Input1: ";
            for (const auto& val : param.input1)
                std::cout << val << " ";
            if (param.config.funcsel == FunctSel::VV_V || param.config.funcsel == FunctSel::VS_V)
            {
                std::cout << "\nInput2: ";
                for (const auto& val : param.input2)
                    std::cout << val << " ";
            }
            std::cout << "\n";

            std::cout << "\nPacked Data (hex):\n";
            std::cout << "Packed input1: 0x" << std::hex << packed_input1 << "\n";
            if (param.config.funcsel == FunctSel::VV_V || param.config.funcsel == FunctSel::VS_V)
            {
                std::cout << "Packed input2: 0x" << std::hex << packed_input2 << "\n";
            }
            std::cout << "Result: 0x" << std::hex << result << std::dec << "\n";

            std::cout << "\nInteger Comparison:\n";
            std::vector<int32_t> unpacked =
                TestUtils::unpackData(result, param.config.prec_out, param.is_format2_out);
            for (size_t i = 0; i < unpacked.size(); ++i)
            {
                std::cout << "Index " << i << ": Expected=" << param.expected[i]
                          << " Actual=" << unpacked[i] << "\n";
            }
            std::cout << "====================================\n";
        }
    }
}

// 实例化参数化测试
INSTANTIATE_TEST_SUITE_P(VpeTests,
                         VpeTest,
                         ::testing::ValuesIn(generateTestCases()),
                         [](const testing::TestParamInfo<VpeTestParam>& info)
                         { return info.param.test_name; });

}  // namespace test
}  // namespace vpu

int main(int argc, char* argv[])
{
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}