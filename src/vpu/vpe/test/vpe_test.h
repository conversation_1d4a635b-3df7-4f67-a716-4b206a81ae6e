#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include "../vpe_config.h"
#include "utils/utils.h"
namespace vpu
{
namespace test
{

// 测试参数结构体
struct VpeTestParam
{
    std::string test_name;              // 测试名称
    VpeConfig config;                   // VPE配置
    std::vector<int32_t> input1;        // 输入数据1
    std::vector<int32_t> input2;        // 输入数据2
    std::vector<int32_t> expected;      // 期望输出
    bool is_format2_in1;                // 输入1是否使用Format2
    bool is_format2_in2;                // 输入2是否使用Format2
    bool is_format2_out;                // 输出是否使用Format2
    bool is_float;                      // 是否是浮点运算
    float tolerance;                    // 浮点比较的误差容限
    std::vector<float> float_expected;  // 浮点期望输出
};

}  // namespace test
}  // namespace vpu