#include "test_case_generator.h"
#include <algorithm>
#include <cmath>
#include <cstdint>
#include <magic_enum.hpp>
#include "../vpe_arithmetic.h"
#include "npu_config.h"

using namespace dtype;
namespace vpu
{
namespace test
{

std::vector<VpeTestParam> TestCaseGenerator::generateTestCases(OpMode op_mode,
                                                               WidthMode width_mode,
                                                               FunctSel funcsel,
                                                               uint8_t prec_out,
                                                               uint8_t prec_in1,
                                                               uint8_t prec_in2,
                                                               uint32_t val_in2)
{
    switch (funcsel)
    {
        case FunctSel::VV_V:
            return generateTestCases_VV(op_mode, width_mode, prec_out, prec_in1, prec_in2);
        case FunctSel::VS_V:
            return generateTestCases_VS(op_mode, width_mode, prec_out, prec_in1, prec_in2, val_in2);
        case FunctSel::V_S:
            return generateTestCases_V_S(op_mode, width_mode, prec_out, prec_in1);
        case FunctSel::V_V:
            return generateTestCases_V_V(width_mode, prec_out, prec_in1);
    }
}

uint32_t TestCaseGenerator::gen_scalar_data(uint8_t prec_in, bool is_float, bool is_format2)
{
    // 根据is_float选择生成浮点数据还是整数数据
    if (is_float)
    {
        // 生成浮点数据
        std::vector<float> float_data = generateFloatData(prec_in, is_format2);
        // 转换为内部表示
        std::vector<int32_t> int_data(float_data.size());
        for (size_t i = 0; i < float_data.size(); ++i)
        {
            int_data[i] = glb_f2mh_conv(float_data[i], prec_in);
        }
        // 打包数据
        return TestUtils::packData(int_data, prec_in, is_format2);
    }
    else
    {
        // 生成整数数据
        std::vector<int32_t> int_data = generateIntData(prec_in, is_format2);
        // 打包数据
        return TestUtils::packData(int_data, prec_in, is_format2);
    }
}

std::vector<VpeTestParam> TestCaseGenerator::generateTestCases_VV(OpMode op_mode,
                                                                  WidthMode width_mode,
                                                                  uint8_t prec_out,
                                                                  uint8_t prec_in1,
                                                                  uint8_t prec_in2)
{
    static int test_case_id = 0;
    std::vector<VpeTestParam> test_cases;
    VpeTestParam param;

    // 设置基本配置
    param.config.op_mode = op_mode;
    param.config.width_mode = width_mode;
    param.config.prec_out = prec_out;
    param.config.prec_in1 = prec_in1;
    param.config.prec_in2 = prec_in2;
    param.config.funcsel = FunctSel::VV_V;  // 默认使用向量-向量运算

    // 设置Format2标志
    param.is_format2_in1 = false;
    param.is_format2_in2 = false;
    param.is_format2_out = false;

    switch (param.config.width_mode)
    {
        case WidthMode::SINGLE_WIDTH:
            break;
        case WidthMode::WIDENING:
            // Input: FORMAT2, Output: FORMAT1
            param.is_format2_in1 = true;
            param.is_format2_in2 = true;
            break;
        case WidthMode::NARROWING:
            // Input1: FORMAT1, Input2&Output: FORMAT2
            param.is_format2_in2 = true;
            param.is_format2_out = true;
            break;
    }
    // 根据精度类型决定是否使用浮点处理
    bool is_float =
        isFloatingPoint(prec_in1) || isFloatingPoint(prec_in2) || isFloatingPoint(prec_out);

    if (is_float)
    {
        // 生成浮点测试数据
        std::vector<float> float_input1 = generateFloatData(prec_in1, param.is_format2_in1);
        std::vector<float> float_input2 = generateFloatData(prec_in2, param.is_format2_in2);
        std::vector<float> float_expected =
            calculateExpected<float>(op_mode, float_input1, float_input2, prec_out);

        // 转换为内部表示
        param.input1.resize(float_input1.size());
        param.input2.resize(float_input2.size());
        param.expected.resize(float_expected.size());
        param.float_expected = float_expected;  // 保存浮点期望值

        // 设置浮点标志和误差容限
        param.is_float = true;
        param.tolerance = (op_mode == OpMode::MUL) ? 2 : 1;

        // 转换浮点数为内部表示

        for (size_t i = 0; i < float_input1.size(); ++i)
        {
            param.input1[i] = glb_f2mh_conv(float_input1[i], prec_in1);
        }
        for (size_t i = 0; i < float_input2.size(); ++i)
        {
            param.input2[i] = glb_f2mh_conv(float_input2[i], prec_in2);
        }
        for (size_t i = 0; i < float_expected.size(); ++i)
        {
            param.expected[i] = glb_f2mh_conv(float_expected[i], prec_out);
        }
    }
    else
    {
        // 生成整数测试数据
        param.input1 = generateIntData(prec_in1, param.is_format2_in1);
        if (param.config.op_mode == OpMode::SHL || param.config.op_mode == OpMode::SHR_FLOOR ||
            param.config.op_mode == OpMode::SHR_CEIL || param.config.op_mode == OpMode::SHR_ROUND)
        {
            param.input2 = generateShiftData(prec_in2, param.is_format2_in2);
        }
        else
        {
            param.input2 = generateIntData(prec_in2, param.is_format2_in2);
        }
        param.expected = calculateExpected<int32_t>(op_mode, param.input1, param.input2, prec_out);
        param.is_float = false;
    }

    // 生成测试名称
    test_case_id++;
    param.test_name =
        std::string(magic_enum::enum_name(width_mode)) + "_" + getPrecisionString(prec_out) + "_" +
        std::string(magic_enum::enum_name(op_mode)) + "_" + std::to_string(test_case_id);

    test_cases.push_back(param);
    return test_cases;
}

std::vector<VpeTestParam> TestCaseGenerator::generateTestCases_VS(OpMode op_mode,
                                                                  WidthMode width_mode,
                                                                  uint8_t prec_out,
                                                                  uint8_t prec_in1,
                                                                  uint8_t prec_in2,
                                                                  uint32_t val_in2)
{
    static int test_case_id = 0;
    std::vector<VpeTestParam> test_cases;
    VpeTestParam param;

    // 设置基本配置
    param.config.op_mode = op_mode;
    param.config.width_mode = width_mode;
    param.config.prec_out = prec_out;
    param.config.prec_in1 = prec_in1;
    param.config.prec_in2 = prec_in2;
    param.config.funcsel = FunctSel::VS_V;  // Vector-Scalar 形式

    // 根据宽度模式设置 Format2 标志
    param.is_format2_in1 = false;
    param.is_format2_in2 = false;
    param.is_format2_out = false;
    switch (param.config.width_mode)
    {
        case WidthMode::SINGLE_WIDTH:
            break;
        case WidthMode::WIDENING:
            param.is_format2_in1 = true;
            param.is_format2_in2 = true;
            break;
        case WidthMode::NARROWING:
            param.is_format2_in2 = true;
            param.is_format2_out = true;
            break;
    }

    // 是否需要浮点处理
    bool is_float =
        isFloatingPoint(prec_in1) || isFloatingPoint(prec_in2) || isFloatingPoint(prec_out);

    if (is_float)
    {
        // 生成浮点输入1向量
        std::vector<float> float_input1 = generateFloatData(prec_in1, param.is_format2_in1);

        // 使用 TestUtils 解析 val_in2
        std::vector<float> unpacked_scalar =
            TestUtils::unpackFloat(val_in2, prec_in2, param.is_format2_in2);

        // 计算期望值
        std::vector<float> float_expected =
            calculateExpected<float>(op_mode, float_input1, unpacked_scalar, prec_out);

        // 初始化向量大小
        param.input1.resize(float_input1.size());
        param.input2.resize(unpacked_scalar.size());
        param.expected.resize(float_expected.size());

        // 保存浮点期望值，设置浮点标记与容差
        param.float_expected = float_expected;
        param.is_float = true;
        param.tolerance = (op_mode == OpMode::MUL) ? 2 : 1;

        // 转换为内部表示
        for (size_t i = 0; i < float_input1.size(); ++i)
        {
            param.input1[i] = glb_f2mh_conv(float_input1[i], prec_in1);
        }
        for (size_t i = 0; i < unpacked_scalar.size(); ++i)
        {
            param.input2[i] = glb_f2mh_conv(unpacked_scalar[i], prec_in2);
        }
        for (size_t i = 0; i < float_expected.size(); ++i)
        {
            param.expected[i] = glb_f2mh_conv(float_expected[i], prec_out);
        }
    }
    else
    {
        // 生成整数输入1向量
        param.input1 = generateIntData(prec_in1, param.is_format2_in1);

        // 使用 TestUtils 解析 val_in2
        std::vector<int32_t> unpacked_scalar =
            TestUtils::unpackData(val_in2, prec_in2, param.is_format2_in2);

        // 若运算模式是移位，则需要确保 shift 量符号位正确
        if (op_mode == OpMode::SHL || op_mode == OpMode::SHR_FLOOR || op_mode == OpMode::SHR_CEIL ||
            op_mode == OpMode::SHR_ROUND)
        {
            for (auto& val : unpacked_scalar)
            {
                val = val & 0x1F;  // 只使用低5位
            }
        }

        // 将 unpacked_scalar 转换为 param.input2
        param.input2 = unpacked_scalar;

        // 计算期望值
        param.expected = calculateExpected<int32_t>(op_mode, param.input1, param.input2, prec_out);

        param.is_float = false;
    }

    // 生成测试名称
    test_case_id++;
    param.test_name =
        std::string(magic_enum::enum_name(width_mode)) + "_" + getPrecisionString(prec_out) + "_" +
        std::string(magic_enum::enum_name(op_mode)) + "_" + std::to_string(test_case_id);

    test_cases.push_back(param);
    return test_cases;
}

std::vector<int32_t> TestCaseGenerator::generateIntData(uint8_t prec, bool is_format2)
{
    auto [min_val, max_val] = getIntRange(prec);
    int parallelism = getParallelism(prec, is_format2);  // 假设使用Format1

    std::vector<int32_t> data(parallelism);
    std::uniform_int_distribution<int32_t> dist(min_val, max_val);

    // 生成随机值
    for (int i = 0; i < parallelism; ++i)
    {
        data[i] = dist(rng);
    }

    // 确保包含一些特殊值
    // if (parallelism >= 4) {
    //     data[0] = min_val;  // 最小值
    //     data[1] = max_val;  // 最大值
    //     data[2] = 0;        // 零
    //     data[3] = max_val/2;// 中间值
    // }

    return data;
}

std::vector<float> TestCaseGenerator::generateFloatData(uint8_t prec, bool is_format2)
{
    auto [min_val, max_val] = getFloatRange(prec);
    int parallelism = getParallelism(prec, is_format2);  // 假设使用Format1

    std::vector<float> data(parallelism);
    std::uniform_real_distribution<float> dist(-10000.0, 10000.0);  // 使用一半范围避免溢出

    // 生成随机值
    for (int i = 0; i < parallelism; ++i)
    {
        data[i] = dist(rng);
    }

    // 确保包含一些特殊值
    if (parallelism >= 4)
    {
        data[0] = min_val;  // 最小值
        data[1] = max_val;  // 最大值
        data[2] = 0.0f;     // 零值
        data[3] = 1.0f;     // 单位值
    }

    return data;
}

std::pair<int32_t, int32_t> TestCaseGenerator::getIntRange(uint8_t prec)
{
    switch (prec)
    {
        case dtype::INT4:
            return {-8, 7};
        case dtype::INT8:
            return {-128, 127};
        case dtype::INT16:
            return {-32768, 32767};
        case dtype::INT32:
            return {-2147483648, 2147483647};
        default:
            return {0, 0};
    }
}

std::pair<float, float> TestCaseGenerator::getFloatRange(uint8_t prec)
{
    switch (prec)
    {
        case dtype::BF16:
        {
            const float bf16_max = std::ldexp(1.0f + (1.0f - 1.0f / 128.0f), 127);
            return {-bf16_max, bf16_max};
        }
        case dtype::FP16:
        {
            const float fp16_max = std::ldexp(1.0f + (1.0f - 1.0f / 1024.0f), 15);
            return {-fp16_max, fp16_max};
        }
        case dtype::FP32:
            return {-FLT_MAX, FLT_MAX};
        default:
            return {0.0f, 0.0f};
    }
}

template <typename T>
std::vector<T> TestCaseGenerator::calculateExpected(OpMode op_mode,
                                                    const std::vector<T>& in1,
                                                    const std::vector<T>& in2,
                                                    uint8_t prec_out)
{
    std::vector<T> result(in1.size());
    std::pair<T, T> range;
    if constexpr (std::is_floating_point<T>::value)
    {
        auto float_range = getFloatRange(prec_out);
        range = {float_range.first, float_range.second};
    }
    else
    {
        auto int_range = getIntRange(prec_out);
        range = {static_cast<T>(int_range.first), static_cast<T>(int_range.second)};
    }

    for (size_t i = 0; i < in1.size(); ++i)
    {
        T val;
        if constexpr (std::is_floating_point<T>::value)
        {
            // 浮点数操作
            switch (op_mode)
            {
                case OpMode::ADD:
                    val = in1[i] + in2[i];
                    break;
                case OpMode::SUB:
                    val = in1[i] - in2[i];
                    break;
                case OpMode::RSUB:
                    val = in2[i] - in1[i];
                    break;
                case OpMode::MUL:
                    val = in1[i] * in2[i];
                    break;
                case OpMode::MAX:
                    val = std::max(in1[i], in2[i]);
                    break;
                case OpMode::MIN:
                    val = std::min(in1[i], in2[i]);
                    break;
                case OpMode::EQ:
                    val = (std::abs(in1[i] - in2[i]) < 1e-6) ? 1.0f : 0.0f;
                    break;
                case OpMode::NE:
                    val = (std::abs(in1[i] - in2[i]) >= 1e-6) ? 1.0f : 0.0f;
                    break;
                case OpMode::GT:
                    val = (in1[i] > in2[i]) ? 1.0f : 0.0f;
                    break;
                case OpMode::GE:
                    val = (in1[i] >= in2[i]) ? 1.0f : 0.0f;
                    break;
                case OpMode::LT:
                    val = (in1[i] < in2[i]) ? 1.0f : 0.0f;
                    break;
                case OpMode::LE:
                    val = (in1[i] <= in2[i]) ? 1.0f : 0.0f;
                    break;
                default:
                    val = 0;
            }
        }
        else
        {
            // 整数操作
            switch (op_mode)
            {
                case OpMode::ADD:
                    val = in1[i] + in2[i];
                    break;
                case OpMode::SUB:
                    val = in1[i] - in2[i];
                    break;
                case OpMode::RSUB:
                    val = in2[i] - in1[i];
                    break;
                case OpMode::MUL:
                    val = in1[i] * in2[i];
                    break;
                case OpMode::MAX:
                    val = std::max(in1[i], in2[i]);
                    break;
                case OpMode::MIN:
                    val = std::min(in1[i], in2[i]);
                    break;
                case OpMode::EQ:
                    val = (in1[i] == in2[i]) ? 1 : 0;
                    break;
                case OpMode::NE:
                    val = (in1[i] != in2[i]) ? 1 : 0;
                    break;
                case OpMode::GT:
                    val = (in1[i] > in2[i]) ? 1 : 0;
                    break;
                case OpMode::GE:
                    val = (in1[i] >= in2[i]) ? 1 : 0;
                    break;
                case OpMode::LT:
                    val = (in1[i] < in2[i]) ? 1 : 0;
                    break;
                case OpMode::LE:
                    val = (in1[i] <= in2[i]) ? 1 : 0;
                    break;
                case OpMode::SHL:
                    val = in1[i] << (in2[i] & 0x1F);
                    break;
                case OpMode::SHR_FLOOR:
                {
                    int shift = in2[i] & 0x1F;
                    val = in1[i] >> shift;
                    break;
                }
                case OpMode::SHR_CEIL:
                {
                    int shift = in2[i] & 0x1F;
                    T shifted = in1[i] >> shift;
                    if (in1[i] < 0)
                    {
                        T mask = (T(1) << shift) - 1;
                        if ((in1[i] & mask) != 0)
                        {
                            shifted += 1;
                        }
                    }
                    val = shifted;
                    break;
                }
                case OpMode::SHR_ROUND:
                {
                    int shift = in2[i] & 0x1F;
                    if (shift == 0)
                    {
                        val = in1[i];
                    }
                    else
                    {
                        if (in1[i] < 0)
                        {
                            T abs_val = -in1[i];
                            bool round_up = (abs_val & (T(1) << (shift - 1))) != 0;
                            val = -((abs_val >> shift) + (round_up ? 1 : 0));
                        }
                        else
                        {
                            bool round_up = (in1[i] & (T(1) << (shift - 1))) != 0;
                            val = (in1[i] >> shift) + (round_up ? 1 : 0);
                        }
                    }
                    break;
                }
                case OpMode::AND:
                    val = in1[i] & in2[i];
                    break;
                case OpMode::OR:
                    val = in1[i] | in2[i];
                    break;
                case OpMode::XOR:
                    val = in1[i] ^ in2[i];
                    break;
                default:
                    val = 0;
            }
        }

        // 饱和处理
        result[i] = std::clamp(val, range.first, range.second);
    }

    return result;
}

int TestCaseGenerator::getParallelism(uint8_t prec, bool is_format2)
{
    if (is_format2)
    {
        switch (prec)
        {
            case dtype::INT4:
                return 4;
            case dtype::INT8:
                return 2;
            case dtype::INT16:
            case dtype::BF16:
            case dtype::FP16:
                return 1;
            case dtype::FP32:
                return 1;
            default:
                return 1;
        }
    }
    else
    {
        switch (prec)
        {
            case dtype::INT4:
                return 8;
            case dtype::INT8:
                return 4;
            case dtype::INT16:
                return 2;
            case dtype::INT32:
                return 1;
            case dtype::BF16:
            case dtype::FP16:
                return 2;
            case dtype::FP32:
                return 1;
            default:
                return 1;
        }
    }
}

template <typename T>
T TestCaseGenerator::calculateReductionExpected(OpMode op_mode,
                                                const std::vector<T>& in1,
                                                uint8_t prec_out)
{
    if (in1.empty())
        return T{};

    T result;
    if constexpr (std::is_floating_point<T>::value)
    {
        // 浮点数操作
        switch (op_mode)
        {
            case OpMode::ADD:
            {
                result = 0;
                for (const auto& val : in1)
                    result += val;
                break;
            }
            case OpMode::MIN:
            {
                result = in1[0];
                for (size_t i = 1; i < in1.size(); ++i)
                    result = std::min(result, in1[i]);
                break;
            }
            case OpMode::MAX:
            {
                result = in1[0];
                for (size_t i = 1; i < in1.size(); ++i)
                    result = std::max(result, in1[i]);
                break;
            }
            default:
                result = 0;
        }
    }
    else
    {
        // 整数操作
        switch (op_mode)
        {
            case OpMode::ADD:
            {
                result = 0;
                for (const auto& val : in1)
                    result += val;
                break;
            }
            case OpMode::MIN:
            {
                result = in1[0];
                for (size_t i = 1; i < in1.size(); ++i)
                    result = std::min(result, in1[i]);
                break;
            }
            case OpMode::MAX:
            {
                result = in1[0];
                for (size_t i = 1; i < in1.size(); ++i)
                    result = std::max(result, in1[i]);
                break;
            }
            case OpMode::AND:
            {
                result = in1[0];
                for (size_t i = 1; i < in1.size(); ++i)
                    result &= in1[i];
                break;
            }
            case OpMode::OR:
            {
                result = in1[0];
                for (size_t i = 1; i < in1.size(); ++i)
                    result |= in1[i];
                break;
            }
            case OpMode::XOR:
            {
                result = in1[0];
                for (size_t i = 1; i < in1.size(); ++i)
                    result ^= in1[i];
                break;
            }
            default:
                result = 0;
        }
    }

    // 饱和处理
    if constexpr (std::is_floating_point<T>::value)
    {
        auto [min_val, max_val] = getFloatRange(prec_out);
        return std::clamp(result, min_val, max_val);
    }
    else
    {
        auto [min_val, max_val] = getIntRange(prec_out);
        return std::clamp(result, static_cast<T>(min_val), static_cast<T>(max_val));
    }
}

std::vector<VpeTestParam> TestCaseGenerator::generateTestCases_V_S(OpMode op_mode,
                                                                   WidthMode width_mode,
                                                                   uint8_t prec_out,
                                                                   uint8_t prec_in1)
{
    static int test_case_id = 0;
    std::vector<VpeTestParam> test_cases;
    VpeTestParam param;

    // 设置基本配置
    param.config.op_mode = op_mode;
    param.config.width_mode = width_mode;
    param.config.prec_out = prec_out;
    param.config.prec_in1 = prec_in1;
    param.config.prec_in2 = 0;             // V_S操作不使用第二个输入
    param.config.funcsel = FunctSel::V_S;  // Vector to Scalar reduction

    // 设置Format2标志
    param.is_format2_in1 = false;
    param.is_format2_in2 = false;  // 不使用第二个输入
    param.is_format2_out = false;

    switch (param.config.width_mode)
    {
        case WidthMode::SINGLE_WIDTH:
            break;
        case WidthMode::WIDENING:
            // Input: FORMAT2, Output: FORMAT1
            param.is_format2_in1 = true;
            break;
        case WidthMode::NARROWING:
            // Input: FORMAT1, Output: FORMAT2
            param.is_format2_out = true;
            break;
    }

    // 检查操作类型是否合法
    bool is_float = isFloatingPoint(prec_in1) || isFloatingPoint(prec_out);
    if (is_float && (op_mode == OpMode::AND || op_mode == OpMode::OR || op_mode == OpMode::XOR))
    {
        // 浮点数不支持位运算，返回空测试用例
        return test_cases;
    }

    if (is_float)
    {
        // 生成浮点测试数据
        std::vector<float> float_input = generateFloatData(prec_in1, param.is_format2_in1);
        float float_expected = calculateReductionExpected<float>(op_mode, float_input, prec_out);

        // 转换为内部表示
        param.input1.resize(float_input.size());
        param.input2.clear();                     // V_S操作不使用第二个输入
        param.expected.resize(1);                 // reduction结果是标量
        param.float_expected = {float_expected};  // 保存浮点期望值

        // 设置浮点标志和误差容限
        param.is_float = true;
        param.tolerance = 1;  // reduction操作可能累积误差，所以设置较大的容限

        // 转换浮点数为内部表示
        for (size_t i = 0; i < float_input.size(); ++i)
        {
            param.input1[i] = glb_f2mh_conv(float_input[i], prec_in1);
        }
        param.expected[0] = glb_f2mh_conv(float_expected, prec_out);
    }
    else
    {
        // 生成整数测试数据
        param.input1 = generateIntData(prec_in1, param.is_format2_in1);
        param.input2.clear();  // V_S操作不使用第二个输入
        int32_t int_expected = calculateReductionExpected<int32_t>(op_mode, param.input1, prec_out);
        param.expected = {int_expected};
        param.is_float = false;
    }

    // 生成测试名称
    test_case_id++;
    param.test_name = std::string(magic_enum::enum_name(width_mode)) + "_" +
                      getPrecisionString(prec_out) + "_" +
                      std::string(magic_enum::enum_name(op_mode)) + "_" + "REDUCE_" +
                      std::to_string(test_case_id);

    test_cases.push_back(param);
    return test_cases;
}

std::vector<VpeTestParam> TestCaseGenerator::generateTestCases_V_V(WidthMode width_mode,
                                                                   uint8_t prec_out,
                                                                   uint8_t prec_in1)
{
    static int test_case_id = 0;
    std::vector<VpeTestParam> test_cases;
    VpeTestParam param;

    // 设置基本配置
    param.config.width_mode = width_mode;
    param.config.prec_out = prec_out;
    param.config.prec_in1 = prec_in1;
    param.config.prec_in2 = 0;             // V_V操作不使用第二个输入
    param.config.funcsel = FunctSel::V_V;  // Vector to Vector operation

    // 设置Format2标志
    param.is_format2_in1 = false;
    param.is_format2_in2 = false;  // 不使用第二个输入
    param.is_format2_out = false;

    switch (param.config.width_mode)
    {
        case WidthMode::SINGLE_WIDTH:
            break;
        case WidthMode::WIDENING:
            // Input: FORMAT2, Output: FORMAT1
            param.is_format2_in1 = true;
            break;
        case WidthMode::NARROWING:
            // Input: FORMAT1, Output: FORMAT2
            param.is_format2_out = true;
            break;
    }

    // 根据输入类型决定处理方式
    bool is_float_in = isFloatingPoint(prec_in1);
    bool is_float_out = isFloatingPoint(prec_out);
    param.is_float = is_float_out;  // 根据输出类型决定是否使用浮点验证

    if (is_float_in)
    {
        // 输入为浮点数
        std::vector<float> float_input = generateFloatData(prec_in1, param.is_format2_in1);
        param.input1.resize(float_input.size());

        // 转换输入的浮点数为内部表示
        for (size_t i = 0; i < float_input.size(); ++i)
        {
            param.input1[i] = glb_f2mh_conv(float_input[i], prec_in1);
        }

        if (is_float_out)
        {
            // 浮点数到浮点数的转换
            auto [min_out, max_out] = getFloatRange(prec_out);
            std::vector<float> float_expected;

            // 计算期望输出
            for (float val : float_input)
            {
                if (val > max_out)
                    float_expected.push_back(max_out);
                else if (val < min_out)
                    float_expected.push_back(min_out);
                else
                    float_expected.push_back(val);
            }

            // 转换期望输出为内部表示
            param.expected.resize(float_expected.size());
            for (size_t i = 0; i < float_expected.size(); ++i)
            {
                param.expected[i] = glb_f2mh_conv(float_expected[i], prec_out);
            }
            param.float_expected = float_expected;
            param.tolerance = 1;
        }
        else
        {
            // 浮点数到整数的转换
            auto [min_out, max_out] = getIntRange(prec_out);
            std::vector<int32_t> expected;

            // 计算期望输出
            for (size_t i = 0; i < float_input.size(); ++i)
            {
                // 先获取内部表示转换回的浮点值
                float actual_val;
                glb_mh2f_conv(param.input1[i], prec_in1, &actual_val);

                if (actual_val > max_out)
                    expected.push_back(max_out);
                else if (actual_val < min_out)
                    expected.push_back(min_out);
                else
                {
                    switch (prec_out)
                    {
                        case dtype::INT8:
                            expected.push_back(static_cast<int8_t>(actual_val));
                            break;
                        case dtype::INT16:
                            expected.push_back(static_cast<int16_t>(actual_val));
                            break;
                        case dtype::INT32:
                            expected.push_back(static_cast<int32_t>(actual_val));
                            break;
                    }
                }
            }
            param.expected = expected;
        }
    }
    else
    {
        // 输入为整数
        std::vector<int32_t> int_input = generateIntData(prec_in1, param.is_format2_in1);
        param.input1 = int_input;

        if (is_float_out)
        {
            // 整数到浮点数的转换
            auto [min_out, max_out] = getFloatRange(prec_out);
            std::vector<float> float_expected;

            // 计算期望输出
            for (int32_t val : int_input)
            {
                float_expected.push_back(std::clamp(static_cast<float>(val), min_out, max_out));
            }

            // 转换期望输出为内部表示
            param.expected.resize(float_expected.size());
            for (size_t i = 0; i < float_expected.size(); ++i)
            {
                param.expected[i] = glb_f2mh_conv(float_expected[i], prec_out);
            }
            param.float_expected = float_expected;
            param.tolerance = 1;
        }
        else
        {
            // 整数到整数的转换
            auto [min_out, max_out] = getIntRange(prec_out);
            std::vector<int32_t> expected;

            // 计算期望输出
            for (int32_t val : int_input)
            {
                expected.push_back(std::clamp(val, min_out, max_out));
            }
            param.expected = expected;
        }
    }

    param.input2.clear();  // V_V操作不使用第二个输入

    // 生成测试名称
    test_case_id++;
    param.test_name = std::string(magic_enum::enum_name(width_mode)) + "_" +
                      getPrecisionString(prec_out) + "_FROM_" + getPrecisionString(prec_in1) + "_" +
                      std::to_string(test_case_id);

    test_cases.push_back(param);
    return test_cases;
}

std::vector<int32_t> TestCaseGenerator::generateShiftData(uint8_t prec, bool is_format2)
{
    int parallelism = getParallelism(prec, is_format2);
    std::vector<int32_t> data(parallelism);

    // 根据精度类型确定最大移位量
    int max_shift;
    switch (prec)
    {
        case dtype::INT4:
            max_shift = 3;
            break;  // 4位宽，最大移位量为7
        case dtype::INT8:
            max_shift = 7;
            break;  // 8位宽，最大移位量为7
        case dtype::INT16:
            max_shift = 15;
            break;  // 16位宽，最大移位量为15
        case dtype::INT32:
            max_shift = 31;
            break;  // 32位宽，最大移位量为31
        default:
            max_shift = 31;  // 默认使用最大值
    }

    // 生成随机值，范围限制在[0,max_shift]
    std::uniform_int_distribution<int32_t> dist(0, max_shift);

    // 生成随机移位量
    for (int i = 0; i < parallelism; ++i)
    {
        data[i] = dist(rng);
    }

    return data;
}

}  // namespace test
}  // namespace vpu