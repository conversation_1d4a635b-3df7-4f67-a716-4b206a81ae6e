#pragma once

#include <cfloat>
#include <cmath>
#include <random>
#include <vector>
#include "../../../../include/npu_config.h"
#include "../vpe_config.h"
#include "vpe_test.h"

#include "utils/utils.h"
// 声明浮点数转换函数
#include "utils/test_utils.h"

// 添加辅助函数，将precision uint8_t转换为字符串
inline std::string getPrecisionString(uint8_t prec)
{
    switch (prec)
    {
        case dtype::INT4:
            return "INT4";
        case dtype::INT8:
            return "INT8";
        case dtype::INT16:
            return "INT16";
        case dtype::INT32:
            return "INT32";
        case dtype::FP16:
            return "FP16";
        case dtype::FP32:
            return "FP32";
        case dtype::BF16:
            return "BF16";
        default:
            return "UNKNOWN";
    }
}

namespace vpu
{
namespace test
{

class TestCaseGenerator
{
  public:
    TestCaseGenerator() : rng(std::random_device{}())
    {
    }

    // 生成指定操作和精度配置的测试用例
    std::vector<VpeTestParam> generateTestCases(OpMode op_mode,
                                                WidthMode width_mode,
                                                FunctSel funcsel,
                                                uint8_t prec_out,
                                                uint8_t prec_in1,
                                                uint8_t prec_in2,
                                                uint32_t val_in2 = 0);

    std::vector<VpeTestParam> generateTestCases_VV(OpMode op_mode,
                                                   WidthMode width_mode,
                                                   uint8_t prec_out,
                                                   uint8_t prec_in1,
                                                   uint8_t prec_in2);

    std::vector<VpeTestParam> generateTestCases_VS(OpMode op_mode,
                                                   WidthMode width_mode,
                                                   uint8_t prec_out,
                                                   uint8_t prec_in1,
                                                   uint8_t prec_in2,
                                                   uint32_t val_in2);

    std::vector<VpeTestParam> generateTestCases_V_S(OpMode op_mode,
                                                    WidthMode width_mode,
                                                    uint8_t prec_out,
                                                    uint8_t prec_in1);

    std::vector<VpeTestParam> generateTestCases_V_V(WidthMode width_mode,
                                                    uint8_t prec_out,
                                                    uint8_t prec_in1);

    uint32_t gen_scalar_data(uint8_t prec_in, bool is_float, bool is_format2);

    // 计算期望结果
    template <typename T>
    std::vector<T> calculateExpected(OpMode op_mode,
                                     const std::vector<T>& in1,
                                     const std::vector<T>& in2,
                                     uint8_t prec_out);

    // 计算reduction操作的期望结果
    template <typename T>
    T calculateReductionExpected(OpMode op_mode, const std::vector<T>& in1, uint8_t prec_out);

  private:
    std::mt19937 rng;  // 随机数生成器

    // 整数数据生成
    std::vector<int32_t> generateIntData(uint8_t prec, bool is_format2);

    // 浮点数据生成
    std::vector<float> generateFloatData(uint8_t prec, bool is_format2);

    // 获取整数类型的最大最小值
    std::pair<int32_t, int32_t> getIntRange(uint8_t prec);

    // 获取浮点类型的最大最小值
    std::pair<float, float> getFloatRange(uint8_t prec);

    // 获取数据并行度
    int getParallelism(uint8_t prec, bool is_format2);

    // 生成移位操作数
    std::vector<int32_t> generateShiftData(uint8_t prec, bool is_format2);

    // 判断是否是浮点类型
    bool isFloatingPoint(uint8_t prec)
    {
        return prec == dtype::BF16 || prec == dtype::FP16 || prec == dtype::FP32;
    }
};

}  // namespace test
}  // namespace vpu