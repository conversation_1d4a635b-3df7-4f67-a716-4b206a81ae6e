#pragma once
#include <cstdint>
#include <vector>
#include "utils/utils.h"
#include "vpe_config.h"
namespace vpu
{

class Vpe
{
  public:
    Vpe() = default;
    ~Vpe() = default;

    // Disable copy/move
    Vpe(const Vpe&) = delete;
    Vpe& operator=(const Vpe&) = delete;
    Vpe(Vpe&&) = delete;
    Vpe& operator=(Vpe&&) = delete;

    // Configure VPE
    void configure(const VpeConfig& config);

    // Main processing function
    uint32_t process(uint32_t data1, uint32_t data2);

  private:
    // Processing paths based on funcsel
    uint32_t process_vv_v(uint32_t data1, uint32_t data2);  // Vector-Vector to Vector
    // uint32_t process_vs_v(uint32_t data1, uint32_t data2);  // Vector-Scalar to Vector
    uint32_t process_v_s(uint32_t data1);  // Vector to Scalar (reduction)
    uint32_t process_v_v(uint32_t data1);  // Vector to Vector (conversion)

    // Element extraction and packing
    std::vector<uint32_t> extract_elements(uint32_t data, uint8_t prec, bool is_format2) const;
    uint32_t pack_elements(const std::vector<uint32_t>& elements,
                           uint8_t prec,
                           bool is_format2) const;

    // Saturation handling
    uint32_t apply_saturation(uint32_t value, uint8_t target_prec) const;

    // Configuration storage
    std::vector<uint32_t> elements1_buffer_;
    std::vector<uint32_t> elements2_buffer_;
    std::vector<uint32_t> results_buffer_;
    VpeConfig config_;
};

}  // namespace vpu