#pragma once
#include <cstdint>
#include "npu_config.h"

namespace vpu
{

// Configuration structure
struct VpeConfig
{
    uint8_t prec_in1;      // Input1 precision, using dtype::INT4, dtype::INT8 etc.
    uint8_t prec_in2;      // Input2 precision
    uint8_t prec_out;      // Output precision
    OpMode op_mode;        // Operation mode from vpu::OpMode
    WidthMode width_mode;  // Width mode from vpu::WidthMode
    // add funcsel from vpu::FunctSel
    FunctSel funcsel;
};

}  // namespace vpu