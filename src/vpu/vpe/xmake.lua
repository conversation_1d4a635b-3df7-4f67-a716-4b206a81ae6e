target("vpe")
    set_kind("static")
    add_files("vpe.cpp")
    add_includedirs(".")
    add_files("vpe_arithmetic.cpp")
    add_deps("common")
    add_deps("dcim_macro")
    add_deps("systemc_logger")
    add_deps("utils")

target("vpe_test")
    set_kind("binary")
    add_files("test/*.cpp")
    add_includedirs("test")
    add_deps("vpe")
    add_deps("common")
    add_deps("utils")
    add_packages("gtest")
    add_ldflags("-Wl,--undefined=sc_main", {force = true})
    add_packages("magic_enum")
    add_tests("vpe_test", {group = "vpe"})
includes("../../cim_cluster/xmake.lua")