#include "vpe_arithmetic.h"
#include <algorithm>
#include <cmath>
#include <limits>

namespace vpu
{

float convert_uint32_to_float(uint32_t val, uint8_t prec)
{
    float flt_val = val;
    glb_mh2f_conv(val, prec, &flt_val);
    return flt_val;
}

namespace
{
// 类型检查辅助函数
bool is_float_type(uint8_t type_code)
{
    return (type_code == static_cast<uint8_t>(dtype::TypeCode::FP) ||
            type_code == static_cast<uint8_t>(dtype::TypeCode::BF));
}

// 通用操作模板（支持浮点和整数）
template <typename Op>
uint32_t handle_operation(uint32_t a,
                          uint8_t prec_a,
                          uint32_t b,
                          uint8_t prec_b,
                          uint8_t prec_out,
                          Op op)
{
    uint8_t a_type_code = (prec_a >> 3);
    uint8_t b_type_code = (prec_b >> 3);
    bool is_float = is_float_type(a_type_code) || is_float_type(b_type_code);

    if (!is_float)
    {
        // 输入已经是解包并符号扩展后的数据，直接转换
        int32_t a_signed = static_cast<int32_t>(a);
        int32_t b_signed = static_cast<int32_t>(b);
        int32_t result = op(a_signed, b_signed);
        return static_cast<uint32_t>(result);
    }
    else
    {
        float a_float = convert_uint32_to_float(a, prec_a);
        float b_float = convert_uint32_to_float(b, prec_b);
        float result = op(a_float, b_float);
        // std::cout << "a_float:  "<< a_float << "\tb_float:  " << b_float << "\tres:  " << result
        // << std::endl;
        uint32_t result_uint = glb_f2mh_conv(result, prec_out);
        return result_uint;
    }
}

// 仅整数操作模板
template <typename Op>
uint32_t handle_integer_only(uint32_t a,
                             uint8_t prec_a,
                             uint32_t b,
                             uint8_t prec_b,
                             uint8_t prec_out,
                             Op op)
{
    uint8_t a_type_code = (prec_a >> 3);
    uint8_t b_type_code = (prec_b >> 3);

    if (is_float_type(a_type_code) || is_float_type(b_type_code))
    {
        error_sc("Operation only supports integer types");
        return 0;
    }

    // 输入已经是解包并符号扩展后的数据，直接转换
    int32_t a_signed = static_cast<int32_t>(a);
    int32_t b_signed = static_cast<int32_t>(b);
    int32_t result = op(a_signed, b_signed);
    return static_cast<uint32_t>(result);
}
}  // anonymous namespace

// Arithmetic operations
uint32_t handle_add(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_operation(a, prec_a, b, prec_b, prec_out, [](auto x, auto y) { return x + y; });
}

uint32_t handle_sub(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_operation(a, prec_a, b, prec_b, prec_out, [](auto x, auto y) { return x - y; });
}

uint32_t handle_min(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_operation(
        a, prec_a, b, prec_b, prec_out, [](auto x, auto y) { return std::min(x, y); });
}

uint32_t handle_max(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_operation(
        a, prec_a, b, prec_b, prec_out, [](auto x, auto y) { return std::max(x, y); });
}

uint32_t handle_multiplication(uint32_t a,
                               uint8_t prec_a,
                               uint32_t b,
                               uint8_t prec_b,
                               uint8_t prec_out)
{
    return handle_operation(a, prec_a, b, prec_b, prec_out, [](auto x, auto y) { return x * y; });
}

// Compare operations
uint32_t handle_eq(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_operation(
        a, prec_a, b, prec_b, prec_out, [](auto x, auto y) { return x == y ? 1u : 0u; });
}

uint32_t handle_ne(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_operation(
        a, prec_a, b, prec_b, prec_out, [](auto x, auto y) { return x != y ? 1u : 0u; });
}

uint32_t handle_gt(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_operation(
        a, prec_a, b, prec_b, prec_out, [](auto x, auto y) { return x > y ? 1u : 0u; });
}

uint32_t handle_ge(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_operation(
        a, prec_a, b, prec_b, prec_out, [](auto x, auto y) { return x >= y ? 1u : 0u; });
}

uint32_t handle_lt(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_operation(
        a, prec_a, b, prec_b, prec_out, [](auto x, auto y) { return x < y ? 1u : 0u; });
}

uint32_t handle_le(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_operation(
        a, prec_a, b, prec_b, prec_out, [](auto x, auto y) { return x <= y ? 1u : 0u; });
}

// Shift operations (integer only)
uint32_t handle_shl(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_integer_only(a,
                               prec_a,
                               b,
                               prec_b,
                               prec_out,
                               [](uint32_t x, uint32_t y)
                               {
                                   // 先转换为有符号数进行算术左移
                                   int32_t signed_x = static_cast<int32_t>(x);
                                   return static_cast<uint32_t>(signed_x << (y & 0x1F));
                               });
}

uint32_t handle_shr_floor(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_integer_only(a,
                               prec_a,
                               b,
                               prec_b,
                               prec_out,
                               [](uint32_t x, uint32_t y)
                               {
                                   // 转换为有符号数进行算术右移
                                   int32_t signed_x = static_cast<int32_t>(x);
                                   return static_cast<uint32_t>(signed_x >> (y & 0x1F));
                               });
}

uint32_t handle_shr_ceil(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_integer_only(a,
                               prec_a,
                               b,
                               prec_b,
                               prec_out,
                               [](uint32_t x, uint32_t y)
                               {
                                   y &= 0x1F;  // 限制移位值在[0,31]
                                   int32_t signed_x = static_cast<int32_t>(x);
                                   int32_t result = signed_x >> y;
                                   // 对于负数，检查被移出的位
                                   if (signed_x < 0)
                                   {
                                       int32_t mask = (1 << y) - 1;
                                       if ((signed_x & mask) != 0)
                                       {
                                           result += 1;
                                       }
                                   }
                                   return static_cast<uint32_t>(result);
                               });
}

uint32_t handle_shr_round(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_integer_only(
        a,
        prec_a,
        b,
        prec_b,
        prec_out,
        [](uint32_t x, uint32_t y)
        {
            y &= 0x1F;  // 限制移位值在[0,31]
            if (y == 0)
                return x;
            int32_t signed_x = static_cast<int32_t>(x);
            if (signed_x < 0)
            {
                // 对于负数，先取绝对值
                int32_t abs_x = -signed_x;
                bool round_up = (abs_x & (1 << (y - 1))) != 0;
                return static_cast<uint32_t>(-((abs_x >> y) + (round_up ? 1 : 0)));
            }
            else
            {
                // 对于正数
                bool round_up = (signed_x & (1 << (y - 1))) != 0;
                return static_cast<uint32_t>((signed_x >> y) + (round_up ? 1 : 0));
            }
        });
}

// Bitwise operations (integer only)
uint32_t handle_and(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_integer_only(
        a, prec_a, b, prec_b, prec_out, [](uint32_t x, uint32_t y) { return x & y; });
}

uint32_t handle_or(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_integer_only(
        a, prec_a, b, prec_b, prec_out, [](uint32_t x, uint32_t y) { return x | y; });
}

uint32_t handle_xor(uint32_t a, uint8_t prec_a, uint32_t b, uint8_t prec_b, uint8_t prec_out)
{
    return handle_integer_only(
        a, prec_a, b, prec_b, prec_out, [](uint32_t x, uint32_t y) { return x ^ y; });
}

// Reduction Operations Implementation
uint32_t handle_reduce_sum(const std::vector<uint32_t>& elements, uint8_t prec_in, uint8_t prec_out)
{
    if (elements.empty())
    {
        error_sc("Empty input for reduction operation");
        return 0;
    }

    // 对于浮点数，使用更高精度进行累加
    if (is_fp_type(prec_in))
    {
        float sum = 0.0f;
        for (const auto& element : elements)
        {
            float val = 0.0f;
            glb_mh2f_conv(element, prec_in, &val);
            sum += val;
        }
        return glb_f2mh_conv(sum, prec_out);
    }
    else
    {
        // 对于整数，使用第一个元素作为初始值
        uint32_t result = elements[0];
        // 从第二个元素开始累加
        for (size_t i = 1; i < elements.size(); ++i)
        {
            // 复用现有的add函数
            result = handle_add(result, prec_out, elements[i], prec_in, prec_out);
        }
        return result;
    }
}

uint32_t handle_reduce_min(const std::vector<uint32_t>& elements, uint8_t prec_in, uint8_t prec_out)
{
    if (elements.empty())
    {
        error_sc("Empty input for reduction operation");
        return 0;
    }

    if (is_fp_type(prec_in))
    {
        float min_val = std::numeric_limits<float>::max();
        for (const auto& element : elements)
        {
            float val = 0.0f;
            glb_mh2f_conv(element, prec_in, &val);
            min_val = std::min(min_val, val);
        }
        // 转换回目标格式
        return glb_f2mh_conv(min_val, prec_out);
    }
    else
    {
        uint32_t result = elements[0];
        for (size_t i = 1; i < elements.size(); ++i)
        {
            result = handle_min(result, prec_out, elements[i], prec_in, prec_out);
        }
        return result;
    }
}

uint32_t handle_reduce_max(const std::vector<uint32_t>& elements, uint8_t prec_in, uint8_t prec_out)
{
    if (elements.empty())
    {
        error_sc("Empty input for reduction operation");
        return 0;
    }

    if (is_fp_type(prec_in))
    {
        float max_val = -std::numeric_limits<float>::max();
        for (const auto& element : elements)
        {
            float val = 0.0f;
            glb_mh2f_conv(element, prec_in, &val);
            max_val = std::max(max_val, val);
        }
        // 转换回目标格式
        return glb_f2mh_conv(max_val, prec_out);
    }
    else
    {
        uint32_t result = elements[0];
        for (size_t i = 1; i < elements.size(); ++i)
        {
            result = handle_max(result, prec_out, elements[i], prec_in, prec_out);
        }
        return result;
    }
}

uint32_t handle_reduce_and(const std::vector<uint32_t>& elements, uint8_t prec_in, uint8_t prec_out)
{
    if (elements.empty())
    {
        error_sc("Empty input for reduction operation");
        return 0;
    }

    if (is_fp_type(prec_in))
    {
        error_sc("Bitwise operations not supported for floating point types");
        return 0;
    }

    uint32_t result = elements[0];
    for (size_t i = 1; i < elements.size(); ++i)
    {
        result = handle_and(result, prec_out, elements[i], prec_in, prec_out);
    }
    return result;
}

uint32_t handle_reduce_or(const std::vector<uint32_t>& elements, uint8_t prec_in, uint8_t prec_out)
{
    if (elements.empty())
    {
        error_sc("Empty input for reduction operation");
        return 0;
    }

    if (is_fp_type(prec_in))
    {
        error_sc("Bitwise operations not supported for floating point types");
        return 0;
    }

    uint32_t result = elements[0];
    for (size_t i = 1; i < elements.size(); ++i)
    {
        result = handle_or(result, prec_out, elements[i], prec_in, prec_out);
    }
    return result;
}

uint32_t handle_reduce_xor(const std::vector<uint32_t>& elements, uint8_t prec_in, uint8_t prec_out)
{
    if (elements.empty())
    {
        error_sc("Empty input for reduction operation");
        return 0;
    }

    if (is_fp_type(prec_in))
    {
        error_sc("Bitwise operations not supported for floating point types");
        return 0;
    }

    uint32_t result = elements[0];
    for (size_t i = 1; i < elements.size(); ++i)
    {
        result = handle_xor(result, prec_out, elements[i], prec_in, prec_out);
    }
    return result;
}

// Type Conversion Operations Implementation
uint32_t handle_convert_fp_to_int(uint32_t data, uint8_t prec_in, uint8_t prec_out)
{
    if (!is_fp_type(prec_in))
    {
        error_sc("Source type must be floating point for fp_to_int conversion");
        return 0;
    }

    // 先转换为float
    float flt_val = 0.0f;
    glb_mh2f_conv(data, prec_in, &flt_val);

    // 获取目标整数类型的范围
    int32_t min_val, max_val;
    switch (prec_out)
    {  // 取低3位得到位宽
        case dtype::INT4:
            min_val = -8;
            max_val = 7;
            break;
        case dtype::INT8:
            min_val = -128;
            max_val = 127;
            break;
        case dtype::INT16:
            min_val = -32768;
            max_val = 32767;
            break;
        case dtype::INT32:
            min_val = -2147483648;
            max_val = 2147483647;
            break;
        default:
            min_val = 0;
            max_val = 0;
    }

    // 范围检查和饱和处理
    if (flt_val > max_val)
    {
        return static_cast<uint32_t>(max_val);
    }
    else if (flt_val < min_val)
    {
        return static_cast<uint32_t>(min_val);
    }

    int32_t result;
    switch (prec_out)
    {
        case dtype::INT8:
            result = static_cast<int32_t>(static_cast<int8_t>(flt_val));
            break;
        case dtype::INT16:
            result = static_cast<int32_t>(static_cast<int16_t>(flt_val));
            break;
        case dtype::INT32:
            result = static_cast<int32_t>(flt_val);
            break;
        default:
            result = 0;
    }

    return static_cast<uint32_t>(result);
}

uint32_t handle_convert_int_to_fp(uint32_t data, uint8_t prec_in, uint8_t prec_out)
{
    if (is_fp_type(prec_in))
    {
        error_sc("Source type must be integer for int_to_fp conversion");
        return 0;
    }

    // 输入已经是解包并符号扩展后的数据，直接转换
    int32_t int_val = static_cast<int32_t>(data);

    // 转换为float并转换为目标浮点格式
    float flt_val = static_cast<float>(int_val);
    return glb_f2mh_conv(flt_val, prec_out);
}

uint32_t handle_convert_fp_to_fp(uint32_t data, uint8_t prec_in, uint8_t prec_out)
{
    if (!is_fp_type(prec_in) || !is_fp_type(prec_out))
    {
        error_sc("Both source and target types must be floating point for fp_to_fp conversion");
        return 0;
    }

    // 使用float作为中间格式进行转换
    float flt_val = 0.0f;
    glb_mh2f_conv(data, prec_in, &flt_val);

    return glb_f2mh_conv(flt_val, prec_out);
}

}  // namespace vpu