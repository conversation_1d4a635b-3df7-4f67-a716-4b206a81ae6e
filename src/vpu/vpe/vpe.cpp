#include "vpe.h"
#include <vector>
#include "npu_config.h"
#include "utils/systemc_logger.h"
#include "vpe_arithmetic.h"

namespace vpu
{

void Vpe::configure(const VpeConfig& config)
{
    config_ = config;
}

uint32_t Vpe::process(uint32_t data1, uint32_t data2)
{
    // 根据funcsel选择不同的处理路径
    switch (config_.funcsel)
    {
        case FunctSel::VV_V:
            return process_vv_v(data1, data2);
        case FunctSel::VS_V:
            // use vv_v
            return process_vv_v(data1, data2);
        case FunctSel::V_S:
            return process_v_s(data1);
        case FunctSel::V_V:
            return process_v_v(data1);
        default:
            error_sc("Invalid function select mode");
            return 0;
    }
}

// Vector-Vector to Vector processing
uint32_t Vpe::process_vv_v(uint32_t data1, uint32_t data2)
{
    // 保持原有的处理逻辑
    bool use_format2_in1 = false;
    bool use_format2_in2 = false;
    bool use_format2_out = false;

    switch (config_.width_mode)
    {
        case WidthMode::SINGLE_WIDTH:
            break;
        case WidthMode::WIDENING:
            // Input: FORMAT2, Output: FORMAT1
            use_format2_in1 = true;
            use_format2_in2 = true;
            break;
        case WidthMode::NARROWING:
            // Input1: FORMAT1, Input2&Output: FORMAT2
            use_format2_in2 = true;
            use_format2_out = true;
            break;
    }

    // 2. 提取各个元素
    elements1_buffer_.clear();
    elements2_buffer_.clear();
    elements1_buffer_ = extract_elements(data1, config_.prec_in1, use_format2_in1);
    elements2_buffer_ = extract_elements(data2, config_.prec_in2, use_format2_in2);

    results_buffer_.clear();
    results_buffer_.reserve(elements1_buffer_.size());

    for (size_t i = 0; i < elements1_buffer_.size(); ++i)
    {
        uint32_t result;
        switch (config_.op_mode)
        {
            case OpMode::ADD:
                result = handle_add(elements1_buffer_[i],
                                    config_.prec_in1,
                                    elements2_buffer_[i],
                                    config_.prec_in2,
                                    config_.prec_out);
                break;
            case OpMode::SUB:
                result = handle_sub(elements1_buffer_[i],
                                    config_.prec_in1,
                                    elements2_buffer_[i],
                                    config_.prec_in2,
                                    config_.prec_out);
                break;
            case OpMode::RSUB:
                result = handle_sub(elements2_buffer_[i],
                                    config_.prec_in2,
                                    elements1_buffer_[i],
                                    config_.prec_in1,
                                    config_.prec_out);
                break;
            case OpMode::MIN:
                result = handle_min(elements1_buffer_[i],
                                    config_.prec_in1,
                                    elements2_buffer_[i],
                                    config_.prec_in2,
                                    config_.prec_out);
                break;
            case OpMode::MAX:
                result = handle_max(elements1_buffer_[i],
                                    config_.prec_in1,
                                    elements2_buffer_[i],
                                    config_.prec_in2,
                                    config_.prec_out);
                break;
            case OpMode::MUL:
                result = handle_multiplication(elements1_buffer_[i],
                                               config_.prec_in1,
                                               elements2_buffer_[i],
                                               config_.prec_in2,
                                               config_.prec_out);
                break;

            // Compare operations
            case OpMode::EQ:
                result = handle_eq(elements1_buffer_[i],
                                   config_.prec_in1,
                                   elements2_buffer_[i],
                                   config_.prec_in2,
                                   config_.prec_out);
                break;
            case OpMode::NE:
                result = handle_ne(elements1_buffer_[i],
                                   config_.prec_in1,
                                   elements2_buffer_[i],
                                   config_.prec_in2,
                                   config_.prec_out);
                break;
            case OpMode::GT:
                result = handle_gt(elements1_buffer_[i],
                                   config_.prec_in1,
                                   elements2_buffer_[i],
                                   config_.prec_in2,
                                   config_.prec_out);
                break;
            case OpMode::GE:
                result = handle_ge(elements1_buffer_[i],
                                   config_.prec_in1,
                                   elements2_buffer_[i],
                                   config_.prec_in2,
                                   config_.prec_out);
                break;
            case OpMode::LT:
                result = handle_lt(elements1_buffer_[i],
                                   config_.prec_in1,
                                   elements2_buffer_[i],
                                   config_.prec_in2,
                                   config_.prec_out);
                break;
            case OpMode::LE:
                result = handle_le(elements1_buffer_[i],
                                   config_.prec_in1,
                                   elements2_buffer_[i],
                                   config_.prec_in2,
                                   config_.prec_out);
                break;

            // Shift operations
            case OpMode::SHL:
                result = handle_shl(elements1_buffer_[i],
                                    config_.prec_in1,
                                    elements2_buffer_[i],
                                    config_.prec_in2,
                                    config_.prec_out);
                break;
            case OpMode::SHR_FLOOR:
                result = handle_shr_floor(elements1_buffer_[i],
                                          config_.prec_in1,
                                          elements2_buffer_[i],
                                          config_.prec_in2,
                                          config_.prec_out);
                break;
            case OpMode::SHR_CEIL:
                result = handle_shr_ceil(elements1_buffer_[i],
                                         config_.prec_in1,
                                         elements2_buffer_[i],
                                         config_.prec_in2,
                                         config_.prec_out);
                break;
            case OpMode::SHR_ROUND:
                result = handle_shr_round(elements1_buffer_[i],
                                          config_.prec_in1,
                                          elements2_buffer_[i],
                                          config_.prec_in2,
                                          config_.prec_out);
                break;

            // Bitwise operations
            case OpMode::AND:
                result = handle_and(elements1_buffer_[i],
                                    config_.prec_in1,
                                    elements2_buffer_[i],
                                    config_.prec_in2,
                                    config_.prec_out);
                break;
            case OpMode::OR:
                result = handle_or(elements1_buffer_[i],
                                   config_.prec_in1,
                                   elements2_buffer_[i],
                                   config_.prec_in2,
                                   config_.prec_out);
                break;
            case OpMode::XOR:
                result = handle_xor(elements1_buffer_[i],
                                    config_.prec_in1,
                                    elements2_buffer_[i],
                                    config_.prec_in2,
                                    config_.prec_out);
                break;

            default:
                error_sc("Unsupported operation mode");
        }
        results_buffer_.push_back(result);
    }

    // 4. 统一进行饱和处理
    for (auto& result : results_buffer_)
    {
        result = apply_saturation(result, config_.prec_out);
    }

    // 5. 按照指定格式打包结果
    uint32_t result = pack_elements(results_buffer_, config_.prec_out, use_format2_out);
    return result;
}

std::vector<uint32_t> Vpe::extract_elements(uint32_t data, uint8_t prec, bool is_format2) const
{
    std::vector<uint32_t> elements;
    uint32_t mask;
    int element_count;
    int shift;

    auto sign_extend = [](uint32_t value, uint32_t sign_bit_mask, uint32_t value_mask) -> uint32_t
    {
        if (value & sign_bit_mask)
        {
            return value | ~value_mask;
        }
        return value;
    };

    // 根据精度确定掩码、元素数量进行移位
    switch (prec)
    {
        case dtype::INT4:
            mask = 0xF;
            element_count = 8;
            shift = 4;
            break;
        case dtype::INT8:
            mask = 0xFF;
            element_count = 4;
            shift = 8;
            break;
        case dtype::INT16:
        case dtype::FP16:
        case dtype::BF16:
            mask = 0xFFFF;
            element_count = 2;
            shift = 16;
            break;
        case dtype::INT32:
        case dtype::FP32:
            elements.push_back(data);
            return elements;
        default:
            error_sc("Unsupported precision");
    }

    if (is_format2)
    {
        element_count = element_count / 2;
        // FORMAT2: 间隔补0模式提取
        for (int i = 0; i < element_count; ++i)
        {
            uint32_t element = (data >> (i * shift * 2)) & mask;
            // 对整数类型进行符号扩展
            if (!is_fp_type(prec))
            {
                switch (prec)
                {
                    case dtype::INT4:
                        element = sign_extend(element, 0x8, 0xF);
                        break;
                    case dtype::INT8:
                        element = sign_extend(element, 0x80, 0xFF);
                        break;
                    case dtype::INT16:
                        element = sign_extend(element, 0x8000, 0xFFFF);
                        break;
                }
            }
            elements.push_back(element);
        }
    }
    else
    {
        // FORMAT1: 直接拼接模式提取
        for (int i = 0; i < element_count; ++i)
        {
            uint32_t element = (data >> (i * shift)) & mask;
            // 对整数类型进行符号扩展
            if (!is_fp_type(prec))
            {
                switch (prec)
                {
                    case dtype::INT4:
                        element = sign_extend(element, 0x8, 0xF);
                        break;
                    case dtype::INT8:
                        element = sign_extend(element, 0x80, 0xFF);
                        break;
                    case dtype::INT16:
                        element = sign_extend(element, 0x8000, 0xFFFF);
                        break;
                }
            }
            elements.push_back(element);
        }
    }
    return elements;
}

uint32_t Vpe::pack_elements(const std::vector<uint32_t>& elements,
                            uint8_t prec,
                            bool is_format2) const
{
    uint32_t result = 0;
    uint32_t shift = 0;
    uint32_t mask = 0;

    // 根据精度设置shift和mask
    switch (prec)
    {
        case dtype::INT4:
            shift = 4;
            mask = 0xF;
            break;
        case dtype::INT8:
            shift = 8;
            mask = 0xFF;
            break;
        case dtype::INT16:
        case dtype::FP16:
        case dtype::BF16:
            shift = 16;
            mask = 0xFFFF;
            break;
        case dtype::INT32:
        case dtype::FP32:
            shift = 32;
            mask = 0xFFFFFFFF;
            break;
        default:
            // 处理未知精度
            return 0;
    }

    if (is_format2)
    {
        // FORMAT2: 间隔补0模式打包
        for (size_t i = 0; i < elements.size(); ++i)
        {
            result |= ((elements[i] & mask) << (i * shift * 2));
        }
    }
    else
    {
        // FORMAT1: 直接拼接模式打包
        for (size_t i = 0; i < elements.size(); ++i)
        {
            result |= ((elements[i] & mask) << (i * shift));
        }
    }

    return result;
}

uint32_t Vpe::apply_saturation(uint32_t value, uint8_t target_prec) const
{
    switch (target_prec)
    {
        case dtype::INT4:
        {
            // 直接使用uint32_t存储的有符号整数位模式进行比较
            static const uint32_t max_val = 7;
            static const uint32_t min_val = static_cast<uint32_t>(-8);
            if (static_cast<int32_t>(value) > 7)
                return max_val;
            if (static_cast<int32_t>(value) < -8)
                return min_val;
            return value;
        }
        case dtype::INT8:
        {
            static const uint32_t max_val = 127;
            static const uint32_t min_val = static_cast<uint32_t>(-128);
            if (static_cast<int32_t>(value) > 127)
                return max_val;
            if (static_cast<int32_t>(value) < -128)
                return min_val;
            return value;
        }
        case dtype::INT16:
        {
            static const uint32_t max_val = 32767;
            static const uint32_t min_val = static_cast<uint32_t>(-32768);
            if (static_cast<int32_t>(value) > 32767)
                return max_val;
            if (static_cast<int32_t>(value) < -32768)
                return min_val;
            return value;
        }
        case dtype::INT32:
            return value;  // 32位不需要饱和处理
        case dtype::FP16:
        {
            // 将二进制表示转换为float进行比较
            float flt_val = 0.0f;
            glb_mh2f_conv(value, dtype::FP16, &flt_val);
            const float fp16_max = std::ldexp(1.0f + (1.0f - 1.0f / 1024.0f), 15);
            const float fp16_min = -std::ldexp(1.0f + (1.0f - 1.0f / 1024.0f), 15);
            // FP16的最大最小值
            if (flt_val > fp16_max)
            {
                // 转回FP16二进制表示
                return glb_f2mh_conv(fp16_max, dtype::FP16);
            }
            if (flt_val < fp16_min)
            {
                return glb_f2mh_conv(fp16_min, dtype::FP16);
            }
            return value;
        }
        case dtype::FP32:
            return value;
        case dtype::BF16:
        {
            // 将二进制表示转换为float进行比较
            float flt_val = 0.0f;
            glb_mh2f_conv(value, dtype::BF16, &flt_val);

            // BF16的最大最小值 (2^127 * (1 + (1-2^-7)))
            const float bf16_max = std::ldexp(1.0f + (1.0f - 1.0f / 128.0f), 127);
            const float bf16_min = -std::ldexp(1.0f + (1.0f - 1.0f / 128.0f), 127);

            if (flt_val > bf16_max)
            {
                return glb_f2mh_conv(bf16_max, dtype::BF16);
            }
            if (flt_val < bf16_min)
            {
                return glb_f2mh_conv(bf16_min, dtype::BF16);
            }
            return value;
        }
        default:
            error_sc("Unsupported precision for saturation");
    }
}

// Vector to Scalar processing (reduction)
uint32_t Vpe::process_v_s(uint32_t data1)
{
    // 1. 根据width_mode确定数据格式
    bool use_format2_in1 = false;

    switch (config_.width_mode)
    {
        case WidthMode::SINGLE_WIDTH:
            break;
        case WidthMode::WIDENING:
            use_format2_in1 = true;
            break;
        case WidthMode::NARROWING:
            error_sc("Narrowing mode not supported for V_S operation");
            break;
    }

    // 2. 提取输入元素
    elements1_buffer_ = extract_elements(data1, config_.prec_in1, use_format2_in1);

    // 3. 根据操作类型执行归约操作
    uint32_t result;
    switch (config_.op_mode)
    {
        case OpMode::ADD:
            result = handle_reduce_sum(elements1_buffer_, config_.prec_in1, config_.prec_out);
            break;
        case OpMode::MIN:
            result = handle_reduce_min(elements1_buffer_, config_.prec_in1, config_.prec_out);
            break;
        case OpMode::MAX:
            result = handle_reduce_max(elements1_buffer_, config_.prec_in1, config_.prec_out);
            break;
        case OpMode::AND:
            result = handle_reduce_and(elements1_buffer_, config_.prec_in1, config_.prec_out);
            break;
        case OpMode::OR:
            result = handle_reduce_or(elements1_buffer_, config_.prec_in1, config_.prec_out);
            break;
        case OpMode::XOR:
            result = handle_reduce_xor(elements1_buffer_, config_.prec_in1, config_.prec_out);
            break;
        default:
            error_sc("Unsupported operation mode for V_S operation");
            return 0;
    }

    // 4. 饱和处理
    return apply_saturation(result, config_.prec_out);
}

// Vector to Vector processing (conversion)
uint32_t Vpe::process_v_v(uint32_t data1)
{
    // 1. 根据width_mode确定数据格式
    bool use_format2_in1 = false;
    bool use_format2_out = false;

    switch (config_.width_mode)
    {
        case WidthMode::SINGLE_WIDTH:
            break;
        case WidthMode::WIDENING:
            use_format2_in1 = true;
            break;
        case WidthMode::NARROWING:
            use_format2_out = true;
            break;
    }

    // 2. 提取输入元素
    elements1_buffer_ = extract_elements(data1, config_.prec_in1, use_format2_in1);

    // 3. 对每个元素进行类型转换
    results_buffer_.clear();
    results_buffer_.reserve(elements1_buffer_.size());

    for (auto element : elements1_buffer_)
    {
        uint32_t result;
        // 根据输入输出类型选择转换式
        if (is_fp_type(config_.prec_in1) && !is_fp_type(config_.prec_out))
        {
            result = handle_convert_fp_to_int(element, config_.prec_in1, config_.prec_out);
        }
        else if (!is_fp_type(config_.prec_in1) && is_fp_type(config_.prec_out))
        {
            result = handle_convert_int_to_fp(element, config_.prec_in1, config_.prec_out);
        }
        else if (is_fp_type(config_.prec_in1) && is_fp_type(config_.prec_out))
        {
            result = handle_convert_fp_to_fp(element, config_.prec_in1, config_.prec_out);
        }
        else
        {
            error_sc("Unsupported conversion type");
            return 0;
        }
        results_buffer_.push_back(result);
    }

    // 4. 饱和处理
    for (auto& result : results_buffer_)
    {
        result = apply_saturation(result, config_.prec_out);
    }

    // 5. 打包结果
    return pack_elements(results_buffer_, config_.prec_out, use_format2_out);
}

}  // namespace vpu