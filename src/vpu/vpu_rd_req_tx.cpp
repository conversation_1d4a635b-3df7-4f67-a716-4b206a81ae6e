#include "vpu_rd_req_tx.h"

namespace vpu
{

VPURdReqTx::ReqInfo VPURdReqTx::generate_req(uint32_t o_cnt_loop,
                                             uint32_t byte_base_in1,
                                             uint32_t byte_base_in2,
                                             WidthMode width_mode,
                                             FunctSel funcsel)
{
    ReqInfo req;

    // Calculate base addresses for both channels
    uint32_t idx_dim0b_in1 = o_cnt_loop;
    uint32_t idx_dim0b_in2 = o_cnt_loop;

    req.ch1_rd_req_addr = byte_base_in1 + idx_dim0b_in1;
    req.ch2_rd_req_addr = byte_base_in2 + idx_dim0b_in2;

    // Determine if channel 2 is used (only in VV_V mode)
    req.ch2_used = (funcsel == FunctSel::VV_V);

    // Generate masks based on width mode
    // For widening mode, mask on odd counts
    req.mask_ch1_rd = (width_mode == WidthMode::WIDENING) ? (o_cnt_loop % 2) : false;

    // For non-single-width modes, mask on odd counts
    req.mask_ch2_rd = (width_mode != WidthMode::SINGLE_WIDTH) ? (o_cnt_loop % 2) : false;

    return req;
}

}  // namespace vpu