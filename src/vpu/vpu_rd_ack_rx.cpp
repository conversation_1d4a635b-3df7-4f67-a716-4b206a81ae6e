#include "vpu_rd_ack_rx.h"
#include <cstdint>
#include <cstring>
#include "npu_config.h"

using namespace dtype;
namespace vpu
{

VPURdAckRx::VPURdAckRx(sc_core::sc_module_name name)
    : sc_core::sc_module(name), init_socket("init_socket")
{
    m_saved_msb.fill(0);
}

VPURdAckRx::DataOut VPURdAckRx::process_read(uint64_t ch1_addr,
                                             uint64_t ch2_addr,
                                             bool ch2_used,
                                             bool mask_ch1_rd,
                                             bool mask_ch2_rd,
                                             uint32_t o_cnt_loop,
                                             WidthMode m_width_mode,
                                             uint8_t m_width_in1,
                                             uint8_t m_width_in2)
{
    DataOut result;
    result.data_valid = true;
    result.ch1_data.fill(0);
    result.ch2_data.fill(0);

    // Handle channel 1 read
    if (!mask_ch1_rd)
    {
        if (!send_read_request(ch1_addr, result.ch1_data))
        {
            result.data_valid = false;
            return result;
        }
    }

    // Handle channel 2 read if used
    if (ch2_used && !mask_ch2_rd)
    {
        if (!send_read_request(ch2_addr, result.ch2_data))
        {
            result.data_valid = false;
            return result;
        }
    }

    // Process data based on width mode
    switch (m_width_mode)
    {
        case WidthMode::SINGLE_WIDTH:
            // Both channels output directly
            break;

        case WidthMode::WIDENING:
            if (o_cnt_loop % 2 == 0)
            {
                // Even count: split data and save MSB for both channels
                Word256b msb1, lsb1;
                split_data(result.ch1_data, msb1, lsb1);
                m_saved_msb = msb1;
                result.ch1_data = extend_data(lsb1, m_width_in1);

                if (ch2_used)
                {
                    Word256b msb2, lsb2;
                    split_data(result.ch2_data, msb2, lsb2);
                    m_saved_msb = msb2;
                    result.ch2_data = extend_data(lsb2, m_width_in2);
                }
            }
            else
            {
                // Odd count: use saved MSB
                result.ch1_data = extend_data(m_saved_msb, m_width_in1);
                if (ch2_used)
                {
                    result.ch2_data = extend_data(m_saved_msb, m_width_in2);
                }
            }
            break;

        case WidthMode::NARROWING:
            // Channel 1 outputs directly
            if (ch2_used)
            {
                // Process channel 2
                if (o_cnt_loop % 2 == 0)
                {
                    Word256b msb2, lsb2;
                    split_data(result.ch2_data, msb2, lsb2);
                    m_saved_msb = msb2;
                    result.ch2_data = extend_data(lsb2, m_width_in2);
                }
                else
                {
                    result.ch2_data = extend_data(m_saved_msb, m_width_in2);
                }
            }
            break;
    }

    return result;
}

bool VPURdAckRx::send_read_request(uint64_t addr, Word256b& data)
{
    tlm::tlm_generic_payload trans;
    sc_core::sc_time delay = sc_core::SC_ZERO_TIME;

    trans.set_command(tlm::TLM_READ_COMMAND);
    trans.set_address(addr);
    trans.set_data_ptr(data.data());
    trans.set_data_length(NPUConfig::LMEM_WD / 8);
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

    init_socket->b_transport(trans, delay);

    return trans.get_response_status() == tlm::TLM_OK_RESPONSE;
}

void VPURdAckRx::split_data(const Word256b& input, Word256b& msb, Word256b& lsb)
{
    size_t half_size = NPUConfig::LMEM_WD / 16;  // Half size in bytes

    // Copy first half to LSB
    std::memcpy(lsb.data(), input.data(), half_size);
    std::memset(lsb.data() + half_size, 0, half_size);

    // Copy second half to MSB
    std::memcpy(msb.data(), input.data() + half_size, half_size);
    std::memset(msb.data() + half_size, 0, half_size);
}

VPURdAckRx::Word256b VPURdAckRx::extend_data(const Word256b& input, uint8_t width)
{
    Word256b result;
    result.fill(0);

    // Convert width code to actual bit width
    uint32_t bit_width = width_code_to_bits(static_cast<dtype::WidthCode>(width));

    // Calculate number of elements and extended size
    uint32_t num_elements =
        NPUConfig::LMEM_WD / (bit_width * 2);  // 256/(bit_width*2) elements after extension
    uint32_t extended_size = bit_width * 2;    // Size after extension

    // Process each element
    for (uint32_t i = 0; i < num_elements; i++)
    {
        uint32_t element = 0;
        uint32_t input_shift = i * bit_width;
        uint32_t output_shift = i * extended_size;

        // Extract element based on width
        switch (bit_width)
        {
            case 4:
                element = (input[input_shift / 8] >> (input_shift % 8)) & 0xF;
                break;
            case 8:
                element = input[input_shift / 8] & 0xFF;
                break;
            case 16:
            {
                uint16_t temp = 0;
                std::memcpy(&temp, &input[input_shift / 8], 2);
                element = temp;
                break;
            }
        }

        // Place extended element in result
        uint32_t byte_pos = output_shift / 8;
        if (byte_pos + (extended_size / 8) <= result.size())
        {
            std::memcpy(&result[byte_pos], &element, extended_size / 8);
        }
    }

    return result;
}

}  // namespace vpu