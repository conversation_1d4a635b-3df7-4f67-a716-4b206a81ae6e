#include <gtest/gtest.h>
#include <memory>
#include <random>
#include "cmd_decode.h"
#include "cmd_encode.h"
#include "instruction_generator.h"
#include "npu_config.h"
#include "utils/systemc_logger.h"

class CmdEncodeTest : public ::testing::Test
{
  protected:
    void SetUp() override
    {
        decoder = std::make_unique<InstructionDecoder>();
        encoder = std::make_unique<InstructionEncoder>();
        generator = std::make_unique<InstructionGenerator>();
    }

    std::unique_ptr<InstructionDecoder> decoder;
    std::unique_ptr<InstructionEncoder> encoder;
    std::unique_ptr<InstructionGenerator> generator;
};

// 测试编码器基本功能
TEST_F(CmdEncodeTest, BasicEncodeDecode)
{
    // 创建一个测试指令
    instruction::DecodedInstruction inst;
    inst.group = instruction::Group::CONTROL;
    inst.funct7 = instruction::opcode::SYNC;
    inst.rs2 = 1;
    inst.rs1 = 2;
    inst.xd = true;
    inst.xs1 = false;
    inst.xs2 = false;
    inst.rd = 3;
    inst.opcode = 0b0001010;
    inst.rs1val = 0x12345678;
    inst.rs2val = 0x87654321;

    // 编码
    auto encoded = encoder->encode(inst);
    ASSERT_FALSE(encoded.empty()) << "Encoding failed";

    // 解码
    auto decoded = decoder->decode(encoded);

    // 验证解码结果
    EXPECT_EQ(decoded.group, inst.group);
    EXPECT_EQ(decoded.funct7, inst.funct7);
    EXPECT_EQ(decoded.rs2, inst.rs2);
    EXPECT_EQ(decoded.rs1, inst.rs1);
    EXPECT_EQ(decoded.xd, inst.xd);
    EXPECT_EQ(decoded.xs1, inst.xs1);
    EXPECT_EQ(decoded.xs2, inst.xs2);
    EXPECT_EQ(decoded.rd, inst.rd);
    EXPECT_EQ(decoded.opcode, inst.opcode);
    EXPECT_EQ(decoded.rs1val, inst.rs1val);
    EXPECT_EQ(decoded.rs2val, inst.rs2val);
}

// 测试无效funct7值
TEST_F(CmdEncodeTest, InvalidFunct7)
{
    instruction::DecodedInstruction inst;
    inst.funct7 = 0xFF;  // 无效的funct7

    auto result = encoder->encode(inst);
    EXPECT_TRUE(result.empty()) << "Expected empty result for invalid funct7";
}

// 测试随机指令生成器
TEST_F(CmdEncodeTest, RandomInstructionGeneration)
{
    for (int i = 0; i < 100; ++i)
    {  // 生成100个随机指令
        auto encoded = generator->generateRandomInstruction();
        ASSERT_FALSE(encoded.empty()) << "Failed to generate instruction";

        // 验证生成的指令可以被正确解码
        auto decoded = decoder->decode(encoded);
        // 验证解码后的指令是否有效
        ASSERT_NE(decoded.group, instruction::Group::UNKNOWN) << "Invalid instruction generated";
    }
}

// 测试特定组的随机指令生成
TEST_F(CmdEncodeTest, GroupSpecificInstructionGeneration)
{
    // 测试每个指令组
    std::vector<instruction::Group> groups = {instruction::Group::CONTROL,
                                              instruction::Group::DATA_TRANSFER,
                                              instruction::Group::TENSOR_MANIPULATION,
                                              instruction::Group::MATRIX_PROCESSING,
                                              instruction::Group::VECTOR_PROCESSING};

    for (const auto& group : groups)
    {
        for (int i = 0; i < 20; ++i)
        {  // 每个组生成20个指令
            auto encoded = generator->generateRandomInstruction(group);
            ASSERT_FALSE(encoded.empty())
                << "Failed to generate instruction for group " << static_cast<int>(group);

            auto decoded = decoder->decode(encoded);
            // 验证生成的指令属于正确的组
            EXPECT_EQ(decoded.group, group);
        }
    }
}

// // 测试RV64模式
// TEST_F(CmdEncodeTest, RV64Mode) {
//     // 创建RV64模式的编码器和解码器

//     InstructionDecoder decoder64;
//     InstructionEncoder encoder64;

//     instruction::DecodedInstruction inst;
//     inst.group = instruction::Group::CONTROL;
//     inst.funct7 = instruction::opcode::SYNC;
//     inst.rs2 = 1;
//     inst.rs1 = 2;
//     inst.xd = true;
//     inst.xs1 = false;
//     inst.xs2 = false;
//     inst.rd = 3;
//     inst.opcode = 0b0001010;
//     inst.rs1val = 0x1234567890ABCDEF;
//     inst.rs2val = 0xFEDCBA9876543210;

//     // 编码
//     auto encoded = encoder64.encode(inst);
//     ASSERT_FALSE(encoded.empty()) << "RV64 encoding failed";

//     // 解码
//     auto decoded = decoder64.decode(encoded);

//     // 验证64位值是否正确保留
//     EXPECT_EQ(decoded.rs1val, inst.rs1val);
//     EXPECT_EQ(decoded.rs2val, inst.rs2val);
// }

int sc_main(int argc, char* argv[])
{
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}