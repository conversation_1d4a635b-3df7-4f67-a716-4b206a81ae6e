#pragma once

#include <cstdint>
#include <vector>
#include "npu_config.h"

class InstructionEncoder
{
  public:
    using Group = instruction::Group;
    using DecodedInstruction = instruction::DecodedInstruction;
    using InstructionInfo = instruction::InstructionInfo;

    // 构造函数
    InstructionEncoder();

    // 编码函数
    std::vector<uint8_t> encode(uint32_t funct7,
                                uint32_t rs2,
                                uint32_t rs1,
                                bool xd,
                                bool xs1,
                                bool xs2,
                                uint32_t rd,
                                uint32_t opcode,
                                uint64_t rs1val = 0,
                                uint64_t rs2val = 0);

    // 从解码指令编码
    std::vector<uint8_t> encode(const DecodedInstruction& decoded);

  private:
    // 使用共享的掩码常量
    static constexpr uint32_t FUNCT7_MASK = instruction::FUNCT7_MASK;
    static constexpr uint32_t RS2_MASK = instruction::RS2_MASK;
    static constexpr uint32_t RS1_MASK = instruction::RS1_MASK;
    static constexpr uint32_t XD_MASK = instruction::XD_MASK;
    static constexpr uint32_t XS1_MASK = instruction::XS1_MASK;
    static constexpr uint32_t XS2_MASK = instruction::XS2_MASK;
    static constexpr uint32_t RD_MASK = instruction::RD_MASK;
    static constexpr uint32_t OPCODE_MASK = instruction::OPCODE_MASK;
    uint8_t m_xlen;
};