#include "instruction_generator.h"
#include <random>

InstructionGenerator::InstructionGenerator()
{
    m_xlen = static_cast<uint8_t>(NPUConfig::RV_XLEN);
}

std::vector<uint8_t> InstructionGenerator::generateRandomInstruction()
{
    instruction::DecodedInstruction inst;

    // 生成随机字段
    inst.group = generateRandomGroup();
    inst.funct7 = generateRandomFunct7(inst.group);
    inst.rs2 = generateRandomRs();
    inst.rs1 = generateRandomRs();
    inst.rd = generateRandomRs();
    inst.opcode = generateRandomOpcode();

    // 根据ISA规范设置xd, xs1, xs2
    switch (inst.funct7)
    {
        case instruction::opcode::LK_LMEM:
            inst.xd = true;
            inst.xs1 = true;
            inst.xs2 = false;
            break;
        case instruction::opcode::RD_LMEM:
            inst.xd = true;
            inst.xs1 = true;
            inst.xs2 = false;
            break;
        case instruction::opcode::V_S_DRV:
            inst.xd = true;
            inst.xs1 = false;
            inst.xs2 = true;
            break;
        default:
            // 随机生成
            static std::random_device rd;
            static std::mt19937 gen(rd());
            static std::uniform_int_distribution<> dis(0, 1);
            inst.xd = dis(gen);
            inst.xs1 = dis(gen);
            inst.xs2 = dis(gen);
    }

    // 生成随机rs1val和rs2val
    static std::random_device rd;
    static std::mt19937_64 gen64(rd());
    static std::uniform_int_distribution<uint64_t> dis64;
    inst.rs1val = dis64(gen64);
    inst.rs2val = dis64(gen64);

    return m_encoder.encode(inst);
}

std::vector<uint8_t> InstructionGenerator::generateRandomInstruction(Group group)
{
    instruction::DecodedInstruction inst;
    inst.group = group;
    inst.funct7 = generateRandomFunct7(group);

    // 其余部分与无组别版本相同
    inst.rs2 = generateRandomRs();
    inst.rs1 = generateRandomRs();
    inst.rd = generateRandomRs();
    inst.opcode = generateRandomOpcode();

    // 根据ISA规范设置xd, xs1, xs2
    switch (inst.funct7)
    {
        case instruction::opcode::LK_LMEM:
            inst.xd = true;
            inst.xs1 = true;
            inst.xs2 = false;
            break;
        case instruction::opcode::RD_LMEM:
            inst.xd = true;
            inst.xs1 = true;
            inst.xs2 = false;
            break;
        case instruction::opcode::V_S_DRV:
            inst.xd = true;
            inst.xs1 = false;
            inst.xs2 = true;
            break;
        default:
            static std::random_device rd;
            static std::mt19937 gen(rd());
            static std::uniform_int_distribution<> dis(0, 1);
            inst.xd = dis(gen);
            inst.xs1 = dis(gen);
            inst.xs2 = dis(gen);
    }

    static std::random_device rd;
    static std::mt19937_64 gen64(rd());
    static std::uniform_int_distribution<uint64_t> dis64;
    inst.rs1val = dis64(gen64);
    inst.rs2val = dis64(gen64);

    return m_encoder.encode(inst);
}

uint32_t InstructionGenerator::generateRandomRs() const
{
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, 31);
    return dis(gen);
}

uint32_t InstructionGenerator::generateRandomOpcode() const
{
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis_high(0, 1);
    uint32_t opcode_low = (generateRandomRs() % 2 == 0) ? 0b00010 : 0b01010;
    uint32_t opcode_high = dis_high(gen);
    return (opcode_high << 5) | opcode_low;
}

InstructionGenerator::Group InstructionGenerator::generateRandomGroup() const
{
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, 4);
    return static_cast<Group>(dis(gen));
}

uint32_t InstructionGenerator::generateRandomFunct7(Group group) const
{
    static std::random_device rd;
    static std::mt19937 gen(rd());

    // 根据组别筛选有效的funct7值
    std::vector<uint32_t> validFunct7;
    for (const auto& [funct7, info] : instruction::INSTRUCTION_MAP)
    {
        if (info.group == group)
        {
            validFunct7.push_back(funct7);
        }
    }

    if (validFunct7.empty())
    {
        return 0;  // 应该不会发生，因为每个组都有有效的funct7
    }

    std::uniform_int_distribution<> dis(0, validFunct7.size() - 1);
    return validFunct7[dis(gen)];
}
