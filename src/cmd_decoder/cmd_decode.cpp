#include "cmd_decode.h"
#include <cstring>

// 构造函数实现
InstructionDecoder::InstructionDecoder()
{
    m_xlen = static_cast<uint8_t>(NPUConfig::RV_XLEN);
}

// vector重载的decode实现
InstructionDecoder::DecodedInstruction InstructionDecoder::decode(
    const std::vector<uint8_t>& command)
{
    return decode(command.data(), command.size());
}

// 主decode函数实现
InstructionDecoder::DecodedInstruction InstructionDecoder::decode(const uint8_t* command,
                                                                  size_t length)
{
    DecodedInstruction decoded;

    // 1. 验证输入参数
    if (command == nullptr)
    {
        warning_sc("Null command pointer");
        return decoded;
    }

    const size_t xlen_bytes = static_cast<size_t>(m_xlen) / 8;
    const size_t required_size = sizeof(uint32_t) + 2 * xlen_bytes;

    if (length < required_size)
    {
        warning_sc("Invalid command length");
        return decoded;
    }

    // 2. 从LSB开始解析
    const uint8_t* ptr = command;

    // 指令字段 (LSB)
    uint32_t instruction;
    std::memcpy(&instruction, ptr, sizeof(uint32_t));
    ptr += sizeof(uint32_t);

    // rs1val (middle)
    decoded.rs1val = 0;
    std::memcpy(&decoded.rs1val, ptr, xlen_bytes);
    ptr += xlen_bytes;

    // rs2val (MSB)
    decoded.rs2val = 0;
    std::memcpy(&decoded.rs2val, ptr, xlen_bytes);

    // 3. 解析指令字段
    decoded.funct7 = (instruction & FUNCT7_MASK) >> 25;
    decoded.rs2 = (instruction & RS2_MASK) >> 20;
    decoded.rs1 = (instruction & RS1_MASK) >> 15;
    decoded.xd = (instruction & XD_MASK) >> 14;
    decoded.xs1 = (instruction & XS1_MASK) >> 13;
    decoded.xs2 = (instruction & XS2_MASK) >> 12;
    decoded.rd = (instruction & RD_MASK) >> 7;
    decoded.opcode = instruction & OPCODE_MASK;

    // 4. 查找指令组和原语
    auto it = instruction::INSTRUCTION_MAP.find(decoded.funct7);
    if (it == instruction::INSTRUCTION_MAP.end())
    {
        warning_sc("Unknown instruction");
        return decoded;
    }

    decoded.group = it->second.group;
    decoded.targetPrimitive = it->second.targetPrimitive;
    decoded.instName = it->second.instName;

    return decoded;
}

inline instruction::IssueQueueCmd InstructionDecoder::toIssueQueueCmd(
    const DecodedInstruction& decoded)
{
    instruction::IssueQueueCmd cmd;

    // Copy relevant fields
    cmd.funct7 = decoded.funct7;
    cmd.xd = decoded.xd;
    cmd.xs1 = decoded.xs1;
    cmd.xs2 = decoded.xs2;
    cmd.rs1val = decoded.rs1val;
    cmd.rs2val = decoded.rs2val;

    return cmd;
}