#pragma once

#include <cstdint>
#include <string>
#include <string_view>
#include <unordered_map>
#include <vector>
#include "npu_config.h"
#include "utils/systemc_logger.h"

class InstructionDecoder
{
  public:
    using Group = instruction::Group;
    using DecodedInstruction = instruction::DecodedInstruction;
    using InstructionInfo = instruction::InstructionInfo;

    // 构造函数
    InstructionDecoder();

    // 修改解码函数返回类型
    DecodedInstruction decode(const uint8_t* command, size_t length);
    DecodedInstruction decode(const std::vector<uint8_t>& command);
    static inline instruction::IssueQueueCmd toIssueQueueCmd(const DecodedInstruction& decoded);

  private:
    // 使用共享的掩码常量
    static constexpr uint32_t FUNCT7_MASK = instruction::FUNCT7_MASK;
    static constexpr uint32_t RS2_MASK = instruction::RS2_MASK;
    static constexpr uint32_t RS1_MASK = instruction::RS1_MASK;
    static constexpr uint32_t XD_MASK = instruction::XD_MASK;
    static constexpr uint32_t XS1_MASK = instruction::XS1_MASK;
    static constexpr uint32_t XS2_MASK = instruction::XS2_MASK;
    static constexpr uint32_t RD_MASK = instruction::RD_MASK;
    static constexpr uint32_t OPCODE_MASK = instruction::OPCODE_MASK;
    uint8_t m_xlen;
};
