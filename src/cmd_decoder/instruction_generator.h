#pragma once

#include <cstdint>
#include <vector>
#include "cmd_encode.h"
#include "npu_config.h"

class InstructionGenerator
{
  public:
    using Group = instruction::Group;

    // 构造函数
    explicit InstructionGenerator();

    // 生成随机指令
    std::vector<uint8_t> generateRandomInstruction();

    // 根据指定的组生成随机指令
    std::vector<uint8_t> generateRandomInstruction(Group group);

  private:
    InstructionEncoder m_encoder;

    // 生成随机字段值
    uint32_t generateRandomRs() const;
    uint32_t generateRandomOpcode() const;
    Group generateRandomGroup() const;
    uint32_t generateRandomFunct7(Group group) const;
    uint8_t m_xlen;
};