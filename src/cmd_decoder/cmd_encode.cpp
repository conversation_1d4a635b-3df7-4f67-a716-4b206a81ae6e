#include "cmd_encode.h"
#include <cstring>
#include "utils/systemc_logger.h"

// 构造函数实现
InstructionEncoder::InstructionEncoder()
{
    m_xlen = static_cast<uint8_t>(NPUConfig::RV_XLEN);
}

// 编码函数实现
std::vector<uint8_t> InstructionEncoder::encode(uint32_t funct7,
                                                uint32_t rs2,
                                                uint32_t rs1,
                                                bool xd,
                                                bool xs1,
                                                bool xs2,
                                                uint32_t rd,
                                                uint32_t opcode,
                                                uint64_t rs1val,
                                                uint64_t rs2val)
{
    // 1. 验证funct7是否为有效指令
    auto it = instruction::INSTRUCTION_MAP.find(funct7);
    if (it == instruction::INSTRUCTION_MAP.end())
    {
        warning_sc("Invalid funct7 value");
        return std::vector<uint8_t>();
    }

    // 2. 构建指令字段
    uint32_t instruction = 0;
    instruction |= (funct7 << 25) & FUNCT7_MASK;
    instruction |= (rs2 << 20) & RS2_MASK;
    instruction |= (rs1 << 15) & RS1_MASK;
    instruction |= (xd ? 1 : 0) << 14;
    instruction |= (xs1 ? 1 : 0) << 13;
    instruction |= (xs2 ? 1 : 0) << 12;
    instruction |= (rd << 7) & RD_MASK;
    instruction |= opcode & OPCODE_MASK;

    // 3. 生成二进制命令
    const size_t xlen_bytes = static_cast<size_t>(m_xlen) / 8;
    std::vector<uint8_t> command(sizeof(uint32_t) + 2 * xlen_bytes);
    uint8_t* ptr = command.data();

    // 写入指令字段 (LSB)
    std::memcpy(ptr, &instruction, sizeof(uint32_t));
    ptr += sizeof(uint32_t);

    // 写入rs1val (middle)
    std::memcpy(ptr, &rs1val, xlen_bytes);
    ptr += xlen_bytes;

    // 写入rs2val (MSB)
    std::memcpy(ptr, &rs2val, xlen_bytes);

    return command;
}

// 从解码指令编码实现
std::vector<uint8_t> InstructionEncoder::encode(const DecodedInstruction& decoded)
{
    return encode(decoded.funct7,
                  decoded.rs2,
                  decoded.rs1,
                  decoded.xd,
                  decoded.xs1,
                  decoded.xs2,
                  decoded.rd,
                  decoded.opcode,
                  decoded.rs1val,
                  decoded.rs2val);
}