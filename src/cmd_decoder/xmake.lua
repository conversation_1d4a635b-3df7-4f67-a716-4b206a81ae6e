target("cmd_decoder_op")
    set_kind("static")
    add_files("cmd_decode.cpp")
    add_headerfiles("cmd_decode.h", {public = true})
    add_deps("common")
    add_deps("systemc_logger")

target("cmd_encode")
    set_kind("static")
    add_files("cmd_encode.cpp")
    add_headerfiles("cmd_encode.h", {public = true})
    add_deps("common")
    add_deps("systemc_logger")

target("instruction_generator")
    set_kind("static")
    add_files("instruction_generator.cpp")
    add_headerfiles("instruction_generator.h", {public = true})
    add_deps("common")
    add_deps("cmd_encode")
    add_deps("systemc_logger")

target("cmd_test_op")
    set_kind("binary")
    add_files("cmd_test.cpp")
    add_deps("cmd_decoder_op")
    add_deps("cmd_encode")
    add_deps("instruction_generator")
    add_packages("gtest")
    add_cxflags("-fvisibility=default")
    add_tests("cmd_test_op", {group = "cmd_decoder"})
