#include <tlm_utils/simple_initiator_socket.h>
#include <chrono>
#include <thread>
#include <vector>
#include "scoreboard.h"
#include "utils/systemc_logger.h"
class TestBench : public sc_core::sc_module
{
  public:
    tlm_utils::simple_initiator_socket<TestBench> socket;
    explicit TestBench(const sc_core::sc_module_name& name) : sc_core::sc_module(name)
    {
        SC_HAS_PROCESS(TestBench);
        SC_THREAD(run_test);
    }

  private:
    void run_test()
    {
        sc_core::wait(sc_core::SC_ZERO_TIME);

        test_single_update();
        test_multiple_updates();
        test_boundary_conditions();
        test_error_handling();
        test_concurrent_access();
        test_performance();
        test_functional_completeness();
        test_consistency();

        info_sc("All tests completed.");
    }

    void test_single_update()
    {
        scoreboardUpdate update;
        update.lmem_id = 1;  // Update LMEM 0
        update.update_status = 1;
        update.lmem_entries[0].status = LMemStatus::BUSY;

        send_update_transaction(update);
        scoreboardData readData = send_read_transaction();
        info_sc(scoreboard_data_string(readData).c_str());

        sc_assert(readData.lmem_entries[0].status == LMemStatus::BUSY);
        info_sc("Single update test passed.");
    }

    void test_multiple_updates()
    {
        scoreboardUpdate update;
        update.lmem_id = 3;  // Update LMEM 0 and 1 (binary 0b11 = 3)
        update.update_status = 1;
        update.update_fu_id = 1;
        update.lmem_entries[0].status = LMemStatus::BUSY;
        update.lmem_entries[0].fu_id = 2;
        update.lmem_entries[1].status = LMemStatus::READY;
        update.lmem_entries[1].fu_id = 3;

        send_update_transaction(update);
        scoreboardData readData = send_read_transaction();
        sc_assert(readData.lmem_entries[0].status == LMemStatus::BUSY);
        sc_assert(readData.lmem_entries[0].fu_id == 2);
        sc_assert(readData.lmem_entries[1].status == LMemStatus::READY);
        sc_assert(readData.lmem_entries[1].fu_id == 3);
        info_sc("Multiple updates test passed.");
    }

    void test_boundary_conditions()
    {
        // Test updating all LMEMs
        scoreboardUpdate update;
        update.lmem_id = (1 << NPUConfig::LMEM_NUM) - 1;
        update.update_status = 1;
        for (int i = 0; i < NPUConfig::LMEM_NUM; ++i)
        {
            update.lmem_entries[i].status = LMemStatus::BUSY;
        }
        send_update_transaction(update);
        scoreboardData readData = send_read_transaction();
        for (int i = 0; i < NPUConfig::LMEM_NUM; ++i)
        {
            sc_assert(readData.lmem_entries[i].status == LMemStatus::BUSY);
        }

        // Test updating no LMEMs
        update.lmem_id = 0;
        send_update_transaction(update);
        scoreboardData newReadData = send_read_transaction();
        for (int i = 0; i < NPUConfig::LMEM_NUM; ++i)
        {
            sc_assert(newReadData.lmem_entries[i].status == readData.lmem_entries[i].status);
        }

        info_sc("Boundary conditions test passed.");
    }

    void test_error_handling()
    {
        // This test depends on how error handling is implemented in the scoreboard
        // For example, you might test invalid LMEM_ID or status values
        info_sc("Error handling test not implemented.");
    }

    void test_concurrent_access()
    {
        // Note: std::thread may not be fully supported in some C++11 environments
        // Consider using SystemC's built-in threading if available
        std::vector<std::thread> threads;
        threads.reserve(10);
        for (int i = 0; i < 10; ++i)
        {
            threads.push_back(std::thread([this, i]() {
                scoreboardUpdate update;
                update.lmem_id = 1 << (i % NPUConfig::LMEM_NUM);
                update.update_status = 1;
                update.lmem_entries[i % NPUConfig::LMEM_NUM].status = LMemStatus::BUSY;
                send_update_transaction(update);
                send_read_transaction();
            }));
        }
        for (std::vector<std::thread>::iterator it = threads.begin(); it != threads.end(); ++it)
        {
            it->join();
        }
        info_sc("Concurrent access test completed.");
    }

    void test_performance()
    {
        auto start = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 1000; ++i)
        {
            scoreboardUpdate update;
            update.lmem_id = 1 << (i % NPUConfig::LMEM_NUM);
            update.update_status = 1;
            update.lmem_entries[i % NPUConfig::LMEM_NUM].status = LMemStatus::BUSY;
            send_update_transaction(update);
        }
        auto end = std::chrono::high_resolution_clock::now();
        std::chrono::duration<double, std::milli> elapsed = std::chrono::duration_cast<std::chrono::duration<double, std::milli>>(end - start);
        info_sc("Performance test: 1000 updates in %f ms", elapsed.count());
    }

    void test_functional_completeness()
    {
        // Test all possible state transitions
        std::vector<LMemStatus> states;
        states.push_back(LMemStatus::IDLE);
        states.push_back(LMemStatus::READY);
        states.push_back(LMemStatus::BUSY);
        states.push_back(LMemStatus::LOCK);
        for (std::vector<LMemStatus>::const_iterator it = states.begin(); it != states.end(); ++it)
        {
            LMemStatus to_state = *it;
            scoreboardUpdate update;
            update.lmem_id = 1;
            update.update_status = 1;
            update.lmem_entries[0].status = to_state;
            send_update_transaction(update);
            scoreboardData readData = send_read_transaction();
            sc_assert(readData.lmem_entries[0].status == to_state);
        }

        // Test CIMC mode update (only for CIM Cluster)
        scoreboardUpdate update;
        update.lmem_id = 1 << (NPUConfig::LMEM_NUM - 1);  // Last LMEM is CIM Cluster
        update.update_cimc_mode = 1;
        update.lmem_entries[NPUConfig::LMEM_NUM - 1].cimc_mode = 2;
        send_update_transaction(update);
        scoreboardData readData = send_read_transaction();
        sc_assert(readData.lmem_entries[NPUConfig::LMEM_NUM - 1].cimc_mode == 2);

        info_sc("Functional completeness test passed.");
    }

    void test_consistency()
    {
        scoreboardUpdate update;
        update.lmem_id = 3;  // Update LMEM 0 and 1 (binary 0b11 = 3)
        update.update_status = 1;
        update.update_fu_id = 1;
        update.lmem_entries[0].status = LMemStatus::BUSY;
        update.lmem_entries[0].fu_id = 2;
        update.lmem_entries[1].status = LMemStatus::READY;
        update.lmem_entries[1].fu_id = 3;

        send_update_transaction(update);
        scoreboardData readData = send_read_transaction();
        sc_assert(readData.lmem_entries[0].status == LMemStatus::BUSY);
        sc_assert(readData.lmem_entries[0].fu_id == 2);
        sc_assert(readData.lmem_entries[1].status == LMemStatus::READY);
        sc_assert(readData.lmem_entries[1].fu_id == 3);
        info_sc("Consistency test passed.");
    }

    void send_update_transaction(const scoreboardUpdate& update)
    {
        tlm::tlm_generic_payload trans;
        sc_core::sc_time delay = sc_core::SC_ZERO_TIME;

        trans.set_command(tlm::TLM_WRITE_COMMAND);
        trans.set_data_ptr(
            reinterpret_cast<unsigned char*>(const_cast<scoreboardUpdate*>(&update)));
        trans.set_data_length(sizeof(scoreboardUpdate));
        trans.set_streaming_width(sizeof(scoreboardUpdate));
        trans.set_byte_enable_ptr(0);
        trans.set_dmi_allowed(false);
        trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

        socket->b_transport(trans, delay);

        if (trans.is_response_error())
        {
            SC_REPORT_ERROR("TLM-2", "Response error from b_transport");
        }
    }

    scoreboardData send_read_transaction()
    {
        scoreboardData readData;
        tlm::tlm_generic_payload trans;
        sc_core::sc_time delay = sc_core::SC_ZERO_TIME;
        trans.set_command(tlm::TLM_READ_COMMAND);
        trans.set_data_ptr(reinterpret_cast<unsigned char*>(&readData));
        trans.set_data_length(sizeof(scoreboardData));
        trans.set_streaming_width(sizeof(scoreboardData));
        trans.set_byte_enable_ptr(0);
        trans.set_dmi_allowed(false);
        trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

        socket->b_transport(trans, delay);

        if (trans.is_response_error())
        {
            SC_REPORT_ERROR("TLM-2", "Response error from b_transport");
        }

        return readData;
    }
};

SC_MODULE(Top)
{
    scoreboard scoreboard_inst;
    TestBench testbench;

    SC_CTOR(Top) : scoreboard_inst("scoreboard"), testbench("testbench")
    {
        testbench.socket.bind(scoreboard_inst.socket);
    }
};

int sc_main(int argc, char* argv[])
{
    g_logger.setLogLevel(SystemCLogger::SC_INFO);
    Top top("top");
    sc_core::sc_start();
    return 0;
}
