#include "scoreboard.h"
#include <iostream>
// #include <magic_enum.hpp>  // Requires C++17, replaced with manual enum to string conversion
#include "npu_config.h"
#include "sysc/kernel/sc_module.h"

// Manual enum to string conversion functions for C++11 compatibility
const char* lmem_status_to_string(LMemStatus status) {
    switch (status) {
        case LMemStatus::IDLE: return "IDLE";
        case LMemStatus::READY: return "READY";
        case LMemStatus::BUSY: return "BUSY";
        case LMemStatus::LOCK: return "LOCK";
        default: return "UNKNOWN";
    }
}

const char* function_unit_type_to_string(FunctionUnitType type) {
    switch (type) {
        case FunctionUnitType::INVALID: return "INVALID";
        case FunctionUnitType::LMU: return "LMU";
        // Add other enum values as needed
        default: return "UNKNOWN";
    }
}
scoreboard::scoreboard(const sc_core::sc_module_name& name) : sc_module(name), socket("socket")
{
    SC_HAS_PROCESS(scoreboard);
    socket.register_b_transport(this, &scoreboard::b_transport);
    debug_sc("scoreboard constructed");
}

void scoreboard::b_transport(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
{
    if (trans.get_command() == tlm::TLM_WRITE_COMMAND)
    {
        auto* update = reinterpret_cast<scoreboardUpdate*>(trans.get_data_ptr());
        update_entry(*update);
        trans.set_response_status(tlm::TLM_OK_RESPONSE);
    }
    else if (trans.get_command() == tlm::TLM_READ_COMMAND)
    {
        auto* data = reinterpret_cast<scoreboardData*>(trans.get_data_ptr());
        read_scoreboard(*data);
        trans.set_response_status(tlm::TLM_OK_RESPONSE);
    }
    else
    {
        trans.set_response_status(tlm::TLM_COMMAND_ERROR_RESPONSE);
    }
    debug_sc("scoreboard b_transport done");
}

void scoreboard::read_scoreboard(scoreboardData& data)
{
    data.lmem_entries = lmem_entries;
}

void scoreboard::update_entry(const scoreboardUpdate& update)
{
    for (int i = 0; i < NPUConfig::LMEM_NUM; ++i)
    {
        if (update.lmem_id[i] == 1)
        {
            if (update.update_status)
            {
                lmem_entries[i].status = update.lmem_entries[i].status;
            }
            if (update.update_fu_id)
            {
                lmem_entries[i].fu_id = update.lmem_entries[i].fu_id;
            }
            if (update.update_ch_util)
            {
                lmem_entries[i].ch_util = update.lmem_entries[i].ch_util;
            }
            if (update.update_cimc_mode)
            {
                lmem_entries[i].cimc_mode = update.lmem_entries[i].cimc_mode;
            }
        }
    }
}

void scoreboard::print_scoreboard() const
{
    std::cout << "scoreboard status:" << std::endl;
    for (int i = 0; i < NPUConfig::LMEM_NUM; ++i)
    {
        std::stringstream ss;
        ss << "LMEM " << i << ": "
           << "status="
           << lmem_status_to_string(static_cast<LMemStatus>(lmem_entries[i].status.to_uint()))
           << ", fu_id="
           << function_unit_type_to_string(static_cast<FunctionUnitType>(lmem_entries[i].fu_id.to_uint()))
           << ", ch_util=" << lmem_entries[i].ch_util.to_uint()
           << ", cimc_mode=" << lmem_entries[i].cimc_mode.to_uint();
        info_sc(ss.str().c_str());
    }
}

// 返回值改为ss.str().c_str()
std::string scoreboard_data_string(const scoreboardData& data)
{
    std::stringstream ss;
    ss << "scoreboard status:" << std::endl;
    for (int i = 0; i < NPUConfig::LMEM_NUM; ++i)
    {
        ss << "LMEM " << i << ": "
           << "status="
           << lmem_status_to_string(static_cast<LMemStatus>(data.lmem_entries[i].status.to_uint()))
           << ", fu_id="
           << function_unit_type_to_string(static_cast<FunctionUnitType>(data.lmem_entries[i].fu_id.to_uint()))
           << ", ch_util=" << data.lmem_entries[i].ch_util.to_uint()
           << ", cimc_mode=" << data.lmem_entries[i].cimc_mode.to_uint() << std::endl;
    }
    return ss.str();
}

void scoreboard::set_scoreboard_state(uint32_t lmem_index,
                                      LMemStatus status,
                                      FunctionUnitType fu_id,
                                      uint8_t ch_util)
{
    try_sc(lmem_index < NPUConfig::LMEM_NUM, "LMEM index out of range");
    lmem_entries[lmem_index].status = status;
    lmem_entries[lmem_index].fu_id = fu_id;
    lmem_entries[lmem_index].ch_util = ch_util;
}

void scoreboard::reset()
{
    for (int i = 0; i < NPUConfig::LMEM_NUM; ++i)
    {
        lmem_entries[i].status = LMemStatus::READY;
        lmem_entries[i].fu_id = FunctionUnitType::INVALID;
        lmem_entries[i].ch_util = 0;
        lmem_entries[i].cimc_mode = 0;
    }
}