#include "cmd_decoder.h"
#include <magic_enum.hpp>
#include "utils/systemc_logger.h"

#include <cstdint>
#include <iomanip>
#include <random>
#include <stdexcept>

Command decode(const uint8_t* rawCommand)
{
    /*
    MSB LSB
  +----------+----------+-------+------+------+----+-----+-----+------+---------+
  |  rs2val  |  rs1val  | func7 |  rs2 |  rs1 | xd | xs1 | xs2 |  rd  | opcode  |
  | RV_XLEN  | RV_XLEN  |   7   |   5  |   5  |  1 |  1  |  1  |   5  |    7    |
  +----------+----------+-------+------+------+----+-----+-----+------+---------+
     */

    // 得到rs2val和rs1val,instruction
    Command cmd;
    uint32_t instruction;

    if constexpr (NPUConfig::RV_XLEN == 32)
    {
        cmd.rs2val = *reinterpret_cast<const uint32_t*>(rawCommand);
        cmd.rs1val = *reinterpret_cast<const uint32_t*>(rawCommand + 4);
        instruction = *reinterpret_cast<const uint32_t*>(rawCommand + 8);
    }
    else if constexpr (NPUConfig::RV_XLEN == 64)
    {
        cmd.rs2val = *reinterpret_cast<const uint64_t*>(rawCommand);
        cmd.rs1val = *reinterpret_cast<const uint64_t*>(rawCommand + 8);
        instruction = *reinterpret_cast<const uint32_t*>(rawCommand + 16);
    }
    else
    {
        static_assert(NPUConfig::RV_XLEN == 32 || NPUConfig::RV_XLEN == 64, "不支持的RV_XLEN值");
    }

    cmd.func7 = (instruction >> 25) & 0x7F;
    cmd.rs2 = (instruction >> 20) & 0x1F;
    cmd.rs1 = (instruction >> 15) & 0x1F;
    cmd.xd = (instruction >> 14) & 0x1;
    cmd.xs1 = (instruction >> 13) & 0x1;
    cmd.xs2 = (instruction >> 12) & 0x1;
    cmd.rd = (instruction >> 7) & 0x1F;
    cmd.opcode = instruction & 0x7F;

    return cmd;
}

InstructionGroup decodeGroup(const uint8_t& func7)
{
    switch (func7 >> 4)
    {
        case 0b000:
            return InstructionGroup::Control;
        case 0b001:
        case 0b010:
            return InstructionGroup::DataTransfer;
        case 0b011:
            return InstructionGroup::TensorManipulation;
        case 0b100:
            return InstructionGroup::MatrixProcessing;
        case 0b101:
            return InstructionGroup::VectorProcessing;
        default:
            throw std::runtime_error("Unknown instruction group");
    }
}

InstructionName decodeName(const uint8_t& func7)
{
    switch (func7)
    {
        case 0b0000000:
            return InstructionName::SYNC;
        case 0b0000001:
            return InstructionName::SW_CIMC;
        case 0b0000100:
            return InstructionName::LK_LMEM;
        case 0b0000101:
            return InstructionName::UNLK_LMEM;
        case 0b0000010:
            return InstructionName::WR_LMEM;
        case 0b0000011:
            return InstructionName::RD_LMEM;
        case 0b0010000:
            return InstructionName::TLD_CFG;
        case 0b0011000:
            return InstructionName::TLD_DRV;
        case 0b0100000:
            return InstructionName::TST_CFG;
        case 0b0101000:
            return InstructionName::TST_DRV;
        case 0b0110000:
            return InstructionName::TM_CFG;
        case 0b0110001:
            return InstructionName::BC_PRE;
        case 0b0111001:
            return InstructionName::BC_DRV;
        case 0b0111000:
            return InstructionName::MOV_DRV;
        case 0b0111010:
            return InstructionName::TRANS_DRV;
        case 0b1000000:
            return InstructionName::MP_CFG;
        case 0b1000001:  // TODO :还有一个DWCONV_PRE
            return InstructionName::CONV_PRE;
        case 0b1001000:
            return InstructionName::CONV_DRV;
        case 0b1001001:
            return InstructionName::DWCONV_DRV;
        case 0b1010000:
            return InstructionName::VP_CFG;
        case 0b1010001:
            return InstructionName::VV_V_PRE;
        case 0b1011000:
            return InstructionName::VV_V_DRV;
        case 0b1010010:
            return InstructionName::VS_V_PRE;
        case 0b1011001:
            return InstructionName::VS_V_DRV;
        case 0b1011010:
            return InstructionName::V_S_DRV;
        case 0b1011011:
            return InstructionName::V_V_DRV;
        default:
            error_sc("Unknown instruction");
    }
}

uint8_t encodeName(const InstructionName& name)
{
    switch (name)
    {
        case InstructionName::SYNC:
            return 0b0000000;
        case InstructionName::SW_CIMC:
            return 0b0000001;
        case InstructionName::LK_LMEM:
            return 0b0000100;
        case InstructionName::UNLK_LMEM:
            return 0b0000101;
        case InstructionName::WR_LMEM:
            return 0b0000010;
        case InstructionName::RD_LMEM:
            return 0b0000011;
        case InstructionName::TLD_CFG:
            return 0b0010000;
        case InstructionName::TLD_DRV:
            return 0b0011000;
        case InstructionName::TST_CFG:
            return 0b0100000;
        case InstructionName::TST_DRV:
            return 0b0101000;
        case InstructionName::TM_CFG:
            return 0b0110000;
        case InstructionName::BC_PRE:
            return 0b0110001;
        case InstructionName::BC_DRV:
            return 0b0111001;
        case InstructionName::MOV_DRV:
            return 0b0111000;
        case InstructionName::TRANS_DRV:
            return 0b0111010;
        case InstructionName::MP_CFG:
            return 0b1000000;
        case InstructionName::CONV_PRE:
            return 0b1000001;
        case InstructionName::CONV_DRV:
            return 0b1001000;
        case InstructionName::DWCONV_DRV:
            return 0b1001001;
        case InstructionName::VP_CFG:
            return 0b1010000;
        case InstructionName::VV_V_PRE:
            return 0b1010001;
        case InstructionName::VV_V_DRV:
            return 0b1011000;
        case InstructionName::VS_V_PRE:
            return 0b1010010;
        case InstructionName::VS_V_DRV:
            return 0b1011001;
        case InstructionName::V_S_DRV:
            return 0b1011010;
        case InstructionName::V_V_DRV:
            return 0b1011011;
        default:
            error_sc("Unknown instruction %s", magic_enum::enum_name(name).data());
    }
}

void print_cmd(const Command& cmd)
{
    std::stringstream ss;

    ss << "Instruction Group: " << magic_enum::enum_name(decodeGroup(cmd.func7)) << "\n";
    ss << "Instruction Name: " << magic_enum::enum_name(decodeName(cmd.func7)) << "\n";
    ss << "func7: 0x" << std::hex << std::setw(2) << std::setfill('0')
       << static_cast<int>(cmd.func7) << "\n";
    ss << "rs2: " << std::dec << static_cast<int>(cmd.rs2) << "\n";
    ss << "rs1: " << static_cast<int>(cmd.rs1) << "\n";
    ss << "xd: " << cmd.xd << ", xs1: " << cmd.xs1 << ", xs2: " << cmd.xs2 << "\n";
    ss << "rd: " << static_cast<int>(cmd.rd) << "\n";
    ss << "opcode: 0x" << std::hex << std::setw(2) << std::setfill('0')
       << static_cast<int>(cmd.opcode) << "\n";
    ss << "rs1val: 0x" << std::hex << std::setw(NPUConfig::RV_XLEN / 4) << std::setfill('0')
       << cmd.rs1val << "\n";
    ss << "rs2val: 0x" << std::hex << std::setw(NPUConfig::RV_XLEN / 4) << std::setfill('0')
       << cmd.rs2val;

    info_sc(ss.str().c_str());
}

// 生成数据
std::vector<uint8_t> generateRawCommand(uint64_t rs2val, uint64_t rs1val, uint32_t instruction)
{
    size_t commandSize = (2 * NPUConfig::RV_XLEN / 8) + 4;
    std::vector<uint8_t> rawCommand(commandSize);

    if constexpr (NPUConfig::RV_XLEN == 32)
    {
        uint32_t rs2val32 = static_cast<uint32_t>(rs2val);
        uint32_t rs1val32 = static_cast<uint32_t>(rs1val);
        std::memcpy(rawCommand.data(), &rs2val32, 4);
        std::memcpy(rawCommand.data() + 4, &rs1val32, 4);
        std::memcpy(rawCommand.data() + 8, &instruction, 4);
    }
    else if constexpr (NPUConfig::RV_XLEN == 64)
    {
        std::memcpy(rawCommand.data(), &rs2val, 8);
        std::memcpy(rawCommand.data() + 8, &rs1val, 8);
        std::memcpy(rawCommand.data() + 16, &instruction, 4);
    }

    return rawCommand;
}

uint32_t generateInstruction(uint8_t func7, uint8_t xd, uint8_t xs1, uint8_t xs2)
{
    static std::random_device rd_seed;
    static std::mt19937 gen(rd_seed());
    static std::uniform_int_distribution<> dis(0, 31);
    static std::uniform_int_distribution<> opcode_high_dis(0, 1);

    uint32_t instruction = 0;

    // 设置func7
    instruction |= (static_cast<uint32_t>(func7) << 25);

    // 随机生成rs2, rs1, rd (0-31)
    uint8_t rs2 = dis(gen);
    uint8_t rs1 = dis(gen);
    uint8_t rd = dis(gen);

    instruction |= (static_cast<uint32_t>(rs2) << 20);
    instruction |= (static_cast<uint32_t>(rs1) << 15);
    instruction |= (static_cast<uint32_t>(rd) << 7);

    // 设置xd, xs1, xs2
    instruction |= (static_cast<uint32_t>(xd) << 14);
    instruction |= (static_cast<uint32_t>(xs1) << 13);
    instruction |= (static_cast<uint32_t>(xs2) << 12);

    // 设置opcode
    uint8_t opcode_low = (dis(gen) % 2 == 0) ? 0b00010 : 0b01010;
    uint8_t opcode_high = opcode_high_dis(gen);
    uint8_t opcode = (opcode_high << 5) | opcode_low;
    instruction |= opcode;

    return instruction;
}

// 辅助函数：检查func7是否有效
bool isValidFunc7(uint8_t func7)
{
    static const uint8_t validFunc7Values[] = {
        0b0000000, 0b0000001, 0b0000100, 0b0000101, 0b0000010, 0b0000011, 0b0010000,
        0b0011000, 0b0100000, 0b0101000, 0b0110000, 0b0110001, 0b0111001, 0b0111000,
        0b0111010, 0b1000000, 0b1000001, 0b1001000, 0b1001001, 0b1010000, 0b1010001,
        0b1011000, 0b1010010, 0b1011001, 0b1011010, 0b1011011};

    for (uint8_t value : validFunc7Values)
    {
        if (func7 == value)
            return true;
    }
    return false;
}

uint32_t generateValidInstruction()
{
    uint8_t func7;
    uint8_t xd, xs1, xs2;
    static std::random_device rd;
    static std::mt19937 gen(rd());

    do
    {
        func7 = std::uniform_int_distribution<>(0, 127)(gen);
    } while (!isValidFunc7(func7));

    // 根据func7设置xd, xs1, xs2
    switch (func7)
    {
        case 0b0000100:  // LK_LMEM
            xd = 1;
            xs1 = 1;
            xs2 = 0;
            break;
        case 0b0000011:  // RD_LMEM
            xd = 1;
            xs1 = 1;
            xs2 = 0;
            break;
        case 0b1011010:  // V_S_DRV
            xd = 1;
            xs1 = 0;
            xs2 = 1;
            break;
        default:
            xd = std::uniform_int_distribution<>(0, 1)(gen);
            xs1 = std::uniform_int_distribution<>(0, 1)(gen);
            xs2 = std::uniform_int_distribution<>(0, 1)(gen);
    }

    return generateInstruction(func7, xd, xs1, xs2);
}

Command generate_command(InstructionName inst_type,
                         uint64_t rs1val,
                         uint64_t rs2val,
                         bool xd,
                         bool xs1,
                         bool xs2)
{
    uint8_t func7 = encodeName(inst_type);
    auto instruction = generateInstruction(func7, xd, xs1, xs2);
    auto rawCommand = generateRawCommand(rs2val, rs1val, instruction);
    return decode(rawCommand.data());
}

// auto sc_main(int argc, char *argv[]) -> int {
//   CommandDecoder decoder;
//   sc_core::sc_start(0, sc_core::SC_NS);

//   uint32_t instruction = generateValidInstruction();
//   auto rawCommand = generateRawCommand(0x1234567890ABCDEF, 0xFEDCBA9876543210, instruction);
//   Command cmd = decoder.decode(rawCommand.data());
//   decoder.print(cmd);
//   return 0;
// }