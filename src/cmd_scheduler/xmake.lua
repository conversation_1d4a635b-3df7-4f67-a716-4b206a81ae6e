target("rsp_rob")
    add_files("rsp_rob.cpp")
    set_kind("static")
    add_deps("common")
    add_deps("systemc_logger")

target("test_rsp_rob")
    add_files("test/test_rsp_rob.cpp")
    set_kind("binary")
    add_deps("rsp_rob")

target("cmd_decoder")
    set_kind("static")
    add_deps("common")
    add_files("cmd_decoder.cpp")
    add_packages("magic_enum")
    add_deps("systemc_logger")



target("cmd_dispatch")
    set_kind("static")
    add_deps("common")
    add_deps("cmd_decoder")
    add_deps("scoreboard")
    add_files("cmd_dispatch.cpp")
    add_packages("magic_enum")
    add_deps("systemc_logger")


target("test_cmd_dispatch")
    set_kind("binary")
    add_deps("cmd_dispatch")
    add_packages("magic_enum")
    add_files("test/test_cmd_dispatch.cpp")


target("cmd_scheduler")
    set_kind("static")
    add_deps("common")
    add_deps("cmd_dispatch")
    add_deps("rsp_rob")
    add_deps("scoreboard")
    add_files("cmd_scheduler.cpp")
    add_packages("magic_enum")
    add_deps("systemc_logger")

target("test_cmd_scheduler")
    set_kind("static")
    add_deps("cmd_scheduler")
    add_files("test/test_cmd_scheduler.cpp")
    add_packages("magic_enum")

target("top")
    set_kind("binary")
    add_deps("cmd_scheduler")
    add_deps("test_cmd_scheduler")
    add_files("test/top.cpp")
