#include "../include/rsp_rob.h"
#include <algorithm>
#include "sysc/kernel/sc_event.h"
#include "sysc/kernel/sc_time.h"
#include "sysc/utils/sc_report.h"

void RSP_ROB::b_transport_wbq_rsp(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
{
    tlm::tlm_command cmd = trans.get_command();
    if (cmd == tlm::TLM_WRITE_COMMAND)
    {
        latest_wbq_rsp = *reinterpret_cast<wbq_rsp_info*>(trans.get_data_ptr());
        debug_sc("received wbq_rsp data");
        trans.set_response_status(tlm::TLM_OK_RESPONSE);
    }
    else
    {
        trans.set_response_status(tlm::TLM_COMMAND_ERROR_RESPONSE);
    }
}

void RSP_ROB::b_transport_disp_roflag(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
{
    tlm::tlm_command cmd = trans.get_command();
    if (cmd == tlm::TLM_WRITE_COMMAND)
    {
        rob_queue_event.notify();
        // 等待握手完成
        // while (!(disp_roflag_ready.read() && disp_roflag_valid.read())){
        //     wait(disp_roflag_ready.value_changed_event() |
        //     disp_roflag_valid.value_changed_event()); debug_sc("waiting for disp_roflag_ready and
        //     disp_roflag_valid handshake");
        // }
        auto* disp_roflag = reinterpret_cast<disp_roflag_info*>(trans.get_data_ptr());
        rob_queue.push(*disp_roflag);
        disp_roflag_event.notify();
        // 如果队列满了，返回错误
        if (rob_queue.size() > rob_queue_size_)
        {
            trans.set_response_status(tlm::TLM_GENERIC_ERROR_RESPONSE);
            error_sc("rob_queue is full,the capacity is %d", rob_queue_size_);
        }
        else
        {
            debug_sc("received disp_roflag data");
            trans.set_response_status(tlm::TLM_OK_RESPONSE);
        }
    }
    else
    {
        trans.set_response_status(tlm::TLM_COMMAND_ERROR_RESPONSE);
    }
}

void RSP_ROB::update_disp_roflag_ready()
{
    disp_roflag_ready.write(rob_queue.size() < rob_queue_size_);
    debug_sc("update_disp_roflag_ready: %d", rob_queue.size());
}

void RSP_ROB::wbq_rsp_valid_change_detector()
{
    wbq_rsp_valid_changed_event.notify();
}

void RSP_ROB::send_rspq_rsp(const rspq_rsp_info& rsp)
{
    tlm::tlm_generic_payload trans;
    sc_core::sc_time delay = sc_core::SC_ZERO_TIME;
    rspq_valid.write(true);
    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_data_ptr(reinterpret_cast<unsigned char*>(const_cast<rspq_rsp_info*>(&rsp)));
    trans.set_data_length(sizeof(rspq_rsp_info));
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
    debug_sc("send rspq_rsp data");
    rspq_socket->b_transport(trans, delay);
    if (trans.get_response_status() != tlm::TLM_OK_RESPONSE)
    {
        SC_REPORT_ERROR("RSP_ROB", "response status error");
    }
    rspq_valid.write(false);
}

void RSP_ROB::decoder_thread()
{
    while (true)
    {
        // 当rob_queue不空时，处理
        while (rob_queue.empty())
        {
            wait(disp_roflag_event);
        }
        // 开始处理
        disp_roflag_info disp_roflag = rob_queue.front();

        rob_queue.pop();

        // 从rolfag中得到 MSB->LSB sync_flag(1) fu_ena(1) fuid(FU_ID_WD) inst.xd(1)
        // inst.rd(5)
        auto sync_flag = disp_roflag.disp_roflag_data[NPUConfig::FU_ID_WD + 8 - 1];
        auto fu_ena = disp_roflag.disp_roflag_data[NPUConfig::FU_ID_WD + 8 - 2];
        auto fuid = disp_roflag.disp_roflag_data.range(NPUConfig::FU_ID_WD + 8 - 3, 6);
        auto inst_xd = disp_roflag.disp_roflag_data[5];
        auto inst_rd = disp_roflag.disp_roflag_data.range(4, 0);

        // 打印disp_roflag
        std::stringstream ss;
        ss << "[RSP_ROB] disp_roflag: sync_flag=" << sync_flag << ", fu_ena=" << fu_ena
           << ", fuid=" << fuid << ", inst_xd=" << inst_xd << ", inst_rd=" << inst_rd;
        info_sc(ss.str().c_str());
        // 判断是否全有效
        bool has_false = std::any_of(wbq_rsp_valid.begin(),
                                     wbq_rsp_valid.end(),
                                     [](const sc_core::sc_in<bool>& v) { return !v.read(); });
        debug_sc("[RSP_ROB] wbq_rsp_valid: %d", has_false);
        // ---------------------------判断阻塞--------------------------------
        bool is_blocked = false;
        // 堵塞条件1 : sync_flag :1 ,某个wbq_rsp_valid为0
        if ((sync_flag != 0U) && has_false)
        {
            debug_sc("[RSP_ROB] is blocked reason:There exists a FunctionUnit "
                     "where the Write-back Queue is empty");
            is_blocked = true;
        }
        // 堵塞条件2 : sync_flag :1 , inst_xd :1 , rspq_ready :0
        if ((sync_flag != 0U) && (inst_xd != 0U) && rspq_ready.read() == 0)
        {
            debug_sc("[RSP_ROB] is blocked reason: Response Queue is full");
            is_blocked = true;
        }
        // 堵塞条件3 : sync_flag :0 , fu_ena :1 ,inst_xd :1 ,( rspq_ready :0 ||
        // wbq_rsp_valid[fuid] :0)
        if (!sync_flag && (fu_ena != 0U) && (inst_xd != 0U))
        {
            if (wbq_rsp_valid[fuid]->read() == 0U)
            {
                debug_sc("[RSP_ROB] is blocked reason: Write-back Queue is empty");
                is_blocked = true;
            }
            if (rspq_ready.read() == 0U)
            {
                debug_sc("[RSP_ROB] is blocked reason: Response Queue is full");
                is_blocked = true;
            }
        }
        // 堵塞条件4 : sync_flag :0 , fu_ena :0 ,inst_xd :1 , rspq_ready :0
        if (!sync_flag && !fu_ena && (inst_xd != 0U) && !rspq_ready.read())
        {
            debug_sc("[RSP_ROB] is blocked reason: Response Queue is full");
            is_blocked = true;
        }

        if (is_blocked)
        {
            // 等待外界wbq_rsp_valid、rspq_ready变化
            if (sync_flag != 0U)
            {
                while (has_false)
                {
                    wait(wbq_rsp_valid_changed_event);
                    has_false =
                        std::any_of(wbq_rsp_valid.begin(),
                                    wbq_rsp_valid.end(),
                                    [](const sc_core::sc_in<bool>& v) { return !v.read(); });
                    if (!has_false)
                    {
                        is_blocked = false;
                        debug_sc("[RSP_ROB] is unblocked");
                        break;
                    }
                }
                if (inst_xd != 0U)
                {
                    // 堵塞解除条件：rspq_ready为1
                    while (true)
                    {
                        wait(rspq_ready->value_changed_event());
                        if (rspq_ready.read())
                        {
                            is_blocked = false;
                            debug_sc("[RSP_ROB] is unblocked");
                            break;
                        }
                    }
                }
            }
            else
            {
                if (inst_xd != 0U)
                {
                    if (fu_ena != 0U)
                    {
                        // 堵塞解除条件：rspq_ready为1或者wbq_rsp_valid[fuid]为1
                        while (true)
                        {
                            wait(rspq_ready->value_changed_event() |
                                 wbq_rsp_valid[fuid]->value_changed_event());
                            if (rspq_ready.read() || wbq_rsp_valid[fuid])
                            {
                                is_blocked = false;
                                debug_sc("[RSP_ROB] is unblocked");
                                break;
                            }
                        }
                    }
                    else
                    {
                        // 堵塞解除条件：rspq_ready为1
                        while (true)
                        {
                            wait(rspq_ready->value_changed_event());
                            if (rspq_ready.read())
                            {
                                is_blocked = false;
                                debug_sc("[RSP_ROB] is unblocked");
                                break;
                            }
                        }
                    }
                }
            }
        }
        // ---------------------------信号处理--------------------------------
        rspq_rsp_info rsp;
        // sync终止
        if (sync_flag != 0U)
        {
            if (inst_xd != 0U)
            {
                rsp.rspq_rsp_data = 0x01 << 5 | inst_rd;
                send_rspq_rsp(rsp);
            }
            wait(1, sc_core::SC_NS);
            term_sync.write(true);
            wait(0, sc_core::SC_NS);  // TODO: 这里需要等待一个时钟周期
            term_sync.write(false);
        }
        else
        {
            if (inst_xd != 0U)
            {
                if (fu_ena != 0U)
                {
                    auto rd_val = latest_wbq_rsp.wbq_rsp_data[fuid];
                    rsp.rspq_rsp_data = rd_val << 5 | inst_rd;
                }
                else
                {
                    rsp.rspq_rsp_data = 0x01 << 5 | inst_rd;
                }
                send_rspq_rsp(rsp);
            }
        }
        info_sc("[RSP_ROB] decoder_thread one round");
    }
}
