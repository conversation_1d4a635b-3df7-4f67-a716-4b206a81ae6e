#include "cmd_dispatch.h"

#include <chrono>
#include <cstdint>
#include <magic_enum.hpp>
#include <thread>
#include "cmd_decoder.h"
#include "npu_config.h"
#include "rsp_rob.h"
#include "scoreboard.h"
#include "utils/systemc_logger.h"

void CmdDispatch::cmd_queue_b_transport(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
{
    // 从trans中得到数据
    auto tlm_cmd = trans.get_command();
    if (tlm_cmd == tlm::TLM_WRITE_COMMAND)
    {
        recv_cmd = *reinterpret_cast<Command*>(trans.get_data_ptr());
        trans.set_response_status(tlm::TLM_OK_RESPONSE);
        cmd_received_event.notify();
        debug_sc("CmdDispatch received cmd");
    }
    else
    {
        trans.set_response_status(tlm::TLM_COMMAND_ERROR_RESPONSE);
    }
}

int CmdDispatch::check_block_reason()
{
    int reasons = BlockReason::NONE;
    if (is_rob_blocked())
    {
        reasons |= BlockReason::ROB;
    }
    if (is_sbd_blocked())
    {
        reasons |= BlockReason::SBD;
    }
    if (is_isq_blocked())
    {
        reasons |= BlockReason::ISQ;
    }
    if (is_sync_blocked())
    {
        reasons |= BlockReason::SYNC;
    }
    return reasons;
}

void CmdDispatch::cmd_process_thread()
{
    while (true)
    {
        wait();
        process_cmd();
    }
}

void CmdDispatch::process_cmd()
{
    debug_sc("CmdDispatch process_cmd");
    // 读取info_sbd
    read_info_sbd();
    //  Process command (invalid, blocked, active)
    if (is_command_invalid())
    {
        handle_invalid_command();
    }
    else if (is_command_blocked())
    {
        handle_blocked_command();
        handle_active_command();
    }
    else
    {
        handle_active_command();
    }
}

void CmdDispatch::read_info_sbd()
{
    tlm::tlm_generic_payload trans;
    sc_core::sc_time delay = sc_core::SC_ZERO_TIME;
    trans.set_command(tlm::TLM_READ_COMMAND);
    trans.set_data_ptr(reinterpret_cast<unsigned char*>(&info_sbd));
    trans.set_data_length(sizeof(scoreboardData));
    trans.set_streaming_width(sizeof(scoreboardData));
    trans.set_byte_enable_ptr(0);
    trans.set_dmi_allowed(false);
    info_sbd_socket->b_transport(trans, delay);

    try_sc(!trans.is_response_error(), "TLM-2 Response error from b_transport");
    debug_sc("CmdDispatch read info_sbd");
    debug_sc(scoreboard_data_string(info_sbd).c_str());
}

void CmdDispatch::send_to_rsp_rob(disp_roflag_info& roflag)
{
    tlm::tlm_generic_payload trans;
    sc_core::sc_time delay = sc_core::SC_ZERO_TIME;
    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_data_ptr(reinterpret_cast<unsigned char*>(&roflag));
    trans.set_data_length(sizeof(disp_roflag_info));
    trans.set_streaming_width(sizeof(disp_roflag_info));
    trans.set_byte_enable_ptr(0);
    trans.set_dmi_allowed(false);
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
    rob_rsp_socket->b_transport(trans, delay);
    try_sc(!trans.is_response_error(), "TLM-2 Response error from b_transport");
    info_sc("CmdDispatch send_to_rsp_rob done");
}

void CmdDispatch::send_update_transaction(const scoreboardUpdate& update)
{
    tlm::tlm_generic_payload trans;
    sc_core::sc_time delay = sc_core::SC_ZERO_TIME;
    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_data_ptr(reinterpret_cast<unsigned char*>(const_cast<scoreboardUpdate*>(&update)));
    trans.set_data_length(sizeof(scoreboardUpdate));
    trans.set_streaming_width(sizeof(scoreboardUpdate));
    trans.set_byte_enable_ptr(0);
    trans.set_dmi_allowed(false);
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
    info_sbd_socket->b_transport(trans, delay);
    if (trans.is_response_error())
    {
        SC_REPORT_ERROR("TLM-2", "Response error from b_transport");
    }
}

bool CmdDispatch::is_command_invalid()
{
    auto inst_type = decodeName(recv_cmd.func7);
    auto rs1_lmem_index = parseLmemAddr(recv_cmd.rs1val).lmem_index;
    auto rs2_lmem_index = parseLmemAddr(recv_cmd.rs2val).lmem_index;
    bool is_invalid = false;

    switch (inst_type)
    {
        case InstructionName::SYNC:
        case InstructionName::TLD_CFG:
        case InstructionName::TST_CFG:
        case InstructionName::TM_CFG:
        case InstructionName::BC_PRE:
        case InstructionName::MP_CFG:
        case InstructionName::VP_CFG:
        case InstructionName::VS_V_PRE:
            is_invalid = false;  // 这些指令永远有效
            break;

        case InstructionName::SW_CIMC:
            is_invalid = is_cimc_locked(rs1_lmem_index);
            break;
        case InstructionName::LK_LMEM:
        case InstructionName::WR_LMEM:
        case InstructionName::RD_LMEM:
        case InstructionName::TLD_DRV:
        case InstructionName::TST_DRV:
        case InstructionName::BC_DRV:
        case InstructionName::CONV_PRE:
        // case InstructionName::DWCONV_PRE:
        // TODO: ISA中不存在MTMM_PRE
        // case InstructionName::MTMM_PRE:
        case InstructionName::VV_V_PRE:
            is_invalid = is_lmem_locked(rs1_lmem_index);
            break;

        case InstructionName::UNLK_LMEM:
            is_invalid = !is_lmem_locked(rs1_lmem_index);
            break;

        case InstructionName::MOV_DRV:
        case InstructionName::TRANS_DRV:
        case InstructionName::CONV_DRV:
        case InstructionName::DWCONV_DRV:
        // TODO: ISA中不存在MTMM_DRV
        // case InstructionName::MTMM_DRV:
        case InstructionName::VV_V_DRV:
        case InstructionName::VS_V_DRV:
        case InstructionName::V_V_DRV:
            is_invalid = is_lmem_locked(rs1_lmem_index) || is_lmem_locked(rs2_lmem_index);
            break;

        case InstructionName::V_S_DRV:
            is_invalid = is_lmem_locked(rs2_lmem_index);
            break;
        default:
            // 未知指令类型，视为无效
            is_invalid = true;
    }

    debug_sc(("Instruction " + std::string(magic_enum::enum_name(inst_type)) + " pointing to LMEM" +
              std::to_string(rs1_lmem_index) + " and LMEM" + std::to_string(rs2_lmem_index) +
              (is_invalid ? " is invalid" : " is valid"))
                 .c_str());
    debug_is_invalid = is_invalid;
    return is_invalid;
}

bool CmdDispatch::is_cimc_locked(uint32_t lmem_index)
{
    // 首先判断lmem_index是否为CIMC
    if (lmem_index == NPUConfig::LMEM_NUM - 1)
    {
        return info_sbd.lmem_entries[lmem_index].status == LMemStatus::LOCK;
    }
    return false;
}

bool CmdDispatch::is_lmem_locked(uint32_t lmem_index)
{
    return info_sbd.lmem_entries[lmem_index].status == LMemStatus::LOCK;
}

void CmdDispatch::handle_invalid_command()
{
    // debug_sc("CmdDispatch handle_invalid_command");
}

bool CmdDispatch::is_command_blocked()
{
    bool is_blocked = is_rob_blocked() || is_sbd_blocked() || is_isq_blocked() || is_sync_blocked();
    debug_is_blocked = is_blocked;
    return is_blocked;
}

bool CmdDispatch::is_rob_blocked()
{
    auto inst_type = decodeName(recv_cmd.func7);
    auto inst_xd = recv_cmd.xd;
    if (inst_type == InstructionName::SYNC)
    {
        return !rob_rsp_ready.read();
        debug_sc("is_rob_blocked: SYNC is blocked,rob_queue is full");
    }
    else if (inst_xd)
    {
        return !rob_rsp_ready.read();
        debug_sc("is_rob_blocked: XD 1,and rob_queue is full");
    }
    return false;
}

bool CmdDispatch::is_sbd_blocked()
{
    auto inst_type = decodeName(recv_cmd.func7);
    auto rs1_lmem_index = parseLmemAddr(recv_cmd.rs1val).lmem_index;
    auto rs2_lmem_index = parseLmemAddr(recv_cmd.rs2val).lmem_index;
    bool is_blocked = false;

    switch (inst_type)
    {
        case InstructionName::SYNC:
        case InstructionName::UNLK_LMEM:
        case InstructionName::TLD_CFG:
        case InstructionName::TST_CFG:
        case InstructionName::TM_CFG:
        case InstructionName::BC_PRE:
        case InstructionName::MP_CFG:
        case InstructionName::VP_CFG:
        case InstructionName::VS_V_PRE:
            // TODO: ISA中不存在 case InstructionName::V_S_POST:
            is_blocked = false;
            break;

        case InstructionName::SW_CIMC:
            is_blocked = is_cimc_busy(rs1_lmem_index);
            break;

        case InstructionName::LK_LMEM:
        case InstructionName::WR_LMEM:
        case InstructionName::RD_LMEM:
        case InstructionName::TLD_DRV:
        case InstructionName::TST_DRV:
        case InstructionName::BC_DRV:
        case InstructionName::CONV_PRE:
        // case InstructionName::DWCONV_PRE:
        // TODO: ISA中不存在MTMM_PRE
        // case InstructionName::MTMM_PRE:
        case InstructionName::VV_V_PRE:
            is_blocked = is_lmem_busy(rs1_lmem_index);
            break;

        case InstructionName::MOV_DRV:
        case InstructionName::TRANS_DRV:
        case InstructionName::CONV_DRV:
        case InstructionName::DWCONV_DRV:
        // TODO: ISA中不存在MTMM_DRV
        // case InstructionName::MTMM_DRV:
        case InstructionName::VV_V_DRV:
        case InstructionName::VS_V_DRV:
        case InstructionName::V_V_DRV:
            is_blocked = is_lmem_busy(rs1_lmem_index) || is_lmem_busy(rs2_lmem_index);
            break;

        case InstructionName::V_S_DRV:
            is_blocked = is_lmem_busy(rs2_lmem_index);
            break;

        default:
            error_sc("CmdDispatch is_sbd_blocked unknown inst_type");
            is_blocked = true;
    }

    debug_sc(("CmdDispatch is_sbd_blocked: Instruction " +
              std::string(magic_enum::enum_name(inst_type)) + " is " +
              (is_blocked ? "blocked" : "not blocked"))
                 .c_str());

    return is_blocked;
}

bool CmdDispatch::is_lmem_busy(uint32_t lmem_index)
{
    return info_sbd.lmem_entries[lmem_index].status == LMemStatus::BUSY;
}

bool CmdDispatch::is_cimc_busy(uint32_t lmem_index)
{
    if (lmem_index == NPUConfig::LMEM_NUM - 1)
    {
        return info_sbd.lmem_entries[lmem_index].status == LMemStatus::BUSY;
    }
    return false;
}

bool CmdDispatch::is_sync_blocked()
{
    return sync_wait_flag;
}

bool CmdDispatch::is_isq_blocked()
{
    auto inst_type = decodeName(recv_cmd.func7);
    auto inst_group = decodeGroup(recv_cmd.func7);
    switch (inst_group)
    {
        case InstructionGroup::MatrixProcessing:
            return !isq_cmd_ready[FunctionUnitType::MPU].read();
        case InstructionGroup::VectorProcessing:
            return !isq_cmd_ready[FunctionUnitType::VPU].read();
        case InstructionGroup::TensorManipulation:
            return !isq_cmd_ready[FunctionUnitType::TMU].read();
        case InstructionGroup::DataTransfer:
            if (inst_type == InstructionName::TLD_CFG || inst_type == InstructionName::TLD_DRV)
            {
                return !isq_cmd_ready[FunctionUnitType::TLU].read();
            }
            else if (inst_type == InstructionName::TST_CFG || inst_type == InstructionName::TST_DRV)
            {
                return !isq_cmd_ready[FunctionUnitType::TSU].read();
            }
            else
            {
                error_sc("CmdDispatch is_isq_blocked unkown inst_type");
                return true;
            }
        case InstructionGroup::Control:
            //  所有function unit 都不ready
            bool is_all_fu_blocked =
                std::any_of(isq_cmd_ready.begin(),
                            isq_cmd_ready.end(),
                            [](const sc_core::sc_in<bool>& v) { return !v.read(); });
            switch (inst_type)
            {
                case InstructionName::SYNC:
                    return is_all_fu_blocked;
                case InstructionName::WR_LMEM:
                case InstructionName::RD_LMEM:
                    return !isq_cmd_ready[FunctionUnitType::LMU].read();
                default:
                    return false;
            }
            error_sc("CmdDispatch is_isq_blocked unkown inst_group");
            return true;
    }
}

void CmdDispatch::handle_term_sync_method()
{
    if (term_sync.read() && sync_wait_flag)
    {
        sync_wait_flag = false;
        info_sc("Sync wait flag cleared due to term_sync signal");
    }
}

void CmdDispatch::handle_blocked_command()
{
    info_sc("CmdDispatch handle_blocked_command");
    int reasons;
    do
    {
        reasons = check_block_reason();

        if (reasons & BlockReason::ROB)
        {
            debug_sc("CmdDispatch handle_blocked_command: ROB is blocked");
            wait(rob_rsp_ready.value_changed_event());
        }
        if (reasons & BlockReason::ISQ)
        {
            debug_sc("CmdDispatch handle_blocked_command: ISQ is blocked");
            wait(isq_cmd_ready[FunctionUnitType::MPU].value_changed_event() |
                 isq_cmd_ready[FunctionUnitType::VPU].value_changed_event() |
                 isq_cmd_ready[FunctionUnitType::TMU].value_changed_event() |
                 isq_cmd_ready[FunctionUnitType::TLU].value_changed_event() |
                 isq_cmd_ready[FunctionUnitType::TSU].value_changed_event() |
                 isq_cmd_ready[FunctionUnitType::LMU].value_changed_event());
        }
        if (reasons & BlockReason::SYNC)
        {
            debug_sc("CmdDispatch handle_blocked_command: SYNC is blocked");
            wait(term_sync.value_changed_event());
        }
        if (reasons & BlockReason::SBD)
        {
            debug_sc("CmdDispatch handle_blocked_command: SBD is blocked");
            wait(sbd_update_event);
            read_info_sbd();
        }
    } while (reasons != BlockReason::NONE);
    info_sc("CmdDispatch handle_blocked_command done");
}

void CmdDispatch::handle_active_command()
{
    info_sc("CmdDispatch handle_active_command");
    auto inst_type = decodeName(recv_cmd.func7);

    // 1. 处理sync陷入
    handle_sync_trap(inst_type);

    // 2. 处理rob写入
    handle_rob_write(inst_type);

    // 3. 处理isq写入
    handle_isq_write(inst_type);

    // 4. 处理sbd更新
    handle_sbd_update(inst_type);
}

void CmdDispatch::handle_sync_trap(InstructionName inst_type)
{
    if (inst_type == InstructionName::SYNC && !sync_wait_flag)
    {
        sync_wait_flag = true;
        info_sc("Sync wait flag set due to SYNC instruction");
    }
}

void CmdDispatch::handle_rob_write(InstructionName inst_type)
{
    if (should_write_rob(inst_type))
    {
        debug_sc("write to rob queue");
        auto roflag = create_roflag(inst_type);
        send_to_rsp_rob(roflag);
    }
}

bool CmdDispatch::should_write_rob(InstructionName inst_type)
{
    if (inst_type == InstructionName::SYNC)
    {
        return true;
    }
    auto inst_xd = recv_cmd.xd;
    if (inst_xd)
    {
        return true;
    }
    return false;
}

auto CmdDispatch::create_roflag(InstructionName inst_type) -> disp_roflag_info
{
    disp_roflag_info roflag;

    // 初始化roflag
    roflag.disp_roflag_data[8 + NPUConfig::FU_ID_WD - 1] = 0;
    roflag.disp_roflag_data[8 + NPUConfig::FU_ID_WD - 2] = 1;
    roflag.disp_roflag_data(6 + NPUConfig::FU_ID_WD - 1, 6) = FunctionUnitType::INVALID;
    roflag.disp_roflag_data[5] = recv_cmd.xd;
    roflag.disp_roflag_data(4, 0) = recv_cmd.rd;

    // 处理特殊情况
    if (inst_type == InstructionName::SYNC)
    {
        roflag.disp_roflag_data[8 + NPUConfig::FU_ID_WD - 1] = 1;
        roflag.disp_roflag_data[8 + NPUConfig::FU_ID_WD - 2] = 0;
    }
    else if (inst_type == InstructionName::SW_CIMC || inst_type == InstructionName::LK_LMEM ||
             inst_type == InstructionName::UNLK_LMEM)
    {
        roflag.disp_roflag_data[8 + NPUConfig::FU_ID_WD - 2] = 0;
    }
    else
    {
        // 使用get_target_fu获取目标功能单元
        FunctionUnitType target_fu = get_target_fu(inst_type);
        if (target_fu != FunctionUnitType::INVALID)
        {
            roflag.disp_roflag_data(6 + NPUConfig::FU_ID_WD - 1, 6) = target_fu;
        }
        else
        {
            error_sc("CmdDispatch create_roflag: Invalid function unit for "
                     "instruction type %s",
                     magic_enum::enum_name(inst_type).data());
        }
    }

    // 打印roflag {1',1',fuid',inst.xd,inst.rd }
    std::stringstream ss;
    ss << "roflag: {" << roflag.disp_roflag_data[8 + NPUConfig::FU_ID_WD - 1] << ","
       << roflag.disp_roflag_data[8 + NPUConfig::FU_ID_WD - 2] << ","
       << magic_enum::enum_name(static_cast<FunctionUnitType>(
              roflag.disp_roflag_data(6 + NPUConfig::FU_ID_WD - 1, 6).to_uint()))
       << "," << roflag.disp_roflag_data[5] << "," << roflag.disp_roflag_data(4, 0) << "}";
    info_sc(ss.str().c_str());

    return roflag;
}

void CmdDispatch::handle_isq_write(InstructionName inst_type)
{
    if (should_write_isq(inst_type))
    {
        debug_sc("write to isq");

        // 处理SYNC指令
        if (inst_type == InstructionName::SYNC)
        {
            for (int i = 0; i < NPUConfig::FU_NUM; ++i)
            {
                isq_cmd.set_rs2val(i, recv_cmd.rs2val);
                isq_cmd.set_rs1val(i, recv_cmd.rs1val);
                isq_cmd.set_inst(i,
                                 (recv_cmd.xd << 9) | (recv_cmd.xs1 << 8) | (recv_cmd.xs2 << 7) |
                                     recv_cmd.opcode);
                isq_cmd_valid[i].write(true);
            }
            debug_sc("SYNC: Write to all ISQs and set all valid signals");
        }
        else
        {
            // 处理其他指令
            FunctionUnitType target_fu = get_target_fu(inst_type);
            if (target_fu != FunctionUnitType::INVALID)
            {
                size_t fu_index = static_cast<size_t>(target_fu);
                isq_cmd.set_rs2val(fu_index, recv_cmd.rs2val);
                isq_cmd.set_rs1val(fu_index, recv_cmd.rs1val);
                isq_cmd.set_inst(fu_index,
                                 (recv_cmd.xd << 9) | (recv_cmd.xs1 << 8) | (recv_cmd.xs2 << 7) |
                                     recv_cmd.opcode);
                isq_cmd_valid[fu_index].write(true);
                debug_sc("Write to ISQ of %s and set valid signal",
                         magic_enum::enum_name(target_fu).data());
            }
            else
            {
                error_sc("Invalid function unit for instruction type: %s",
                         magic_enum::enum_name(inst_type).data());
            }
        }
        send_to_issue_queue();
    }
}

void CmdDispatch::send_to_issue_queue()
{
    tlm::tlm_generic_payload payload;
    sc_core::sc_time delay = sc_core::SC_ZERO_TIME;
    payload.set_command(tlm::TLM_WRITE_COMMAND);
    payload.set_address(0);
    payload.set_data_ptr(reinterpret_cast<unsigned char*>(&isq_cmd));
    payload.set_data_length(sizeof(ISQ_CMD));
    payload.set_streaming_width(sizeof(ISQ_CMD));
    payload.set_byte_enable_ptr(nullptr);
    payload.set_byte_enable_length(0);
    payload.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
    isq_cmd_socket->b_transport(payload, delay);
    try_sc(payload.get_response_status() == tlm::TLM_OK_RESPONSE,
           "ISQ command socket transport failed");
    // 清空isq_cmd_valid
    for (auto& valid : isq_cmd_valid)
    {
        valid.write(false);
    }
}

bool CmdDispatch::should_write_isq(InstructionName inst_type)
{
    if (inst_type == InstructionName::SW_CIMC || inst_type == InstructionName::LK_LMEM ||
        inst_type == InstructionName::UNLK_LMEM)
    {
        return false;
    }
    return true;
}

FunctionUnitType CmdDispatch::get_target_fu(InstructionName inst_type)
{
    auto inst_group = decodeGroup(recv_cmd.func7);
    switch (inst_group)
    {
        case InstructionGroup::Control:
            if (inst_type == InstructionName::WR_LMEM || inst_type == InstructionName::RD_LMEM)
            {
                return FunctionUnitType::LMU;
            }
            break;
        case InstructionGroup::DataTransfer:
            if (inst_type == InstructionName::TLD_CFG || inst_type == InstructionName::TLD_DRV)
            {
                return FunctionUnitType::TLU;
            }
            else if (inst_type == InstructionName::TST_CFG || inst_type == InstructionName::TST_DRV)
            {
                return FunctionUnitType::TSU;
            }
            break;
        case InstructionGroup::MatrixProcessing:
            return FunctionUnitType::MPU;
        case InstructionGroup::TensorManipulation:
            return FunctionUnitType::TMU;
        case InstructionGroup::VectorProcessing:
            return FunctionUnitType::VPU;
    }
    return FunctionUnitType::INVALID;
}

void CmdDispatch::handle_sbd_update(InstructionName inst_type)
{
    scoreboardUpdate update;
    auto rs1_lmem_index = parseLmemAddr(recv_cmd.rs1val).lmem_index;
    auto rs2_lmem_index = parseLmemAddr(recv_cmd.rs2val).lmem_index;

    switch (inst_type)
    {
        case InstructionName::SW_CIMC:
            update_cimc(update, rs1_lmem_index);
            break;
        case InstructionName::LK_LMEM:
            update_lmem_status(update, rs1_lmem_index, LMemStatus::LOCK);
            break;
        case InstructionName::UNLK_LMEM:
            update_lmem_status(update, rs1_lmem_index, LMemStatus::IDLE);
            break;
        case InstructionName::WR_LMEM:
        case InstructionName::RD_LMEM:
        case InstructionName::TLD_DRV:
        case InstructionName::TST_DRV:
        case InstructionName::BC_DRV:
            update_single_lmem(update, rs1_lmem_index, get_target_fu(inst_type), 0b001);
            break;
        case InstructionName::MOV_DRV:
        case InstructionName::TRANS_DRV:
        case InstructionName::VS_V_DRV:
            update_double_lmem_simple(
                update, rs1_lmem_index, rs2_lmem_index, get_target_fu(inst_type));
            break;
        case InstructionName::CONV_DRV:
        case InstructionName::DWCONV_DRV:
        case InstructionName::VV_V_DRV:
        case InstructionName::V_V_DRV:
            update_double_lmem_conditional(
                update, rs1_lmem_index, rs2_lmem_index, get_target_fu(inst_type));
            break;
        case InstructionName::V_S_DRV:
            update_single_lmem(update, rs2_lmem_index, FunctionUnitType::VPU, 0b010);
            break;
        case InstructionName::CONV_PRE:
        // case InstructionName::DWCONV_PRE:
        case InstructionName::VV_V_PRE:
            update_single_lmem(
                update, rs1_lmem_index, get_target_fu(inst_type), 0b100, LMemStatus::READY);
            break;
        default:
            // 其他指令不需要更新scoreboard
            info_sc("CmdDispatch handle_sbd_update: No update for instruction type %s",
                    magic_enum::enum_name(inst_type).data());
            return;
    }
    info_sc("CmdDispatch handle_sbd_update: Update scoreboard for instruction "
            "type %s",
            magic_enum::enum_name(inst_type).data());
    send_update_transaction(update);
}

void CmdDispatch::update_cimc(scoreboardUpdate& update, uint32_t lmem_index)
{
    update.lmem_id = 1 << lmem_index;
    update.update_cimc_mode = 1;
    update.lmem_entries[lmem_index].cimc_mode = recv_cmd.rs2val;
}

void CmdDispatch::update_lmem_status(scoreboardUpdate& update,
                                     uint32_t lmem_index,
                                     LMemStatus status)
{
    update.lmem_id = 1 << lmem_index;
    update.update_status = 1;
    update.lmem_entries[lmem_index].status = status;
}

void CmdDispatch::update_single_lmem(scoreboardUpdate& update,
                                     uint32_t lmem_index,
                                     FunctionUnitType fu_type,
                                     uint8_t ch_util,
                                     LMemStatus status)
{
    update.lmem_id = 1 << lmem_index;
    update.update_status = 1;
    update.update_fu_id = 1;
    update.update_ch_util = 1;
    if (fu_type == FunctionUnitType::LMU)
    {
        update.update_ch_util = 0;
    }
    update.lmem_entries[lmem_index].status = status;
    update.lmem_entries[lmem_index].fu_id = fu_type;
    update.lmem_entries[lmem_index].ch_util = ch_util;
}

void CmdDispatch::update_double_lmem_simple(scoreboardUpdate& update,
                                            uint32_t rs1_lmem_index,
                                            uint32_t rs2_lmem_index,
                                            FunctionUnitType fu_type)
{
    update.lmem_id = (1 << rs1_lmem_index) | (1 << rs2_lmem_index);
    update.update_status = 1;
    update.update_fu_id = 1;
    update.update_ch_util = 1;

    if (rs1_lmem_index == rs2_lmem_index)
    {
        update.lmem_entries[rs1_lmem_index].status = LMemStatus::BUSY;
        update.lmem_entries[rs1_lmem_index].fu_id = fu_type;
        update.lmem_entries[rs1_lmem_index].ch_util = 0b011;
    }
    else
    {
        update.lmem_entries[rs1_lmem_index].status = LMemStatus::BUSY;
        update.lmem_entries[rs1_lmem_index].fu_id = fu_type;
        update.lmem_entries[rs1_lmem_index].ch_util = 0b001;

        update.lmem_entries[rs2_lmem_index].status = LMemStatus::BUSY;
        update.lmem_entries[rs2_lmem_index].fu_id = fu_type;
        update.lmem_entries[rs2_lmem_index].ch_util = 0b010;
    }
}

void CmdDispatch::update_double_lmem_conditional(scoreboardUpdate& update,
                                                 uint32_t rs1_lmem_index,
                                                 uint32_t rs2_lmem_index,
                                                 FunctionUnitType fu_type)
{
    update.lmem_id = (1 << rs1_lmem_index) | (1 << rs2_lmem_index);
    update.update_status = 1;
    update.update_fu_id = 1;
    update.update_ch_util = 1;

    auto update_single = [&](uint32_t lmem_index, uint8_t base_ch_util)
    {
        LMemStatus current_status =
            static_cast<LMemStatus>(info_sbd.lmem_entries[lmem_index].status.to_uint());
        FunctionUnitType current_fu_id =
            static_cast<FunctionUnitType>(info_sbd.lmem_entries[lmem_index].fu_id.to_uint());

        update.lmem_entries[lmem_index].status = LMemStatus::BUSY;
        update.lmem_entries[lmem_index].fu_id = fu_type;

        if (current_status == LMemStatus::READY && current_fu_id == fu_type)
        {
            update.lmem_entries[lmem_index].ch_util = base_ch_util | 0b100;
        }
        else
        {
            update.lmem_entries[lmem_index].ch_util = base_ch_util;
        }
    };

    if (rs1_lmem_index == rs2_lmem_index)
    {
        update_single(rs1_lmem_index, 0b011);
    }
    else
    {
        update_single(rs1_lmem_index, 0b001);
        update_single(rs2_lmem_index, 0b010);
    }
}

// for external module get cmd
void CmdDispatch::get_cmd(Command& cmd)
{
    cmd = recv_cmd;
}
