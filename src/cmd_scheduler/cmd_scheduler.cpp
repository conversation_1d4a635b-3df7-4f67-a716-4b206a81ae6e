#include "cmd_scheduler.h"

CmdScheduler::CmdScheduler(const sc_core::sc_module_name& name, uint8_t rob_queue_size)
    : sc_module(name), m_cmd_dispatch("m_cmd_dispatch"), m_rsp_rob("m_rsp_rob", rob_queue_size)
{
    // 连接内部模块
    m_cmd_dispatch.rob_rsp_socket.bind(m_rsp_rob.disp_roflag_socket);

    // 连接内部 disp_roflag 信号
    m_cmd_dispatch.rob_rsp_valid(m_disp_roflag_valid);
    m_cmd_dispatch.rob_rsp_ready(m_disp_roflag_ready);
    m_rsp_rob.disp_roflag_valid(m_disp_roflag_valid);
    m_rsp_rob.disp_roflag_ready(m_disp_roflag_ready);
    m_rsp_rob.term_sync(m_term_sync);
    m_cmd_dispatch.term_sync(m_term_sync);
    // 连接外部信号到内部模块
    m_cmd_dispatch.cmd_queue_valid(cmd_queue_valid);
    m_cmd_dispatch.cmd_queue_ready(cmd_queue_ready);
    m_rsp_rob.rspq_valid(rspq_valid);
    m_rsp_rob.rspq_ready(rspq_ready);
    for (int i = 0; i < NPUConfig::FU_NUM; ++i)
    {
        m_cmd_dispatch.isq_cmd_valid[i](isq_cmd_valid[i]);
        m_cmd_dispatch.isq_cmd_ready[i](isq_cmd_ready[i]);
        m_rsp_rob.wbq_rsp_valid[i](wbq_rsp_valid[i]);
        m_rsp_rob.wbq_rsp_ready[i](wbq_rsp_ready[i]);
    }
}
