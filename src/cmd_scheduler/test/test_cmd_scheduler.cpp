#include "test_cmd_scheduler.h"
#include <magic_enum.hpp>
#include "sysc/kernel/sc_time.h"

TestCmdScheduler::TestCmdScheduler(const sc_core::sc_module_name& name) : sc_module(name)
{
    isq_cmd_socket.register_b_transport(this, &TestCmdScheduler::b_transport_isq_cmd);
    rspq_socket.register_b_transport(this, &TestCmdScheduler::b_transport_rspq);
}

void TestCmdScheduler::b_transport_isq_cmd(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
{
    debug_sc("TestCmdScheduler b_transport_isq_cmd triggered");
    trans.set_response_status(tlm::TLM_OK_RESPONSE);
}

void TestCmdScheduler::b_transport_rspq(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
{
    debug_sc("TestCmdScheduler b_transport_rspq triggered");
    trans.set_response_status(tlm::TLM_OK_RESPONSE);
}

uint64_t TestCmdScheduler::generate_lmem_addr(uint32_t lmem_index, uint32_t lmem_offset)
{
    // 确保lmem_index在有效范围
    if (lmem_index >= NPUConfig::FU_NUM)
    {
        SC_REPORT_ERROR("TLM-2", "LMEM index out of range");
    }
    uint64_t addr = 0;
    addr |= lmem_offset << 5;
    addr |= lmem_index << (NPUConfig::LMEM_OFFSET + 5);
    // 最低五位为0
    addr &= ~0x1full;
    return addr;
}

void TestCmdScheduler::send_cmd()
{
    Command cmd;
    uint32_t instruction = generateValidInstruction();
    static std::random_device rd_seed;
    static std::mt19937 gen(rd_seed());
    static std::uniform_int_distribution<> dis(0, NPUConfig::LMEM_NUM - 1);
    static std::uniform_int_distribution<> lmem_offset_dis(0, (1 << NPUConfig::LMEM_OFFSET) - 1);
    uint64_t rs1val = generate_lmem_addr(dis(gen), lmem_offset_dis(gen));
    uint64_t rs2val = generate_lmem_addr(dis(gen), lmem_offset_dis(gen));
    auto rawCommand = generateRawCommand(rs2val, rs1val, instruction);
    cmd = decode(rawCommand.data());
    // print_cmd(cmd);
    tlm::tlm_generic_payload trans;
    trans.set_data_ptr(reinterpret_cast<unsigned char*>(&cmd));
    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
    sc_core::sc_time delay = sc_core::SC_ZERO_TIME;
    cmd_input_socket->b_transport(trans, delay);
}

void TestCmdScheduler::send_cmd(InstructionName inst_type,
                                uint64_t rs1_lmem_index,
                                uint64_t rs2_lmem_index,
                                bool xd,
                                bool xs1,
                                bool xs2)
{
    static std::random_device rd_seed;
    static std::mt19937 gen(rd_seed());
    static std::uniform_int_distribution<> dis(0, (1 << NPUConfig::LMEM_OFFSET) - 1);
    uint64_t rs1val = generate_lmem_addr(rs1_lmem_index, dis(gen));
    uint64_t rs2val = generate_lmem_addr(rs2_lmem_index, dis(gen));
    Command cmd = generate_command(inst_type, rs1val, rs2val, xd, xs1, xs2);
    // print_cmd(cmd);
    tlm::tlm_generic_payload trans;
    trans.set_data_ptr(reinterpret_cast<unsigned char*>(&cmd));
    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
    sc_core::sc_time delay = sc_core::SC_ZERO_TIME;
    cmd_input_socket->b_transport(trans, delay);
    try_sc(trans.is_response_ok(), "cmd_input_socket->b_transport failed");
}
void TestCmdScheduler::send_invalid_command(InstructionName inst_type,
                                            uint64_t rs1_lmem_index,
                                            uint64_t rs2_lmem_index,
                                            LMemStatus lmem_status1,
                                            LMemStatus lmem_status2)
{
    info_sbd->set_scoreboard_state(rs1_lmem_index, lmem_status1);
    info_sbd->set_scoreboard_state(rs2_lmem_index, lmem_status2);
    send_cmd(inst_type, rs1_lmem_index, rs2_lmem_index, true, true, true);
}
void TestCmdScheduler::test_invalid_command(bool should_be_invalid,
                                            InstructionName inst_type,
                                            uint32_t rs1_lmem_index,
                                            uint32_t rs2_lmem_index,
                                            LMemStatus lmem_status1,
                                            LMemStatus lmem_status2)
{
    info_sbd->set_scoreboard_state(rs1_lmem_index, lmem_status1);
    info_sbd->set_scoreboard_state(rs2_lmem_index, lmem_status2);
    send_cmd(inst_type, rs1_lmem_index, rs2_lmem_index, true, true, true);

    // 检查命令是否被正确识别为无效
    wait(5, sc_core::SC_NS);

    // 这里需要一个方法来验证命令是否被识别为无效，可能需要在CmdDispatch中添加一个标志或计数器
    bool is_invalid = cmd_sched->m_cmd_dispatch.debug_is_invalid;
    std::stringstream ss;
    ss << "测试无效指令: " << magic_enum::enum_name(inst_type) << std::endl;
    ss << "  LMEM索引: rs1=" << rs1_lmem_index << ", rs2=" << rs2_lmem_index << std::endl;
    ss << "  预期结果: " << (should_be_invalid ? "无效" : "有效") << std::endl;
    ss << "  实际结果: " << (is_invalid ? "无效" : "有效") << std::endl;

    if (is_invalid == should_be_invalid)
    {
        ss << "测试结果: 通过 ✓" << std::endl;
    }
    else
    {
        ss << "测试结果: 失败 ✗" << std::endl;
    }
    ss << "----------------------------------------" << std::endl;
    info_sc(ss.str().c_str());
    // 重置状态
    info_sbd->reset();
}

void TestCmdScheduler::test_all_invalid_commands()
{
    // 定义测试用例结构
    struct TestCase
    {
        InstructionName inst;
        bool should_be_invalid;
        uint32_t rs1_lmem_index;
        uint32_t rs2_lmem_index;
        LMemStatus lmem_status1;
        LMemStatus lmem_status2;
    };

    // 避免堵塞条件
    for (auto& isq_ready : isq_cmd_ready)
    {
        isq_ready.write(true);
    }

    for (auto& wbq_valid : wbq_rsp_valid)
    {
        wbq_valid.write(true);
    }
    rspq_ready.write(true);
    cmd_queue_valid.write(true);
    wait(sc_core::SC_ZERO_TIME);
    // 定义测试用例
    std::vector<TestCase> test_cases = {
        // Control指令
        {InstructionName::SYNC, false, 0, 0, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::SW_CIMC,
         true,
         NPUConfig::LMEM_NUM - 1,
         0,
         LMemStatus::LOCK,
         LMemStatus::IDLE},
        {InstructionName::SW_CIMC, false, 0, 1, LMemStatus::LOCK, LMemStatus::IDLE},
        {InstructionName::LK_LMEM, true, 0, 1, LMemStatus::LOCK, LMemStatus::LOCK},
        {InstructionName::LK_LMEM, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::UNLK_LMEM, true, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::UNLK_LMEM, false, 0, 1, LMemStatus::LOCK, LMemStatus::IDLE},
        {InstructionName::WR_LMEM, true, 0, 1, LMemStatus::LOCK, LMemStatus::IDLE},
        {InstructionName::WR_LMEM, false, 0, 0, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::RD_LMEM, true, 0, 1, LMemStatus::LOCK, LMemStatus::IDLE},
        {InstructionName::RD_LMEM, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},

        // Data Transfer指令
        {InstructionName::TLD_CFG, false, 0, 0, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::TLD_DRV, true, 0, 1, LMemStatus::LOCK, LMemStatus::IDLE},
        {InstructionName::TLD_DRV, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::TST_CFG, false, 0, 0, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::TST_DRV, true, 0, 1, LMemStatus::LOCK, LMemStatus::IDLE},
        {InstructionName::TST_DRV, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},

        // Tensor Manipulation指令
        {InstructionName::TM_CFG, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::BC_PRE, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::BC_DRV, true, 0, 1, LMemStatus::LOCK, LMemStatus::IDLE},
        {InstructionName::BC_DRV, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::MOV_DRV, true, 0, 1, LMemStatus::IDLE, LMemStatus::LOCK},
        {InstructionName::MOV_DRV, true, 1, 2, LMemStatus::LOCK, LMemStatus::IDLE},
        {InstructionName::MOV_DRV, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::TRANS_DRV, true, 0, 1, LMemStatus::LOCK, LMemStatus::LOCK},
        {InstructionName::TRANS_DRV, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},

        // Matrix Processing指令
        {InstructionName::MP_CFG, false, 0, 0, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::CONV_PRE, true, 0, 1, LMemStatus::LOCK, LMemStatus::IDLE},
        {InstructionName::CONV_PRE, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::CONV_DRV, true, 0, 1, LMemStatus::LOCK, LMemStatus::LOCK},
        {InstructionName::CONV_DRV, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        // {InstructionName::DWCONV_PRE, true, 0, 0, LMemStatus::LOCK, LMemStatus::IDLE},
        // {InstructionName::DWCONV_PRE, false, 0, 0, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::DWCONV_DRV, true, 0, 1, LMemStatus::LOCK, LMemStatus::LOCK},
        {InstructionName::DWCONV_DRV, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        // {InstructionName::MTMM_PRE, true, 0, 0, LMemStatus::LOCK, LMemStatus::IDLE},
        // {InstructionName::MTMM_PRE, false, 0, 0, LMemStatus::IDLE, LMemStatus::IDLE},
        // {InstructionName::MTMM_DRV, true, 0, 0, LMemStatus::LOCK, LMemStatus::LOCK},
        // {InstructionName::MTMM_DRV, false, 0, 0, LMemStatus::IDLE, LMemStatus::IDLE},

        // Vector Processing指令
        {InstructionName::VP_CFG, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::VV_V_PRE, true, 0, 1, LMemStatus::LOCK, LMemStatus::IDLE},
        {InstructionName::VV_V_PRE, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::VV_V_DRV, true, 0, 1, LMemStatus::LOCK, LMemStatus::LOCK},
        {InstructionName::VV_V_DRV, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::VS_V_PRE, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::VS_V_DRV, true, 0, 1, LMemStatus::IDLE, LMemStatus::LOCK},
        {InstructionName::VS_V_DRV, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::V_S_DRV, true, 0, 1, LMemStatus::IDLE, LMemStatus::LOCK},
        {InstructionName::V_S_DRV, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
        // {InstructionName::V_S_POST, false, 0, 0, LMemStatus::IDLE, LMemStatus::IDLE},
        {InstructionName::V_V_DRV, true, 0, 1, LMemStatus::LOCK, LMemStatus::LOCK},
        {InstructionName::V_V_DRV, false, 0, 1, LMemStatus::IDLE, LMemStatus::IDLE},
    };

    // 执行测试用例
    for (const auto& test_case : test_cases)
    {
        test_invalid_command(test_case.should_be_invalid,
                             test_case.inst,
                             test_case.rs1_lmem_index,
                             test_case.rs2_lmem_index,
                             test_case.lmem_status1,
                             test_case.lmem_status2);
    }
}
// 测试命令堵塞条件
// 触发sync指令
void TestCmdScheduler::trigger_sync()
{
    send_cmd(InstructionName::SYNC);
}
// 设置ISQ队列状态
void TestCmdScheduler::set_isq_queue_status(int fu_index, bool is_full)
{
    isq_cmd_ready[fu_index].write(!is_full);
}

// 设置scoreboard状态
void TestCmdScheduler::set_scoreboard_status(uint32_t lmem_index, LMemStatus status)
{
    info_sbd->set_scoreboard_state(lmem_index, status);
}

// 设置WBQ状态
void TestCmdScheduler::set_wbq_status(int fu_index, bool is_full)
{
    wbq_rsp_valid[fu_index].write(!is_full);
}
// 设置所有WBQ状态
void TestCmdScheduler::set_all_wbq_valid()
{
    for (auto& wbq_valid : wbq_rsp_valid)
    {
        wbq_valid.write(true);
    }
}
// 设置所有WBQ状态
void TestCmdScheduler::set_all_wbq_invalid()
{
    for (auto& wbq_valid : wbq_rsp_valid)
    {
        wbq_valid.write(false);
    }
}
// 设置RSPQ状态
void TestCmdScheduler::set_rspq_status(bool is_full)
{
    rspq_ready.write(!is_full);
}