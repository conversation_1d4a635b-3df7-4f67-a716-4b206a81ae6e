#include <cstdint>
#include <iostream>
#include <random>
#include "../include/rsp_rob.h"
#include "npu_config.h"
#include "sysc/communication/sc_signal_ports.h"
#include "sysc/kernel/sc_simcontext.h"
#include "sysc/kernel/sc_time.h"
#include "systemc.h"
#include "tlm.h"
#include "tlm_utils/simple_initiator_socket.h"
#include "tlm_utils/simple_target_socket.h"
#include "utils/systemc_logger.h"
class TestModule : public sc_module
{
  public:
    tlm_utils::simple_initiator_socket<TestModule> disp_socket;
    tlm_utils::simple_initiator_socket<TestModule> wbq_socket;
    tlm_utils::simple_target_socket<TestModule> rspq_socket;

    sc_core::sc_out<bool> disp_roflag_valid;
    sc_core::sc_in<bool> disp_roflag_ready;

    sc_core::sc_in<bool> term_sync;

    sc_core::sc_in<bool> rspq_valid;
    sc_core::sc_out<bool> rspq_ready;

    std::array<sc_core::sc_out<bool>, NPUConfig::FU_NUM> wbq_rsp_valid;
    std::array<sc_core::sc_in<bool>, NPUConfig::FU_NUM> wbq_rsp_ready;

    // SC_HAS_PROCESS(TestModule);
    TestModule(const sc_module_name& name) : sc_module(name), term_sync("term_sync")
    {
        rspq_socket.register_b_transport(this, &TestModule::b_transport_rspq);
        SC_THREAD(test_thread);
    }

    void send_disp_roflag(const disp_roflag_info& disp_roflag)
    {
        debug_sc("Sending DISP ROFLAG");
        tlm::tlm_generic_payload trans;
        sc_time delay = SC_ZERO_TIME;
        trans.set_command(tlm::TLM_WRITE_COMMAND);
        trans.set_data_ptr(
            reinterpret_cast<unsigned char*>(const_cast<disp_roflag_info*>(&disp_roflag)));
        trans.set_data_length(sizeof(disp_roflag_info));
        trans.set_streaming_width(sizeof(disp_roflag_info));
        trans.set_byte_enable_ptr(0);
        trans.set_dmi_allowed(false);
        trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
        disp_socket->b_transport(trans, delay);
        if (trans.is_response_error())
        {
            SC_REPORT_ERROR("TLM-2", "Response error from b_transport");
        }
    }

    void send_wbq_rsp(const wbq_rsp_info& wbq_rsp)
    {
        tlm::tlm_generic_payload trans;
        sc_time delay = SC_ZERO_TIME;

        trans.set_command(tlm::TLM_WRITE_COMMAND);
        trans.set_data_ptr(reinterpret_cast<unsigned char*>(const_cast<wbq_rsp_info*>(&wbq_rsp)));
        trans.set_data_length(sizeof(wbq_rsp_info));
        trans.set_streaming_width(sizeof(wbq_rsp_info));
        trans.set_byte_enable_ptr(0);
        trans.set_dmi_allowed(false);
        trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
        debug_sc("Sending WBQ response");
        wbq_socket->b_transport(trans, delay);

        if (trans.is_response_error())
        {
            SC_REPORT_ERROR("TLM-2", "Response error from b_transport");
        }
    }

    void b_transport_rspq(tlm::tlm_generic_payload& trans, sc_time& delay)
    {
        if (trans.get_command() == tlm::TLM_WRITE_COMMAND)
        {
            auto* rsp = reinterpret_cast<rspq_rsp_info*>(trans.get_data_ptr());
            cout << "Received RSPQ response: " << hex << rsp->rspq_rsp_data << endl;
            trans.set_response_status(tlm::TLM_OK_RESPONSE);
        }
        else
        {
            trans.set_response_status(tlm::TLM_COMMAND_ERROR_RESPONSE);
        }
    }

    void test_thread()
    {
        // Implement test scenarios here
        test_wbq_blocking_sync_flag();
        test_wbq_blocking_fu_ena();
        test_rspq_blocking_sync_flag();
        test_rspq_blocking_no_sync_flag();
    }

  private:
    // 为结构体写入随机值的方法
    void randomize_wbq_rsp_info(wbq_rsp_info& wbq_rsp)
    {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<uint64_t> dis(0, (1ULL << NPUConfig::RV_XLEN) - 1);

        for (auto& data : wbq_rsp.wbq_rsp_data)
        {
            data = sc_uint<NPUConfig::RV_XLEN>(dis(gen));
        }
    }
    void test_wbq_blocking_sync_flag()
    {
        cout << "Testing WBQ blocking with sync flag" << endl;

        // 设置roflag，sync_flag=1
        disp_roflag_info disp_roflag;
        disp_roflag.disp_roflag_data[NPUConfig::FU_ID_WD + 8 - 1] = 1;  // sync_flag
        disp_roflag.disp_roflag_data[5] = 1;                            // inst_xd
        rspq_ready.write(true);
        wait(SC_ZERO_TIME);
        send_disp_roflag(disp_roflag);
        // 非rspq堵塞
        // 设置wbq_rsp_valid不全为有效
        wbq_rsp_info wbq_rsp;
        randomize_wbq_rsp_info(wbq_rsp);
        for (auto& valid : wbq_rsp_valid)
        {
            valid->write(std::rand() % 2 == 0);
        }
        wbq_rsp_valid[0]->write(false);
        send_wbq_rsp(wbq_rsp);
        wait(SC_ZERO_TIME);

        wait(10, SC_NS);
        // 解除堵塞
        for (auto& valid : wbq_rsp_valid)
        {
            valid->write(true);
        }
        wait(10, SC_NS);
        cout << "WBQ blocking with sync flag test completed" << endl;
    }

    void test_wbq_blocking_fu_ena()
    {
        cout << "Testing WBQ blocking with fu_ena" << endl;

        // 设置roflag，sync=0, fu_ena=1, inst.xd=1
        disp_roflag_info disp_roflag;
        disp_roflag.disp_roflag_data[NPUConfig::FU_ID_WD + 8 - 1] = 0;           // sync_flag
        disp_roflag.disp_roflag_data[NPUConfig::FU_ID_WD + 8 - 2] = 1;           // fu_ena
        disp_roflag.disp_roflag_data[5] = 1;                                     // inst_xd
        disp_roflag.disp_roflag_data.range(NPUConfig::FU_ID_WD + 8 - 3, 6) = 2;  // fuid = 2
        wbq_rsp_valid[2].write(false);  // 设置fu_id=2的为false
        wait(SC_ZERO_TIME);
        send_disp_roflag(disp_roflag);

        // 设置wbq_rsp_valid[fu_id]为false
        wbq_rsp_info wbq_rsp;
        randomize_wbq_rsp_info(wbq_rsp);
        send_wbq_rsp(wbq_rsp);

        wait(100, SC_NS);  // 等待一段时间

        // 解除堵塞
        wbq_rsp_valid[2].write(true);
        wait(SC_ZERO_TIME);
        cout << "WBQ blocking with fu_ena test completed" << endl;
    }

    void test_rspq_blocking_sync_flag()
    {
        cout << "Testing RSPQ blocking with sync flag" << endl;

        for (auto& valid : wbq_rsp_valid)
        {
            valid->write(true);
        }
        // 设置roflag，sync_flag=1, inst.xd=1
        disp_roflag_info disp_roflag;
        disp_roflag.disp_roflag_data[NPUConfig::FU_ID_WD + 8 - 1] = 1;  // sync_flag
        disp_roflag.disp_roflag_data[5] = 1;                            // inst_xd

        // 设置rspq_ready为false
        rspq_ready.write(false);
        wait(SC_ZERO_TIME);
        send_disp_roflag(disp_roflag);

        wbq_rsp_info wbq_rsp;
        randomize_wbq_rsp_info(wbq_rsp);
        send_wbq_rsp(wbq_rsp);

        wait(100, SC_NS);  // 等待一段时间

        // 解除堵塞
        rspq_ready.write(true);
        wait(SC_ZERO_TIME);
        cout << "RSPQ blocking with sync flag test completed" << endl;
    }

    void test_rspq_blocking_no_sync_flag()
    {
        cout << "Testing RSPQ blocking without sync flag" << endl;
        for (auto& valid : wbq_rsp_valid)
        {
            valid->write(true);
        }
        wait(SC_ZERO_TIME);
        // 设置roflag，sync_flag=0, inst.xd=1
        disp_roflag_info disp_roflag;
        disp_roflag.disp_roflag_data[NPUConfig::FU_ID_WD + 8 - 1] = 0;  // sync_flag
        disp_roflag.disp_roflag_data[5] = 1;                            // inst_xd
        // 设置rspq_ready为false
        rspq_ready.write(false);
        wait(SC_ZERO_TIME);
        send_disp_roflag(disp_roflag);

        wait(100, SC_NS);  // 等待一段时间

        // 解除堵塞
        rspq_ready.write(true);

        wait(100, SC_NS);  // 等待处理
        cout << "RSPQ blocking without sync flag test completed" << endl;
    }
};

int sc_main(int argc, char* argv[])
{
    g_logger.setLogLevel(SystemCLogger::SC_INFO);
    // Create modules
    RSP_ROB rsp_rob("rsp_rob", NPUConfig::ROB_QUEUE_SIZE);
    TestModule test_module("test_module");

    // Create signals
    sc_signal<bool> term_sync_signal("term_sync_signal");
    std::array<sc_signal<bool>, NPUConfig::FU_NUM> wbq_rsp_valid_signal;
    std::array<sc_signal<bool>, NPUConfig::FU_NUM> wbq_rsp_ready_signal;
    sc_signal<bool> rspq_valid_signal("rspq_valid_signal");
    sc_signal<bool> rspq_ready_signal("rspq_ready_signal");
    sc_signal<bool> disp_roflag_valid_signal("disp_roflag_valid_signal");
    sc_signal<bool> disp_roflag_ready_signal("disp_roflag_ready_signal");

    // Connect modules
    test_module.disp_socket.bind(rsp_rob.disp_roflag_socket);
    test_module.wbq_socket.bind(rsp_rob.wbq_rsp_socket);
    rsp_rob.rspq_socket.bind(test_module.rspq_socket);

    rsp_rob.term_sync.bind(term_sync_signal);
    test_module.term_sync.bind(term_sync_signal);

    // Connect the rspq_ready signal
    rsp_rob.rspq_ready.bind(rspq_ready_signal);
    test_module.rspq_ready.bind(rspq_ready_signal);
    // connect to rspq_valid_signal
    rsp_rob.rspq_valid.bind(rspq_valid_signal);
    test_module.rspq_valid.bind(rspq_valid_signal);
    // connect to disp_roflag_valid_signal
    rsp_rob.disp_roflag_valid.bind(disp_roflag_valid_signal);
    test_module.disp_roflag_valid.bind(disp_roflag_valid_signal);

    // connect to disp_roflag_ready_signal
    rsp_rob.disp_roflag_ready.bind(disp_roflag_ready_signal);
    test_module.disp_roflag_ready.bind(disp_roflag_ready_signal);

    // connect to wbq_rsp_valid_signa
    for (int i = 0; i < NPUConfig::FU_NUM; i++)
    {
        rsp_rob.wbq_rsp_valid[i].bind(wbq_rsp_valid_signal[i]);
        test_module.wbq_rsp_valid[i].bind(wbq_rsp_valid_signal[i]);
    }
    // connect to wbq_rsp_ready_signal
    for (int i = 0; i < NPUConfig::FU_NUM; i++)
    {
        rsp_rob.wbq_rsp_ready[i].bind(wbq_rsp_ready_signal[i]);
        test_module.wbq_rsp_ready[i].bind(wbq_rsp_ready_signal[i]);
    }
    // Start simulation
    sc_start();

    return 0;
}