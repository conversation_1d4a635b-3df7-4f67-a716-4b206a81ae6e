#ifndef TEST_CMD_SCHEDULER_H
#define TEST_CMD_SCHEDULER_H

#include <random>
#include "cmd_scheduler.h"
#include "npu_config.h"
#include "systemc"
#include "tlm_utils/simple_initiator_socket.h"
#include "tlm_utils/simple_target_socket.h"

class TestCmdScheduler : public sc_core::sc_module
{
  public:
    tlm_utils::simple_initiator_socket<TestCmdScheduler> cmd_input_socket;
    tlm_utils::simple_initiator_socket<TestCmdScheduler> wbq_rsp_socket;
    tlm_utils::simple_target_socket<TestCmdScheduler> isq_cmd_socket;
    tlm_utils::simple_target_socket<TestCmdScheduler> rspq_socket;
    sc_core::sc_out<bool> cmd_queue_valid;
    sc_core::sc_in<bool> cmd_queue_ready;
    sc_core::sc_in<bool> rspq_valid;
    sc_core::sc_out<bool> rspq_ready;
    std::array<sc_core::sc_in<bool>, NPUConfig::FU_NUM> isq_cmd_valid;
    std::array<sc_core::sc_out<bool>, NPUConfig::FU_NUM> isq_cmd_ready;
    std::array<sc_core::sc_out<bool>, NPUConfig::FU_NUM> wbq_rsp_valid;
    std::array<sc_core::sc_in<bool>, NPUConfig::FU_NUM> wbq_rsp_ready;

    TestCmdScheduler(const sc_core::sc_module_name& name);
    void b_transport_isq_cmd(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    void b_transport_rspq(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    void send_cmd();
    void send_cmd(InstructionName inst_type,
                  uint64_t rs1_lmem_index = 0,
                  uint64_t rs2_lmem_index = 0,
                  bool xd = false,
                  bool xs1 = false,
                  bool xs2 = false);
    uint64_t generate_lmem_addr(uint32_t lmem_index, uint32_t lmem_offset);
    void send_invalid_command(InstructionName inst_type,
                              uint64_t rs1_lmem_index = 0,
                              uint64_t rs2_lmem_index = 0,
                              LMemStatus lmem_status1 = LMemStatus::LOCK,
                              LMemStatus lmem_status2 = LMemStatus::LOCK);
    void test_invalid_command(bool should_be_invalid,
                              InstructionName inst_type,
                              uint32_t rs1_lmem_index,
                              uint32_t rs2_lmem_index,
                              LMemStatus lmem_status1,
                              LMemStatus lmem_status2);
    void test_all_invalid_commands();
    void trigger_sync();
    void set_isq_queue_status(int fu_index, bool is_full);
    void set_scoreboard_status(uint32_t lmem_index, LMemStatus status);
    void set_wbq_status(int fu_index, bool is_full);
    void set_rspq_status(bool is_full);
    void set_all_wbq_valid();
    void set_all_wbq_invalid();

    CmdScheduler* cmd_sched;
    scoreboard* info_sbd;
};

#endif
