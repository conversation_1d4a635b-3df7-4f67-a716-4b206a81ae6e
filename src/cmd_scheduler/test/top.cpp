#include <systemc>
#include "cmd_scheduler.h"
#include "sysc/kernel/sc_simcontext.h"
#include "test_cmd_scheduler.h"

using namespace sc_core;

class TOP : public sc_core::sc_module
{
  public:
    CmdScheduler cmd_sched;
    TestCmdScheduler tb;
    scoreboard info_sbd;
    // 声明信号
    sc_core::sc_signal<bool> cmd_queue_valid;
    sc_core::sc_signal<bool> cmd_queue_ready;
    sc_core::sc_signal<bool> rspq_valid;
    sc_core::sc_signal<bool> rspq_ready;
    std::array<sc_core::sc_signal<bool>, NPUConfig::FU_NUM> isq_cmd_valid;
    std::array<sc_core::sc_signal<bool>, NPUConfig::FU_NUM> isq_cmd_ready;
    std::array<sc_core::sc_signal<bool>, NPUConfig::FU_NUM> wbq_rsp_valid;
    std::array<sc_core::sc_signal<bool>, NPUConfig::FU_NUM> wbq_rsp_ready;

    explicit TOP(sc_core::sc_module_name name)
        : sc_module(name), cmd_sched("cmd_sched", 8), tb("tb"), info_sbd("info_sbd")
    {  // 假设ROB队列大小为8

        // 连接TLM sockets
        cmd_sched.m_cmd_dispatch.info_sbd_socket.bind(info_sbd.socket);
        tb.isq_cmd_socket.bind(cmd_sched.m_cmd_dispatch.isq_cmd_socket);
        tb.wbq_rsp_socket.bind(cmd_sched.m_rsp_rob.wbq_rsp_socket);
        cmd_sched.m_cmd_dispatch.cmd_queue_socket.bind(tb.cmd_input_socket);
        cmd_sched.m_rsp_rob.rspq_socket.bind(tb.rspq_socket);
        // 连接信号
        tb.cmd_queue_valid(cmd_queue_valid);
        tb.cmd_queue_ready(cmd_queue_ready);
        tb.rspq_valid(rspq_valid);
        tb.rspq_ready(rspq_ready);
        cmd_sched.cmd_queue_valid(cmd_queue_valid);
        cmd_sched.cmd_queue_ready(cmd_queue_ready);
        cmd_sched.rspq_valid(rspq_valid);
        cmd_sched.rspq_ready(rspq_ready);

        for (int i = 0; i < NPUConfig::FU_NUM; ++i)
        {
            tb.isq_cmd_valid[i](isq_cmd_valid[i]);
            tb.isq_cmd_ready[i](isq_cmd_ready[i]);
            tb.wbq_rsp_valid[i](wbq_rsp_valid[i]);
            tb.wbq_rsp_ready[i](wbq_rsp_ready[i]);
            cmd_sched.isq_cmd_valid[i](isq_cmd_valid[i]);
            cmd_sched.isq_cmd_ready[i](isq_cmd_ready[i]);
            cmd_sched.wbq_rsp_valid[i](wbq_rsp_valid[i]);
            cmd_sched.wbq_rsp_ready[i](wbq_rsp_ready[i]);
        }

        // 设置TestBench的cmd_sched指针
        tb.cmd_sched = &cmd_sched;
        tb.info_sbd = &info_sbd;

        SC_THREAD(test_thread);
    }
    void test_thread()
    {
        info_sc("test_thread start");
        tb.test_all_invalid_commands();
    }
};

int sc_main(int argc, char* argv[])
{
    g_logger.setLogLevel(SystemCLogger::SC_INFO);
    TOP top("top");
    sc_start(300, SC_NS);
    return 0;
}