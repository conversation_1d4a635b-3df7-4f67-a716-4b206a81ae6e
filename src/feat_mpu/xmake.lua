

target("feat_mpu")
    set_kind("static")
    add_files("feat_mpu.cpp")
    add_includedirs(".",{public=true})
    add_deps("dcim_cluster")
    add_deps("local_mem")
    add_deps("utils")
    add_packages("spdlog")

target("test_mpu")
    set_kind("binary")
    add_files("test/test_mpu.cpp")
    add_deps("feat_mpu")
    add_tests("test_mpu", {group = "feat_mpu"})
    add_packages("spdlog")
includes("../cim_cluster/xmake.lua")
includes("../local_mem/xmake.lua")
