#include "feat_mpu.hpp"
#include <cstdint>
#include <sstream>
#include <iomanip>
#include "../cim_cluster/inc/dcim_cluster.hpp"
#include "npu_config.h"
#include "utils/utils.h"
#include "utils/sc_logger.h"
using namespace sc_core;
using namespace instruction::opcode;

feat_mpu::feat_mpu(sc_module_name name, DcimCluster* cim_cluster)
    : sc_module(name),
      cfg_socket("cfg_socket"),
      mem_socket("mem_socket"),
      m_delay(sc_time(0, SC_NS)),
      m_cim_cluster(cim_cluster)
{
    cfg_socket.register_b_transport(this, &feat_mpu::b_transport_cfg);

    SC_THREAD(thread_cfg);
    SC_THREAD(thread_drv);
}

void feat_mpu::b_transport_cfg(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
{
    m_issue_queue_cmd = *(IssueQueueCmd*)trans.get_data_ptr();
    trans.set_response_status(tlm::TLM_OK_RESPONSE);
    rec_cfg_event.notify();
}

void feat_mpu::thread_cfg()
{
    while (true)
    {
        wait(rec_cfg_event);
        if (m_issue_queue_cmd.funct7 == MP_CFG)
        {
            m_cfg.writeReg(m_issue_queue_cmd.rs1val, m_issue_queue_cmd.rs2val);
        }
        else if (m_issue_queue_cmd.funct7 == GEMV_PRE || m_issue_queue_cmd.funct7 == GEMM_PRE)
        {
            m_page_idx = m_issue_queue_cmd.rs1val;
            m_byte_base_in = m_issue_queue_cmd.rs2val;
        }
        else if (m_issue_queue_cmd.funct7 == GEMV_DRV || m_issue_queue_cmd.funct7 == GEMM_DRV)
        {
            m_byte_base_out = m_issue_queue_cmd.rs1val;
            m_byte_base_orig = m_issue_queue_cmd.rs2val;
            // prec
            m_cfg_mpu = m_cfg.getConfig();
            m_cfg_mpu.print();
            m_prec_in = m_cfg_mpu.cfg_type_in << 3 | m_cfg_mpu.cfg_wd_in;
            m_prec_out = m_cfg_mpu.cfg_type_out << 3 | m_cfg_mpu.cfg_wd_out;
            m_prec_orig = m_cfg_mpu.cfg_type_orig << 3 | m_cfg_mpu.cfg_wd_orig;
            m_prec_wt = m_cfg_mpu.cfg_type_wt << 3 | m_cfg_mpu.cfg_wd_wt;
            cim_prec_in = dtype_to_dcim_fmt(m_prec_in);
            cim_prec_out = dtype_to_dcim_fmt(m_prec_out);
            cim_prec_orig = dtype_to_dcim_fmt(m_prec_orig);
            cim_prec_wt = dtype_to_dcim_fmt(m_prec_wt);
            drv_event.notify();
        }
    }
}

void feat_mpu::thread_drv()
{
    while (true)
    {
        wait(drv_event);
        if (m_issue_queue_cmd.funct7 == GEMM_DRV)
        {
            process_gemm();
        }
        else if (m_issue_queue_cmd.funct7 == GEMV_DRV)
        {
            process_gemv();
        }
    }
}

void feat_mpu::process_gemm()
{
    // Constants for GEMM
    constexpr uint32_t INPUTS_PER_BATCH =
        256;  // 32x4x2 (rows per page * engines * macros per engine)

    // 1. Calculate matrix dimensions
    uint32_t input_rows = m_cfg_mpu.cfg_size_dim1_in;
    uint32_t input_cols = m_cfg_mpu.cfg_size_dim0b_in;
    uint32_t output_rows = input_rows;
    uint32_t output_cols = 64;  // Fixed size from CIM cluster output (per batch)

    // 2. Process each row of the input matrix
    for (uint32_t row = 0; row < input_rows; row++)
    {
        // Vector to store input data for current row
        std::vector<Word256b> input_data;

        // Read input data for current row from local memory
        for (uint32_t dim0b = 0; dim0b < input_cols; dim0b++)
        {
            // Calculate memory address for current block
            uint64_t read_addr = m_byte_base_in + (row * m_cfg_mpu.cfg_stride_dim1_in + dim0b) *
                                                      (NPUConfig::LMEM_WD / 8);

            // Calculate mask for last block (if needed)
            MaskInfo mask_info =
                calculate_mask_info(dim0b, input_cols, m_cfg_mpu.cfg_rem_dim0_in, m_prec_in);

            // Read data from memory
            Word256b data = read_data(read_addr, mask_info);
            input_data.push_back(data);
        }

        // 3. Convert input data to raw format for DCIM engine
        std::vector<uint8_t> raw_input =
            convert_to_raw_input(input_data, INPUTS_PER_BATCH, m_prec_in);

        // 4. Prepare to store computation results for this row
        std::vector<uint8_t> output_buffer(256);  // 64 FP32 values * 4 bytes = 256 bytes

        // 5. Call CIM cluster to perform matrix-vector multiplication for this row
        bool compute_success = m_cim_cluster->computeRawInput(
            raw_input.data(), cim_prec_in, m_page_idx, cim_prec_wt, output_buffer.data());

        if (!compute_success)
        {
            std::cerr << "Error: CIM cluster computation failed for row " << row << std::endl;
            return;
        }
        // std::cout << " row " << row << " CIM cluster output (64 FP32 values):" << std::endl;
        // for (int i = 0; i < 64; ++i) {
        //     // Each FP32 value is 4 bytes. Access the i-th value.
        //     // Interpret the 4 bytes starting at index i*4 as a float.
        //     float output_val = *reinterpret_cast<float*>(&output_buffer[i * 4]);
        //     std::cout << "Output[" << i << "]: " << output_val << std::endl;
        // }
        // Calculate number of elements in output and determine precision
        uint32_t num_elements =
            NPUConfig::LMEM_WD /
            width_code_to_bits(static_cast<dtype::WidthCode>(m_cfg_mpu.cfg_wd_wt));
        bool is_fp_out = is_float_type(m_cfg_mpu.cfg_type_out);
        uint32_t prec_bits =
            width_code_to_bits(static_cast<dtype::WidthCode>(m_cfg_mpu.cfg_wd_out));

        // 6. Handle accumulation if requested
        if (m_cfg_mpu.cfg_accu)
        {
            // Read original data for accumulation
            std::vector<Word256b> orig_data;
            for (uint32_t dim0b = 0; dim0b < m_cfg_mpu.cfg_size_dim0b_out; dim0b++)
            {
                uint64_t read_addr =
                    m_byte_base_orig +
                    (row * m_cfg_mpu.cfg_stride_dim1_out + dim0b) * (NPUConfig::LMEM_WD / 8);
                MaskInfo mask_info = calculate_mask_info(
                    dim0b, m_cfg_mpu.cfg_size_dim0b_out, m_cfg_mpu.cfg_rem_dim0_out, m_prec_orig);
                orig_data.push_back(read_data(read_addr, mask_info));
            }

            // Convert to raw format
            std::vector<uint8_t> raw_orig =
                convert_to_raw_input(orig_data, num_elements, m_prec_orig);
            bool is_fp = is_float_type(m_cfg_mpu.cfg_type_orig);

            // Accumulate (assuming data is already in FP32 format from DCIM)
            for (size_t i = 0; i < num_elements; i++)
            {
                float* result = reinterpret_cast<float*>(&output_buffer[i * 4]);
                if (is_fp)
                {
                    uint32_t mhex = uint32_t(raw_orig[2 * i + 1] << 8 | raw_orig[2 * i]);
                    float orig_val;
                    glb_mh2f_conv(mhex, m_prec_orig, &orig_val);
                    *result += orig_val;
                }
                else
                {
                    uint32_t mhex = uint32_t(raw_orig[2 * i + 1] << 8 | raw_orig[2 * i]);
                    int32_t sv = uint32_to_int32_prec(mhex, m_prec_orig);
                    *result += static_cast<float>(sv);
                }
            }
        }

        // 7. Apply activation function if requested
        if (m_cfg_mpu.cfg_act)
        {
            // Apply ReLU
            for (size_t i = 0; i < num_elements; i++)
            {
                float* val = reinterpret_cast<float*>(&output_buffer[i * 4]);
                if (*val < 0.0f)
                    *val = 0.0f;
            }
        }

        // 8. Apply right shift for integer outputs if configured
        if (m_cfg_mpu.cfg_shift > 0 && !is_fp_out)
        {  // Check if output is integer type
            for (size_t i = 0; i < num_elements; ++i)
            {
                float fval = *reinterpret_cast<float*>(&output_buffer[i * 4]);
                int32_t iv = static_cast<int32_t>(fval) >> m_cfg_mpu.cfg_shift;
                float f2 = static_cast<float>(iv);
                std::memcpy(&output_buffer[i * 4], &f2, sizeof(float));
            }
        }

        // 9. Pack results based on precision and write to memory
        uint32_t total_bits = num_elements * prec_bits;
        uint32_t total_bytes = (total_bits + 7) / 8;
        std::vector<uint8_t> packed(total_bytes, 0);

        for (size_t idx = 0; idx < num_elements; ++idx)
        {
            float fval = *reinterpret_cast<float*>(&output_buffer[idx * 4]);
            uint8_t width_code_out = m_prec_out & 0x07;
            uint8_t type_code_out = m_prec_out >> 3;

            if (is_fp_out)
            {
                uint32_t mh = glb_f2mh_conv(fval, m_prec_out);

                // Pack the raw floating-point data based on width
                if (width_code_out == static_cast<uint8_t>(dtype::WidthCode::W8))
                {                           // FP8
                    size_t byte_idx = idx;  // 1 byte per element
                    if (byte_idx < packed.size())
                    {
                        packed[byte_idx] = static_cast<uint8_t>(mh & 0xFF);
                    }
                }
                else if (width_code_out == static_cast<uint8_t>(dtype::WidthCode::W16))
                {                               // FP16, BF16
                    size_t byte_idx = idx * 2;  // 2 bytes per element
                    if (byte_idx + 1 < packed.size())
                    {
                        packed[byte_idx] = static_cast<uint8_t>(mh & 0xFF);
                        packed[byte_idx + 1] = static_cast<uint8_t>((mh >> 8) & 0xFF);
                    }
                }
                else if (width_code_out == static_cast<uint8_t>(dtype::WidthCode::W32))
                {                               // FP32
                    size_t byte_idx = idx * 4;  // 4 bytes per element
                    if (byte_idx + 3 < packed.size())
                    {
                        packed[byte_idx] = static_cast<uint8_t>(mh & 0xFF);
                        packed[byte_idx + 1] = static_cast<uint8_t>((mh >> 8) & 0xFF);
                        packed[byte_idx + 2] = static_cast<uint8_t>((mh >> 16) & 0xFF);
                        packed[byte_idx + 3] = static_cast<uint8_t>((mh >> 24) & 0xFF);
                    }
                }
                else
                {
                    // Handle unsupported FP width or error
                    std::cerr << "Unsupported floating point output width: " << (int)width_code_out
                              << std::endl;
                }
            }
            else
            {                                             // Integer output
                int32_t iv = static_cast<int32_t>(fval);  // Convert float to int

                // Pack the integer data based on width
                if (width_code_out == static_cast<uint8_t>(dtype::WidthCode::W4))
                {                                                          // INT4
                    size_t byte_idx = idx / 2;                             // 2 elements per byte
                    uint8_t nibble_val = static_cast<uint8_t>(iv & 0x0F);  // Get the lower 4 bits

                    if (byte_idx < packed.size())
                    {
                        if (idx % 2 == 0)
                        {  // First element of the byte (lower nibble)
                            packed[byte_idx] = (packed[byte_idx] & 0xF0) | nibble_val;
                        }
                        else
                        {  // Second element of the byte (upper nibble)
                            packed[byte_idx] = (packed[byte_idx] & 0x0F) | (nibble_val << 4);
                        }
                    }
                }
                else if (width_code_out == static_cast<uint8_t>(dtype::WidthCode::W8))
                {                           // INT8
                    size_t byte_idx = idx;  // 1 byte per element
                    if (byte_idx < packed.size())
                    {
                        packed[byte_idx] = static_cast<uint8_t>(iv & 0xFF);  // Get the lower 8 bits
                    }
                }
                else if (width_code_out == static_cast<uint8_t>(dtype::WidthCode::W16))
                {                               // INT16
                    size_t byte_idx = idx * 2;  // 2 bytes per element
                    if (byte_idx + 1 < packed.size())
                    {
                        packed[byte_idx] = static_cast<uint8_t>(iv & 0xFF);  // Lower byte
                        packed[byte_idx + 1] =
                            static_cast<uint8_t>((iv >> 8) & 0xFF);  // Upper byte
                    }
                }
                else
                {
                    // Handle unsupported INT width or error
                    std::cerr << "Unsupported integer output width: " << (int)width_code_out
                              << std::endl;
                }
            }
        }

        for (uint32_t w = 0; w < (total_bytes + 31) / 32; ++w)
        {
            Word256b word;
            word.fill(0);
            size_t copy_bytes = std::min<size_t>(32, total_bytes - w * 32);
            std::memcpy(word.data(), &packed[w * 32], copy_bytes);
            uint64_t write_addr = m_byte_base_out + (row * m_cfg_mpu.cfg_stride_dim1_out + w) * 32;
            // std::cout << " row " << row << " write_addr: " << write_addr << std::endl;
            write_data(write_addr, word);
        }
    }
}

// Convert data from various precisions to 2-byte format needed for DCIM engine
std::vector<uint8_t> feat_mpu::convert_to_raw_input(const std::vector<Word256b>& input_data,
                                                    uint32_t total_elements,
                                                    uint8_t prec)
{
    // Each element in the DCIM raw input needs to be 2 bytes (uint16_t)
    std::vector<uint8_t> raw_input(total_elements * 2);

    size_t dest_idx = 0;
    uint8_t width_code = prec & 0x07;
    uint8_t type_code = prec >> 3;

    for (const auto& word : input_data)
    {
        size_t src_idx = 0;

        // Process based on precision type
        if (width_code == static_cast<uint8_t>(dtype::WidthCode::W4))
        {
            // INT4: 2 elements per byte
            while (src_idx < word.size() && dest_idx < raw_input.size())
            {
                uint8_t byte_val = word[src_idx++];

                // Extract low 4 bits for first element
                if (dest_idx < raw_input.size())
                {
                    raw_input[dest_idx++] = byte_val & 0x0F;
                    raw_input[dest_idx++] = 0;
                }

                // Extract high 4 bits for second element
                if (dest_idx < raw_input.size())
                {
                    raw_input[dest_idx++] = (byte_val >> 4) & 0x0F;
                    raw_input[dest_idx++] = 0;
                }
            }
        }
        else if (width_code == static_cast<uint8_t>(dtype::WidthCode::W8))
        {
            // INT8 or FP8: 1 byte per element
            while (src_idx < word.size() && dest_idx < raw_input.size())
            {
                raw_input[dest_idx++] = word[src_idx];
                raw_input[dest_idx++] = 0;  // Upper byte is 0
                src_idx++;
            }
        }
        else if (width_code == static_cast<uint8_t>(dtype::WidthCode::W16))
        {
            // INT16 or FP16: 2 bytes per element
            while (src_idx + 1 < word.size() && dest_idx + 1 < raw_input.size())
            {
                raw_input[dest_idx++] = word[src_idx++];
                raw_input[dest_idx++] = word[src_idx++];
            }
        }

        // Stop if we've processed all needed elements
        if (dest_idx >= total_elements * 2)
        {
            break;
        }
    }

    return raw_input;
}

void feat_mpu::process_gemv()
{
    // Constants for GEMV
    constexpr uint32_t INPUTS_PER_BATCH =
        256;  // 33x4x2 (rows per page * engines * macros per engine)

    // 1. Calculate number of batches needed based on vector size
    uint32_t vector_size = m_cfg_mpu.cfg_size_dim0b_in * (NPUConfig::LMEM_WD / 8);
    uint32_t batch_count = (vector_size + INPUTS_PER_BATCH - 1) / INPUTS_PER_BATCH;

    // Vector to store all input data for processing
    std::vector<Word256b> input_data;

    // 2. Read input data from local memory
    for (uint32_t dim0b = 0; dim0b < m_cfg_mpu.cfg_size_dim0b_in; dim0b++)
    {
        // Calculate memory address for current block
        uint64_t read_addr = m_byte_base_in + dim0b * (NPUConfig::LMEM_WD / 8);

        // Calculate mask for last block (if needed)
        MaskInfo mask_info = calculate_mask_info(
            dim0b, m_cfg_mpu.cfg_size_dim0b_in, m_cfg_mpu.cfg_rem_dim0_in, m_prec_in);

        // Read data from memory
        Word256b data = read_data(read_addr, mask_info);
        input_data.push_back(data);
    }

    // 3. Convert input data to raw format for DCIM engine (2 bytes per element)
    std::vector<uint8_t> raw_input = convert_to_raw_input(input_data, INPUTS_PER_BATCH, m_prec_in);

    // 4. Prepare to store computation results
    std::vector<uint8_t> output_buffer(256);  // 64 FP32 values * 4 bytes = 256 bytes

    // 5. Call CIM cluster to perform matrix-vector multiplication
    bool compute_success = m_cim_cluster->computeRawInput(
        raw_input.data(), cim_prec_in, m_page_idx, cim_prec_wt, output_buffer.data());

    if (!compute_success)
    {
        std::cerr << "Error: CIM cluster computation failed" << std::endl;
        return;
    }

    // Print all 64 output values stored in output_buffer
    std::cout << "CIM cluster output (64 FP32 values):" << std::endl;
    for (int i = 0; i < 64; ++i)
    {
        // Each FP32 value is 4 bytes. Access the i-th value.
        // Interpret the 4 bytes starting at index i*4 as a float.
        float output_val = *reinterpret_cast<float*>(&output_buffer[i * 4]);
        std::cout << "Output[" << i << "]: " << output_val << std::endl;
    }
    uint32_t num_elements =
        NPUConfig::LMEM_WD / width_code_to_bits(static_cast<dtype::WidthCode>(m_cfg_mpu.cfg_wd_wt));
    bool is_fp_out = is_float_type(m_cfg_mpu.cfg_type_out);
    uint32_t prec_bits = width_code_to_bits(static_cast<dtype::WidthCode>(m_cfg_mpu.cfg_wd_out));

    // 6. Handle accumulation if requested
    if (m_cfg_mpu.cfg_accu)
    {
        // Read original data for accumulation
        std::vector<Word256b> orig_data;
        for (uint32_t dim0b = 0; dim0b < m_cfg_mpu.cfg_size_dim0b_out; dim0b++)
        {
            uint64_t read_addr = m_byte_base_orig + dim0b * (NPUConfig::LMEM_WD / 8);
            MaskInfo mask_info = calculate_mask_info(
                dim0b, m_cfg_mpu.cfg_size_dim0b_out, m_cfg_mpu.cfg_rem_dim0_out, m_prec_orig);
            orig_data.push_back(read_data(read_addr, mask_info));
        }

        // Convert to raw format
        std::vector<uint8_t> raw_orig = convert_to_raw_input(orig_data, num_elements, m_prec_orig);
        bool is_fp = is_float_type(m_cfg_mpu.cfg_type_orig);
        // Accumulate (assuming data is already in FP32 format from DCIM)
        for (size_t i = 0; i < num_elements; i++)
        {
            float* result = reinterpret_cast<float*>(&output_buffer[i * 4]);
            if (is_fp)
            {
                uint32_t mhex = uint32_t(raw_orig[2 * i + 1] << 8 | raw_orig[2 * i]);
                float orig_val;
                glb_mh2f_conv(mhex, m_prec_orig, &orig_val);
                *result += orig_val;
            }
            else
            {
                uint32_t mhex = uint32_t(raw_orig[2 * i + 1] << 8 | raw_orig[2 * i]);
                std::cout << " row " << i << " mhex: " << mhex << std::endl;
                int32_t sv = uint32_to_int32_prec(mhex, m_prec_orig);
                *result += static_cast<float>(sv);
            }
            std::cout << " row " << i << " result: " << *result << std::endl;
        }
    }

    // 7. Apply activation function if requested
    if (m_cfg_mpu.cfg_act)
    {
        // Apply ReLU
        for (size_t i = 0; i < num_elements; i++)
        {
            float* val = reinterpret_cast<float*>(&output_buffer[i * 4]);
            if (*val < 0.0f)
                *val = 0.0f;
            std::cout << " row " << i << " after act: " << *val << std::endl;
        }
    }

    // 8. Apply right shift for integer outputs if configured
    if (m_cfg_mpu.cfg_shift > 0 && !is_fp_out)
    {  // Check if output is integer type
        for (size_t i = 0; i < num_elements; ++i)
        {
            float fval = *reinterpret_cast<float*>(&output_buffer[i * 4]);
            int32_t iv = static_cast<int32_t>(fval) >> m_cfg_mpu.cfg_shift;
            float f2 = static_cast<float>(iv);
            std::memcpy(&output_buffer[i * 4], &f2, sizeof(float));
            std::cout << " row " << i << " after shift: " << f2 << std::endl;
        }
    }

    // 9. Pack results based on precision and write to memory
    uint32_t total_bits = num_elements * prec_bits;
    uint32_t total_bytes = (total_bits + 7) / 8;
    std::vector<uint8_t> packed(total_bytes, 0);

    for (size_t idx = 0; idx < num_elements; ++idx)
    {
        float fval = *reinterpret_cast<float*>(&output_buffer[idx * 4]);
        uint8_t width_code_out = m_prec_out & 0x07;
        uint8_t type_code_out = m_prec_out >> 3;

        if (is_fp_out)
        {
            uint32_t mh = glb_f2mh_conv(fval, m_prec_out);

            // Pack the raw floating-point data based on width
            if (width_code_out == static_cast<uint8_t>(dtype::WidthCode::W8))
            {                           // FP8
                size_t byte_idx = idx;  // 1 byte per element
                if (byte_idx < packed.size())
                {
                    packed[byte_idx] = static_cast<uint8_t>(mh & 0xFF);
                }
            }
            else if (width_code_out == static_cast<uint8_t>(dtype::WidthCode::W16))
            {                               // FP16, BF16
                size_t byte_idx = idx * 2;  // 2 bytes per element
                if (byte_idx + 1 < packed.size())
                {
                    packed[byte_idx] = static_cast<uint8_t>(mh & 0xFF);
                    packed[byte_idx + 1] = static_cast<uint8_t>((mh >> 8) & 0xFF);
                }
            }
            else if (width_code_out == static_cast<uint8_t>(dtype::WidthCode::W32))
            {                               // FP32
                size_t byte_idx = idx * 4;  // 4 bytes per element
                if (byte_idx + 3 < packed.size())
                {
                    packed[byte_idx] = static_cast<uint8_t>(mh & 0xFF);
                    packed[byte_idx + 1] = static_cast<uint8_t>((mh >> 8) & 0xFF);
                    packed[byte_idx + 2] = static_cast<uint8_t>((mh >> 16) & 0xFF);
                    packed[byte_idx + 3] = static_cast<uint8_t>((mh >> 24) & 0xFF);
                }
            }
            else
            {
                // Handle unsupported FP width or error
                std::cerr << "Unsupported floating point output width: " << (int)width_code_out
                          << std::endl;
            }
        }
        else
        {                                             // Integer output
            int32_t iv = static_cast<int32_t>(fval);  // Convert float to int

            // Pack the integer data based on width
            if (width_code_out == static_cast<uint8_t>(dtype::WidthCode::W4))
            {                                                          // INT4
                size_t byte_idx = idx / 2;                             // 2 elements per byte
                uint8_t nibble_val = static_cast<uint8_t>(iv & 0x0F);  // Get the lower 4 bits

                if (byte_idx < packed.size())
                {
                    if (idx % 2 == 0)
                    {  // First element of the byte (lower nibble)
                        packed[byte_idx] = (packed[byte_idx] & 0xF0) | nibble_val;
                    }
                    else
                    {  // Second element of the byte (upper nibble)
                        packed[byte_idx] = (packed[byte_idx] & 0x0F) | (nibble_val << 4);
                    }
                }
            }
            else if (width_code_out == static_cast<uint8_t>(dtype::WidthCode::W8))
            {                           // INT8
                size_t byte_idx = idx;  // 1 byte per element
                if (byte_idx < packed.size())
                {
                    packed[byte_idx] = static_cast<uint8_t>(iv & 0xFF);  // Get the lower 8 bits
                }
            }
            else if (width_code_out == static_cast<uint8_t>(dtype::WidthCode::W16))
            {                               // INT16
                size_t byte_idx = idx * 2;  // 2 bytes per element
                if (byte_idx + 1 < packed.size())
                {
                    packed[byte_idx] = static_cast<uint8_t>(iv & 0xFF);           // Lower byte
                    packed[byte_idx + 1] = static_cast<uint8>((iv >> 8) & 0xFF);  // Upper byte
                }
            }
            else
            {
                // Handle unsupported INT width or error
                std::cerr << "Unsupported integer output width: " << (int)width_code_out
                          << std::endl;
            }
        }
    }

    for (uint32_t w = 0; w < (total_bytes + 31) / 32; ++w)
    {
        Word256b word;
        word.fill(0);
        size_t copy_bytes = std::min<size_t>(32, total_bytes - w * 32);
        std::memcpy(word.data(), &packed[w * 32], copy_bytes);
        uint64_t write_addr = m_byte_base_out + w * 32;
        write_data(write_addr, word);
    }
}

void feat_mpu::write_data(uint64_t write_byte_address, Word256b& data)
{
    // Log the write operation details with data content
    SC_INFO("MPU WRITE: function=write_data, addr={:#x}, data_size={} bytes", 
                write_byte_address, sizeof(data));
    
    // Log all the data being written
    std::ostringstream data_stream;
    data_stream << "Write data: ";
    for (size_t i = 0; i < data.size(); ++i) {
        data_stream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(data[i]) << " ";
    }
    SC_INFO("MPU WRITE DATA: {}", data_stream.str());
    
    // Create TLM payload
    m_trans.set_command(tlm::TLM_WRITE_COMMAND);
    m_trans.set_address(write_byte_address);
    m_trans.set_data_ptr(reinterpret_cast<uint8_t*>(data.data()));
    m_trans.set_data_length(sizeof(data));  // 256 bits = 32 bytes
    m_trans.set_streaming_width(sizeof(data));
    m_trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

    // Send transaction through socket
    mem_socket->b_transport(m_trans, m_delay);
    
    // Log the transaction result
    if (m_trans.get_response_status() == tlm::TLM_OK_RESPONSE) {
        SC_INFO("MPU WRITE SUCCESS: addr={:#x}, status=OK", write_byte_address);
    } else {
        SC_ERROR("MPU WRITE ERROR: addr={:#x}, status={}", 
                     write_byte_address, static_cast<int>(m_trans.get_response_status()));
    }
}

Word256b feat_mpu::read_data(uint64_t read_byte_address, MaskInfo& mask_info)
{
    Word256b data;
    
    // Log the read operation details
    SC_INFO("MPU READ: function=read_data, addr={:#x}, data_size={} bytes", 
                read_byte_address, sizeof(data));
    
    // Create TLM payload
    m_trans.set_command(tlm::TLM_READ_COMMAND);
    m_trans.set_address(read_byte_address);
    m_trans.set_data_ptr(reinterpret_cast<uint8_t*>(data.data()));
    m_trans.set_data_length(sizeof(data));
    m_trans.set_streaming_width(sizeof(data));
    m_trans.set_byte_enable_ptr(nullptr);
    m_trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

    // Send transaction through socket
    mem_socket->b_transport(m_trans, m_delay);
    if (m_trans.get_response_status() == tlm::TLM_OK_RESPONSE)
    {
        SC_INFO("MPU READ SUCCESS: addr={:#x}, status=OK", read_byte_address);
        
        // Log all the data read
        std::ostringstream data_stream;
        data_stream << "Read data: ";
        for (size_t i = 0; i < data.size(); ++i) {
            data_stream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(data[i]) << " ";
        }
        SC_INFO("MPU READ DATA: {}", data_stream.str());
        
        if (mask_info.need_mask)
        {
            SC_INFO("MPU READ: Applying mask to data");
            for (size_t i = 0; i < data.size(); i++)
            {
                data[i] &= mask_info.mask[i];
            }
            
            // Log all masked data
            std::ostringstream masked_stream;
            masked_stream << "Masked data: ";
            for (size_t i = 0; i < data.size(); ++i) {
                masked_stream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(data[i]) << " ";
            }
            SC_INFO("MPU READ MASKED DATA: {}", masked_stream.str());
        }
        return data;
    }
    else
    {
        SC_ERROR("MPU READ ERROR: Failed to read data from local memory at address: {:#x}, status={}", 
                     read_byte_address, static_cast<int>(m_trans.get_response_status()));
        return Word256b();
    }
}
