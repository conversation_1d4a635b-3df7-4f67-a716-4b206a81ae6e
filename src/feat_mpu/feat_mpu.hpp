#pragma once

#include <cstdint>
#include <systemc>
#include <vector>
#include "../cim_cluster/inc/dcim_cluster.hpp"
#include "npu_config.h"
#include "sysc/kernel/sc_time.h"
#include "tlm_utils/simple_initiator_socket.h"
#include "tlm_utils/simple_target_socket.h"
#include "utils/register.h"
#include "utils/utils.h"

class feat_mpu : public sc_core::sc_module
{
  public:
    using Word256b = std::array<uint8_t, NPUConfig::LMEM_WD / 8>;
    using IssueQueueCmd = instruction::IssueQueueCmd;
    using MPUConfigManager = hardware_instruction::CimTpConfigManager;
    using MPUConfig = hardware_instruction::CimTensorProcessConfig;
    tlm_utils::simple_target_socket<feat_mpu> cfg_socket;
    tlm_utils::simple_initiator_socket<feat_mpu> mem_socket;
    SC_HAS_PROCESS(feat_mpu);

    // Modified constructor to accept a DcimCluster pointer
    feat_mpu(sc_core::sc_module_name name, DcimCluster* cim_cluster);

  private:
    tlm::tlm_generic_payload m_trans;
    MPUConfigManager m_cfg;
    MPUConfig m_cfg_mpu;

    uint64_t m_byte_base_in;
    uint64_t m_byte_base_out;
    uint64_t m_byte_base_orig;
    uint64_t m_page_idx;
    sc_core::sc_event rec_cfg_event;
    sc_core::sc_event drv_event;
    sc_core::sc_time m_delay;

    IssueQueueCmd m_issue_queue_cmd;
    uint8_t m_prec_in, m_prec_out, m_prec_orig, m_prec_wt;
    uint8_t cim_prec_in, cim_prec_out, cim_prec_orig, cim_prec_wt;
    bool m_cfg_accu, m_cfg_act;
    uint8_t m_cfg_shift;

    // Pointer to the DcimCluster instance
    DcimCluster* m_cim_cluster;

    void b_transport_cfg(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    Word256b read_data(uint64_t read_byte_address, MaskInfo& mask_info);
    void write_data(uint64_t write_byte_address, Word256b& data);
    void thread_cfg();
    void thread_drv();

    // Helper function to convert data to DCIM raw input format
    std::vector<uint8_t> convert_to_raw_input(const std::vector<Word256b>& input_data,
                                              uint32_t total_elements,
                                              uint8_t prec);

    // Processing functions
    void process_gemv();
    void process_gemm();
};