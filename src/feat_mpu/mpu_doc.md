# MPU (Matrix Processing Unit) 设计文档

## 1. 模块概述

MPU (Matrix Processing Unit) 是一个基于 SystemC/TLM 的硬件模块，专门用于执行矩阵和向量的乘法运算。该模块支持 GEMV (General Matrix-Vector multiplication) 和 GEMM (General Matrix-Matrix multiplication) 两种主要操作模式，并与 CIM (Compute-In-Memory) 集群协作完成高性能的矩阵运算。

### 1.1 主要特性

- **双运算模式**：支持 GEMV 和 GEMM 操作
- **多精度支持**：支持 INT4/8/16 和 FP8/16 多种数据格式
- **灵活配置**：支持累加、激活函数(ReLU)、右移等后处理操作
- **TLM 接口**：提供标准的 TLM 2.0 接口用于配置和内存访问
- **CIM 集成**：与 DCIM 集群紧密集成，实现高效的矩阵运算

## 2. 架构设计

### 2.1 整体架构

```mermaid
graph TB
    subgraph "feat_mpu Module"
        subgraph "Configuration Management"
            CM1[MPUConfig Manager]
            CM2[Precision Control]
            CM3[Register Interface]
        end
        
        subgraph "Control Threads"
            CT1[thread_cfg<br/>配置处理线程]
            CT2[thread_drv<br/>执行驱动线程]
        end
        
        subgraph "Data Processing Pipeline"
            DP1[Data Read/Write]
            DP2[Format Conversion]
            DP3[Post Processing<br/>累加/激活/移位]
        end
        
        subgraph "Events"
            EV1[rec_cfg_event]
            EV2[drv_event]
        end
    end
    
    subgraph "External Interfaces"
        EI1[cfg_socket<br/>TLM Target]
        EI2[mem_socket<br/>TLM Initiator]
        EI3[CIM Cluster<br/>DCIM Integration]
    end
    
    EI1 --> CM1
    CM1 --> CT1
    CT1 --> EV1
    EV1 --> CT2
    CT2 --> EV2
    EV2 --> DP1
    DP1 --> DP2
    DP2 --> EI3
    EI3 --> DP3
    DP3 --> EI2
    DP1 <--> EI2
```

### 2.2 主要组件

#### 2.2.1 配置管理器 (MPUConfigManager)
- 管理所有配置寄存器
- 处理精度、操作模式、维度等参数
- 提供配置读写接口

#### 2.2.2 控制线程
- **thread_cfg**: 处理配置命令和预处理指令
- **thread_drv**: 执行矩阵运算驱动指令

#### 2.2.3 数据处理管道
- 数据读取和预处理
- 精度转换和格式化
- 后处理操作 (累加、激活、移位)

## 3. 接口设计

### 3.1 TLM 接口

#### 3.1.1 配置接口 (cfg_socket)
```cpp
tlm_utils::simple_target_socket<feat_mpu> cfg_socket;
```
- **功能**: 接收来自处理器的配置和控制指令
- **数据类型**: IssueQueueCmd 结构体
- **操作类型**: 
  - MP_CFG: 配置寄存器写入
  - GEMV_PRE/GEMM_PRE: 预处理指令
  - GEMV_DRV/GEMM_DRV: 驱动执行指令

#### 3.1.2 内存接口 (mem_socket)
```cpp
tlm_utils::simple_initiator_socket<feat_mpu> mem_socket;
```
- **功能**: 访问本地内存进行数据读写
- **数据宽度**: 256位 (32字节)
- **操作类型**: TLM_READ_COMMAND, TLM_WRITE_COMMAND

### 3.2 CIM 集群接口

```cpp
DcimCluster* m_cim_cluster;
```
- **功能**: 与 DCIM 集群协作执行矩阵运算
- **接口方法**: `computeRawInput()`
- **数据格式**: 原始字节数组，每个元素2字节

## 4. 指令集支持

### 4.1 GEMV 指令序列

| 指令名称   | 功能码    | 操作数                      | 描述                    |
|------------|-----------|----------------------------|-------------------------|
| gemv_pre   | 100_0010  | rs1val=page_idx, rs2val=base_addr_in | 设置页面索引和输入基址 |
| gemv_drv   | 100_0011  | rs1val=base_addr_out, rs2val=base_addr_ori | 设置输出基址并执行运算 |

### 4.2 GEMM 指令序列

| 指令名称   | 功能码    | 操作数                      | 描述                    |
|------------|-----------|----------------------------|-------------------------|
| gemm_pre   | 100_0100  | rs1val=page_idx, rs2val=base_addr_in | 设置页面索引和输入基址 |
| gemm_drv   | 100_0101  | rs1val=base_addr_out, rs2val=base_addr_ori | 设置输出基址并执行运算 |

### 4.3 指令执行流程

#### 4.3.1 GEMV 执行流程

```mermaid
sequenceDiagram
    participant CPU
    participant MPU
    participant Memory
    participant CIM
    
    Note over CPU,CIM: GEMV 操作序列
    
    CPU->>MPU: MP_CFG (配置寄存器)
    MPU->>MPU: 更新配置参数
    
    CPU->>MPU: GEMV_PRE (page_idx, base_addr_in)
    MPU->>MPU: 设置页面索引和输入基址
    
    CPU->>MPU: GEMV_DRV (base_addr_out, base_addr_ori)
    MPU->>MPU: 触发执行流程
    
    loop 读取输入数据
        MPU->>Memory: 读取输入向量数据
        Memory-->>MPU: 返回256位数据
    end
    
    MPU->>MPU: 转换为CIM原始格式
    MPU->>CIM: computeRawInput()
    CIM-->>MPU: 返回计算结果
    
    alt 配置累加
        MPU->>Memory: 读取原始数据
        Memory-->>MPU: 返回原始向量
        MPU->>MPU: 执行累加操作
    end
    
    alt 配置激活函数
        MPU->>MPU: 应用ReLU激活
    end
    
    alt 配置右移
        MPU->>MPU: 执行右移操作
    end
    
    MPU->>MPU: 精度转换和数据打包
    MPU->>Memory: 写入计算结果
    MPU-->>CPU: 完成信号
```

#### 4.3.2 GEMM 执行流程

```mermaid
flowchart TD
    A[开始 GEMM] --> B[接收 GEMM_PRE 指令]
    B --> C[设置 page_idx 和 base_addr_in]
    C --> D[接收 GEMM_DRV 指令]
    D --> E[设置 base_addr_out 和 base_addr_orig]
    E --> F[获取配置参数]
    F --> G[初始化行计数器 row=0]
    
    G --> H{row < input_rows?}
    H -->|是| I[读取当前行输入数据]
    I --> J[转换为CIM原始格式]
    J --> K[调用CIM执行计算]
    K --> L[获取计算结果]
    
    L --> M{配置累加?}
    M -->|是| N[读取原始数据并累加]
    M -->|否| O{配置激活?}
    N --> O
    
    O -->|是| P[应用ReLU激活]
    O -->|否| Q{配置右移?}
    P --> Q
    
    Q -->|是| R[执行右移操作]
    Q -->|否| S[精度转换和打包]
    R --> S
    
    S --> T[写入当前行结果]
    T --> U[row = row + 1]
    U --> H
    
    H -->|否| V[结束 GEMM]
```

## 5. 配置寄存器

### 5.1 精度配置

| 字段名称      | 位域     | 描述                        |
|---------------|----------|----------------------------|
| cfg_type_out  | [1:0]    | 输出张量数据类型            |
| cfg_type_orig | [3:2]    | 原始张量数据类型            |
| cfg_type_in   | [5:4]    | 输入张量数据类型            |
| cfg_type_wt   | [7:6]    | 权重矩阵数据类型            |
| cfg_wd_out    | [10:8]   | 输出张量数据宽度            |
| cfg_wd_orig   | [13:11]  | 原始张量数据宽度            |
| cfg_wd_in     | [16:14]  | 输入张量数据宽度            |
| cfg_wd_wt     | [19:17]  | 权重矩阵数据宽度            |

### 5.2 操作配置

| 字段名称   | 位域     | 描述                        |
|------------|----------|----------------------------|
| cfg_accu   | [20]     | 累加配置 (0=无累加, 1=累加)  |
| cfg_act    | [21]     | 激活函数 (0=无, 1=ReLU)     |
| cfg_shift  | [26:22]  | 右移位数 (仅用于整数输出)   |

### 5.3 张量维度配置

#### 输入张量 (GEMV/GEMM)
- `cfg_size_dim0b_in`: Dim0b 大小
- `cfg_rem_dim0_in`: Dim0 余数
- `cfg_stride_dim1_in`: Dim1 步长 (仅GEMM)

#### 输出张量 (GEMV/GEMM)
- `cfg_size_dim0b_out`: Dim0b 大小
- `cfg_rem_dim0_out`: Dim0 余数
- `cfg_stride_dim1_out`: Dim1 步长 (仅GEMM)

## 6. 数据流设计

### 6.1 GEMV 数据流

```mermaid
graph LR
    A[输入向量<br/>Local Mem<br/>256bit] --> B[精度转换<br/>convert_to<br/>raw_input]
    B --> C[CIM计算<br/>DCIM<br/>Cluster]
    C --> D[后处理<br/>累加/激活/<br/>移位操作]
    D --> E[输出向量<br/>Local Mem<br/>打包输出]
    
    F[原始数据<br/>Local Mem] --> D
```

#### 详细流程：
1. **数据读取**: 从本地内存读取输入向量数据
2. **格式转换**: 将数据转换为 CIM 集群所需的2字节格式
3. **矩阵运算**: 调用 DCIM 集群执行矩阵-向量乘法
4. **后处理**: 根据配置执行累加、激活函数、右移等操作
5. **精度转换**: 将结果转换为目标精度格式
6. **数据写回**: 将结果写入本地内存

### 6.2 GEMM 数据流

```mermaid
graph TD
    A[输入矩阵<br/>Local Mem<br/>多行数据] --> B[逐行处理<br/>行级循环<br/>处理]
    B --> C[CIM计算<br/>DCIM<br/>Cluster]
    C --> D[后处理<br/>累加/激活/<br/>移位操作]
    D --> E[输出矩阵<br/>Local Mem<br/>逐行写入]
    
    F[原始数据<br/>Local Mem] --> D
    B --> B
```

#### 详细流程：
1. **逐行处理**: 对输入矩阵的每一行执行类似 GEMV 的操作
2. **行级循环**: 按行迭代处理整个输入矩阵
3. **内存管理**: 动态计算每行的内存地址和步长

## 7. 精度支持与转换

### 7.1 支持的数据类型

| 类型代码 | 宽度代码 | 数据格式        | 说明          |
|----------|----------|----------------|---------------|
| 00       | 010      | INT4           | 4位整数       |
| 00       | 011      | INT8           | 8位整数       |
| 00       | 100      | INT16          | 16位整数      |
| 01       | 011      | FP8            | 8位浮点       |
| 01       | 100      | FP16/BF16      | 16位浮点      |

### 7.2 精度转换机制

#### 7.2.1 输入数据转换 (convert_to_raw_input)
- 将各种精度的输入数据转换为 CIM 集群所需的2字节格式
- 处理 INT4 的双元素打包
- 处理 INT8/FP8 的零填充
- 处理 INT16/FP16 的直接映射

#### 7.2.2 输出数据打包
- 根据目标精度将计算结果转换为目标格式
- 支持浮点数的 glb_f2mh_conv 转换
- 支持整数的截断和打包
- 处理 INT4 的双元素合并

## 8. 内存访问模式

### 8.1 内存布局

```mermaid
graph LR
    subgraph "Local Memory Layout"
        A[Input Data<br/>byte_base_in]
        B[Weight Data<br/>CIM Cluster]
        C[Output Data<br/>byte_base_out]
        D[Original Data<br/>byte_base_orig]
    end
    
    A -.-> E[256-bit aligned access]
    C -.-> E
    D -.-> E
```

### 8.2 地址计算

#### GEMV 地址模式：
```cpp
read_addr = byte_base_in + dim0b * (NPUConfig::LMEM_WD / 8)
write_addr = byte_base_out + w * 32
```

#### GEMM 地址模式：
```cpp
read_addr = byte_base_in + (row * cfg_stride_dim1_in + dim0b) * (NPUConfig::LMEM_WD / 8)
write_addr = byte_base_out + (row * cfg_stride_dim1_out + w) * 32
```

### 8.3 掩码处理
```cpp
struct MaskInfo {
    bool need_mask;
    std::array<uint8_t, 32> mask;
};
```
- 用于处理非对齐的数据访问
- 支持部分字节的有效性控制

## 9. CIM 集群集成

### 9.1 接口协议
```cpp
bool computeRawInput(
    uint8_t* input_data,        // 输入数据指针
    uint8_t input_precision,    // 输入精度
    uint32_t page_index,        // 权重页面索引
    uint8_t weight_precision,   // 权重精度
    uint8_t* output_buffer      // 输出缓冲区
);
```

### 9.2 数据格式要求
- **输入格式**: 每个元素2字节，按照特定精度编码
- **输出格式**: 64个 FP32 值，总共256字节
- **批处理大小**: GEMV=256个元素，GEMM=256个元素

## 10. 错误处理与调试

### 10.1 错误处理机制
- CIM 计算失败检测
- 内存访问响应状态检查
- 不支持精度格式的错误报告

### 10.2 调试支持
- 可选的输出值打印 (在 process_gemv 中)
- 配置参数的打印输出
- 详细的错误信息输出

## 11. 性能特性

### 11.1 并行处理能力
- 单周期内处理256位数据
- 支持多种精度的并行转换
- 与 CIM 集群的流水线协作

### 11.2 内存带宽优化
- 256位对齐的内存访问
- 批量数据处理减少访问次数
- 高效的数据打包和解包

---

