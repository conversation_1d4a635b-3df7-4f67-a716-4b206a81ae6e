#include <cmath>
#include <cstdint>
#include <iomanip>
#include <iostream>
#include <memory>
#include <string>
#include <systemc>
#include <vector>
#include <sstream>

#include "../../cim_cluster/inc/dcim_cluster.hpp"
#include "../../cim_cluster/inc/dcim_com.h"
#include "../../local_mem/local_mem.h"
#include "../feat_mpu.hpp"
#include "npu_config.h"
#include "utils/register.h"
#include "utils/utils.h"

using namespace sc_core;
using namespace npu_sc;
using namespace instruction::opcode;

class MPUTestBench : public sc_module
{
  public:
    tlm_utils::simple_initiator_socket<MPUTestBench> cfg_socket;

    SC_HAS_PROCESS(MPUTestBench);
    MPUTestBench(sc_module_name name, LocalMemory* local_mem, DcimCluster* dcim_cluster)
        : sc_module(name), m_local_mem(local_mem), m_dcim_cluster(dcim_cluster)
    {
        SC_THREAD(test_thread);
    }

    typedef hardware_instruction::CimTensorProcessConfig MPUConfig;
    typedef hardware_instruction::CimTpConfigManager MPUConfigManager;
    typedef instruction::IssueQueueCmd IssueQueueCmd;
    typedef std::array<uint8_t, NPUConfig::LMEM_WD / 8> Word256b;

    // Test results
    struct TestResult
    {
        std::string test_name;
        bool passed;
        std::string error_message;
    };
    std::vector<TestResult> test_results;

  private:
    LocalMemory* m_local_mem;
    DcimCluster* m_dcim_cluster;
    uint32_t m_gemm_rows = 16;  // Number of rows for GEMM test

    void test_thread()
    {
        std::cout << "Starting MPU tests..." << std::endl;

        test_gemv();
        test_gemm();

        // Print test results
        std::cout << "\n===== Test Results =====\n";
        int passed_count = 0;
        for (const auto& result : test_results)
        {
            std::cout << (result.passed ? "[PASS] " : "[FAIL] ") << result.test_name;
            if (!result.passed)
            {
                std::cout << " - " << result.error_message;
            }
            std::cout << std::endl;
            if (result.passed)
                passed_count++;
        }
        std::cout << "\nPassed " << passed_count << "/" << test_results.size() << " tests\n";
    }

    void sendCommand(const IssueQueueCmd& cmd)
    {
        // Create TLM payload for command
        tlm::tlm_generic_payload trans;
        sc_time delay = SC_ZERO_TIME;
        trans.set_command(tlm::TLM_WRITE_COMMAND);
        trans.set_data_ptr(reinterpret_cast<uint8_t*>(const_cast<IssueQueueCmd*>(&cmd)));
        trans.set_data_length(sizeof(IssueQueueCmd));
        trans.set_streaming_width(sizeof(IssueQueueCmd));
        trans.set_byte_enable_ptr(0);
        trans.set_dmi_allowed(false);
        trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

        // Send command through configuration socket
        cfg_socket->b_transport(trans, delay);

        if (trans.is_response_error())
        {
            SC_REPORT_ERROR("MPUTestBench", "Command send failed");
        }
    }

    void configureOperation(const MPUConfig& config)
    {
        // Configure MPU through configuration interface
        MPUConfigManager cfg_manager;
        cfg_manager.setConfig(config);
        IssueQueueCmd cfg_cmd;

        // Configure the 8 registers
        for (int i = 0; i < 8; i++)
        {
            cfg_cmd.funct7 = MP_CFG;
            cfg_cmd.rs1val = i;
            cfg_cmd.rs2val = cfg_manager.readReg(i);
            sendCommand(cfg_cmd);
            wait(0, SC_NS);
        }
    }

    bool writeMemory(uint32_t unit_index, uint64_t word_offset, const Word256b& data)
    {
        return m_local_mem->write_word(unit_index, word_offset, data);
    }

    bool readMemory(uint32_t unit_index, uint64_t word_offset, Word256b& data)
    {
        return m_local_mem->read_word(unit_index, word_offset, data);
    }

    void clearMemory()
    {
        m_local_mem->reset();
    }

    // Helper to prepare input data for specific precision
    std::vector<Word256b> prepareInputData(uint8_t data_type, const float* input_data)
    {
        std::vector<Word256b> result;
        uint8_t width_code = data_type & 0x07;
        uint8_t type_code = data_type >> 3;

        const uint32_t NUM_INPUTS = 256;  // Fixed input size

        // Calculate total bytes needed and number of Word256b blocks
        uint32_t bits_per_value = width_code_to_bits(static_cast<dtype::WidthCode>(width_code));
        uint32_t total_bits = NUM_INPUTS * bits_per_value;
        uint32_t total_bytes = (total_bits + 7) / 8;       // Round up to nearest byte
        uint32_t bytes_per_word = NPUConfig::LMEM_WD / 8;  // 32 bytes
        uint32_t num_words = (total_bytes + bytes_per_word - 1) / bytes_per_word;

        // Initialize result vector
        result.resize(num_words);
        for (auto& word : result)
        {
            word.fill(0);
        }

        // Pack data based on type
        bool is_float = is_float_type(type_code);

        for (uint32_t i = 0; i < NUM_INPUTS; i++)
        {
            uint32_t packed_value = 0;

            if (is_float)
            {
                // Use glb_f2mh_conv for float types
                packed_value = glb_f2mh_conv(input_data[i], data_type);
            }
            else
            {
                // Cast to integer for integer types
                packed_value = static_cast<uint32_t>(static_cast<int32_t>(input_data[i]));
            }

            // Pack into Word256b based on width
            switch (width_code)
            {
                case static_cast<uint8_t>(dtype::WidthCode::W4):
                {
                    // Two 4-bit values per byte
                    uint32_t byte_idx = i / 2;
                    uint32_t word_idx = byte_idx / bytes_per_word;
                    uint32_t byte_in_word = byte_idx % bytes_per_word;

                    if (word_idx < result.size() && byte_in_word < bytes_per_word)
                    {
                        if (i % 2 == 0)
                        {
                            // Lower nibble
                            result[word_idx][byte_in_word] =
                                (result[word_idx][byte_in_word] & 0xF0) | (packed_value & 0x0F);
                        }
                        else
                        {
                            // Upper nibble
                            result[word_idx][byte_in_word] =
                                (result[word_idx][byte_in_word] & 0x0F) |
                                ((packed_value & 0x0F) << 4);
                        }
                    }
                    break;
                }
                case static_cast<uint8_t>(dtype::WidthCode::W8):
                {
                    // One byte per value
                    uint32_t byte_idx = i;
                    uint32_t word_idx = byte_idx / bytes_per_word;
                    uint32_t byte_in_word = byte_idx % bytes_per_word;

                    if (word_idx < result.size() && byte_in_word < bytes_per_word)
                    {
                        result[word_idx][byte_in_word] = packed_value & 0xFF;
                    }
                    break;
                }
                case static_cast<uint8_t>(dtype::WidthCode::W16):
                {
                    // Two bytes per value (little-endian)
                    uint32_t byte_idx = i * 2;
                    uint32_t word_idx = byte_idx / bytes_per_word;
                    uint32_t byte_in_word = byte_idx % bytes_per_word;

                    if (word_idx < result.size() && byte_in_word + 1 < bytes_per_word)
                    {
                        result[word_idx][byte_in_word] = packed_value & 0xFF;
                        result[word_idx][byte_in_word + 1] = (packed_value >> 8) & 0xFF;
                    }
                    break;
                }
                case static_cast<uint8_t>(dtype::WidthCode::W32):
                {
                    // Four bytes per value (little-endian)
                    uint32_t byte_idx = i * 4;
                    uint32_t word_idx = byte_idx / bytes_per_word;
                    uint32_t byte_in_word = byte_idx % bytes_per_word;

                    if (word_idx < result.size() && byte_in_word + 3 < bytes_per_word)
                    {
                        result[word_idx][byte_in_word] = packed_value & 0xFF;
                        result[word_idx][byte_in_word + 1] = (packed_value >> 8) & 0xFF;
                        result[word_idx][byte_in_word + 2] = (packed_value >> 16) & 0xFF;
                        result[word_idx][byte_in_word + 3] = (packed_value >> 24) & 0xFF;
                    }
                    break;
                }
                default:
                    SC_REPORT_ERROR("MPUTestBench", "Unsupported width code");
                    return result;
            }
        }

        return result;
    }

    // Initialize weights in the CIM cluster
    void initializeWeights(uint8_t weight_type,
                           uint32_t page_idx,
                           const float* weight_data = nullptr)
    {
        uint32_t rows_per_page = 33;  // DCIM_NUM_ROWS_PER_PAGE
        uint32_t bytes_per_row = 32;  // DCIM_NUM_BYTES_PER_ROW

        std::vector<uint8_t> page_buffer(rows_per_page * bytes_per_row, 0);

        uint8_t width_code = weight_type & 0x07;
        uint8_t type_code = weight_type >> 3;
        bool is_float = is_float_type(type_code);

        // 准备权重数据（简化处理，一列32个数据）
        std::vector<float> input_weights(32);
        if (weight_data != nullptr)
        {
            // 使用提供的权重数据
            for (uint32_t i = 0; i < 32; i++)
            {
                input_weights[i] = weight_data[i];
            }
        }
        else
        {
            // 默认所有权重设为1.0
            for (uint32_t i = 0; i < 32; i++)
            {
                input_weights[i] = 1.0f;
            }
        }

        if (is_float)
        {
            // 浮点处理
            // 转换为raw data，类似prepareInputData
            uint16_t data_hex[32];
            for (uint32_t i = 0; i < 32; i++)
            {
                uint32_t raw_value = glb_f2mh_conv(input_weights[i], weight_type);
                data_hex[i] = static_cast<uint16_t>(raw_value);
            }

            // 转换data_type为float_data_align可用的格式
            uint8_t dcim_data_type = dtype_to_dcim_fmt(weight_type);

            // 进行数据对齐
            uint16_t data_algn[33];
            float_data_align(data_hex, dcim_data_type, data_algn);

            // 将data_algn写入page_buffer
            // 对于每一行（33行），将data_algn[row]按照位宽写入该行的32个字节中
            for (uint32_t row = 0; row < rows_per_page; row++)
            {
                uint16_t row_data = data_algn[row];

                for (uint32_t byte = 0; byte < bytes_per_row; byte++)
                {
                    uint8_t value = 0;
                    switch (width_code)
                    {
                        case static_cast<uint8_t>(dtype::WidthCode::W8):
                            // 对于FP8，每个数据只有低8位有效
                            value = row_data & 0xFF;
                            break;
                        case static_cast<uint8_t>(dtype::WidthCode::W16):
                            // 对于FP16，按小端序存储16位数据
                            value = (byte % 2 == 0) ? (row_data & 0xFF) : ((row_data >> 8) & 0xFF);
                            break;
                        case static_cast<uint8_t>(dtype::WidthCode::W32):
                            // 对于FP32，只使用低16位，按小端序存储
                            value = (byte % 4 == 0)   ? (row_data & 0xFF)
                                    : (byte % 4 == 1) ? ((row_data >> 8) & 0xFF)
                                                      : 0x00;
                            break;
                        default:
                            // 其他位宽暂不支持浮点
                            value = 0x00;
                            break;
                    }
                    page_buffer[row * bytes_per_row + byte] = value;
                }
            }
        }
        else
        {
            // 整数处理，也采用float输入转换，类似于prepareInputData
            // 转换为raw data
            std::vector<uint32_t> raw_data(32);
            for (uint32_t i = 0; i < 32; i++)
            {
                // Cast float to integer for integer types
                raw_data[i] = static_cast<uint32_t>(static_cast<int32_t>(input_weights[i]));
            }

            // 按位宽处理，类似prepareInputData的逻辑
            for (uint32_t row = 0; row < rows_per_page; row++)
            {
                for (uint32_t byte = 0; byte < bytes_per_row; byte++)
                {
                    uint8_t value = 0;

                    // 计算在32个权重数据中的索引
                    uint32_t weight_idx = byte % 32;
                    uint32_t packed_value = raw_data[weight_idx];

                    switch (width_code)
                    {
                        case static_cast<uint8_t>(dtype::WidthCode::W4):
                            value = (packed_value & 0x0F) | ((packed_value & 0x0F) << 4);
                            break;
                        case static_cast<uint8_t>(dtype::WidthCode::W8):
                            value = packed_value & 0xFF;
                            break;
                        case static_cast<uint8_t>(dtype::WidthCode::W16):
                            // For INT16: take appropriate byte
                            value = (byte % 2 == 0) ? (packed_value & 0xFF)
                                                    : ((packed_value >> 8) & 0xFF);
                            break;
                        case static_cast<uint8_t>(dtype::WidthCode::W32):
                            // For INT32: take appropriate byte
                            value = (packed_value >> ((byte % 4) * 8)) & 0xFF;
                            break;
                        default:
                            break;
                    }
                    page_buffer[row * bytes_per_row + byte] = value;
                }
            }
        }

        // Write weights to all engines and macros for complete test coverage
        for (size_t engine_idx = 0; engine_idx < 4; engine_idx++)
        {
            for (size_t macro_idx = 0; macro_idx < 2; macro_idx++)
            {
                m_dcim_cluster->writePage(engine_idx, macro_idx, page_idx, page_buffer.data());
            }
        }
    }

    // Helper to send commands to set up and trigger operation
    void setupAndTriggerOperation(uint64_t byte_base_in,
                                  uint64_t byte_base_out,
                                  uint64_t byte_base_orig,
                                  uint32_t page_idx,
                                  bool is_gemm)
    {
        IssueQueueCmd cmd;

        // Set up PRE command
        cmd.funct7 = is_gemm ? GEMM_PRE : GEMV_PRE;
        cmd.rs1val = page_idx;
        cmd.rs2val = byte_base_in;
        sendCommand(cmd);
        wait(0, SC_NS);
        // Set up DRV command
        cmd.funct7 = is_gemm ? GEMM_DRV : GEMV_DRV;
        cmd.rs1val = byte_base_out;
        cmd.rs2val = byte_base_orig;
        sendCommand(cmd);

        // Wait for processing to complete
        wait(100, SC_NS);
    }

    // Verify results against expected values
    bool verifyResults(uint8_t output_type,
                       uint64_t byte_base_out,
                       uint32_t num_outputs,
                       const float* reference_results)
    {
        uint8_t width_code = output_type & 0x07;
        uint8_t type_code = output_type >> 3;
        bool is_float_output = is_float_type(type_code);
        uint32_t bytes_per_value = 0;

        // Get bytes per value
        switch (width_code)
        {
            case static_cast<uint8_t>(dtype::WidthCode::W4):
                bytes_per_value = 1;  // 2 values per byte
                break;
            case static_cast<uint8_t>(dtype::WidthCode::W8):
                bytes_per_value = 1;
                break;
            case static_cast<uint8_t>(dtype::WidthCode::W16):
                bytes_per_value = 2;
                break;
            case static_cast<uint8_t>(dtype::WidthCode::W32):
                bytes_per_value = 4;
                break;
            default:
                SC_REPORT_ERROR("MPUTestBench", "Unsupported width code for verification");
                return false;
        }

        uint32_t total_bytes = num_outputs * bytes_per_value;
        if (width_code == static_cast<uint8_t>(dtype::WidthCode::W4))
        {
            total_bytes = (num_outputs + 1) / 2;  // Two 4-bit values per byte
        }
        uint32_t words_to_read = (total_bytes + 31) / 32;

        for (uint32_t w = 0; w < words_to_read; w++)
        {
            Word256b word;
            uint64_t word_offset = (byte_base_out / 32) + w;

            if (!readMemory(0, word_offset, word))
            {
                SC_REPORT_ERROR("MPUTestBench", "Failed to read output data");
                return false;
            }

            uint32_t values_in_word = 32 / bytes_per_value;
            if (width_code == static_cast<uint8_t>(dtype::WidthCode::W4))
            {
                values_in_word = 64;  // 32 bytes * 2 values per byte
            }

            // Check each value in the word
            for (uint32_t i = 0; i < values_in_word; i++)
            {
                uint32_t global_idx = w * values_in_word + i;
                if (global_idx >= num_outputs)
                    break;

                uint32_t raw_value = 0;
                float actual_value = 0.0f;
                float expected_value = reference_results[global_idx];
                // std::cout << "expected_value: " << expected_value << std::endl;

                // Extract raw value from memory
                switch (width_code)
                {
                    case static_cast<uint8_t>(dtype::WidthCode::W4):
                        if (i % 2 == 0)
                        {
                            raw_value = word[i / 2] & 0x0F;
                        }
                        else
                        {
                            raw_value = (word[i / 2] >> 4) & 0x0F;
                        }
                        break;
                    case static_cast<uint8_t>(dtype::WidthCode::W8):
                        raw_value = word[i];
                        break;
                    case static_cast<uint8_t>(dtype::WidthCode::W16):
                        raw_value = word[i * 2] | (word[i * 2 + 1] << 8);
                        break;
                    case static_cast<uint8_t>(dtype::WidthCode::W32):
                        raw_value = word[i * 4] | (word[i * 4 + 1] << 8) | (word[i * 4 + 2] << 16) |
                                    (word[i * 4 + 3] << 24);
                        break;
                }

                // Convert raw value to actual value for comparison
                if (is_float_output)
                {
                    // Use glb_mh2f_conv for float types
                    glb_mh2f_conv(raw_value, output_type, &actual_value);
                }
                else
                {
                    // Convert integer raw value to signed value
                    int32_t signed_value = uint32_to_int32_prec(raw_value, output_type);
                    actual_value = static_cast<float>(signed_value);
                    // std::cout << "actual_value: " << actual_value << std::endl;
                }

                // Compare with tolerance for float comparisons
                float tolerance = is_float_output ? 1e-6f : 0.0f;
                if (std::abs(actual_value - expected_value) > tolerance)
                {
                    std::stringstream ss;
                    ss << "Output mismatch at index " << global_idx << ": expected "
                       << expected_value << ", got " << actual_value;
                    std::cout << ss.str() << std::endl;
                    return false;
                }
            }
        }

        return true;
    }

    // Helper function to calculate dimensions based on data type
    void calculateDimensions(uint8_t data_type,
                             uint32_t num_elements,
                             uint32_t& size_dim0b,
                             uint32_t& rem_dim0)
    {
        uint8_t width_code = data_type & 0x07;
        uint32_t bits_per_element = width_code_to_bits(static_cast<dtype::WidthCode>(width_code));
        uint32_t total_bits = num_elements * bits_per_element;
        uint32_t total_bytes = (total_bits + 7) / 8;
        uint32_t bytes_per_word = NPUConfig::LMEM_WD / 8;  // 32 bytes

        size_dim0b = (total_bytes + bytes_per_word - 1) / bytes_per_word;
        uint32_t total_used_bytes = size_dim0b * bytes_per_word;
        rem_dim0 = (total_used_bytes > total_bytes) ? (total_used_bytes - total_bytes) : 0;
    }

    // Helper function to calculate output matrix dimensions based on weight type
    uint32_t calculateOutputCols(uint8_t weight_type)
    {
        uint8_t width_code = weight_type & 0x07;
        switch (width_code)
        {
            case static_cast<uint8_t>(dtype::WidthCode::W4):
                return 256 / 4;  // 64 columns for INT4 weights
            case static_cast<uint8_t>(dtype::WidthCode::W8):
                return 256 / 8;  // 32 columns for INT8 weights
            case static_cast<uint8_t>(dtype::WidthCode::W16):
                return 256 / 16;  // 16 columns for INT16 weights
            case static_cast<uint8_t>(dtype::WidthCode::W32):
                return 256 / 32;  // 8 columns for INT32 weights
            default:
                return 64;  // Default fallback
        }
    }

    // Helper function to compute reference GEMV result
    std::vector<float> computeReferenceGEMV(const float* input_vector,
                                            uint32_t output_cols,
                                            const float* weight_vector = nullptr)
    {
        std::vector<float> result(output_cols, 0.0f);

        for (uint32_t out_idx = 0; out_idx < output_cols; out_idx++)
        {
            float sum = 0.0f;
            for (uint32_t in_idx = 0; in_idx < 256; in_idx++)
            {
                if (weight_vector != nullptr)
                {
                    sum += input_vector[in_idx] * weight_vector[in_idx];
                }
                else
                {
                    sum += input_vector[in_idx] * 1.0f;
                }
            }
            result[out_idx] = sum;
        }

        return result;
    }

    // Test GEMV with INT8 activations, INT4 weights, INT16 output
    void test_gemv()
    {
        // Test name
        std::string test_name = "GEMV INT8->INT4->INT16";
        std::cout << "Running test: " << test_name << std::endl;

        clearMemory();

        // Setup configuration
        MPUConfig config;
        config.cfg_type_in = static_cast<uint8_t>(dtype::TypeCode::INT);
        config.cfg_wd_in = static_cast<uint8_t>(dtype::WidthCode::W8);
        config.cfg_type_wt = static_cast<uint8_t>(dtype::TypeCode::INT);
        config.cfg_wd_wt = static_cast<uint8_t>(dtype::WidthCode::W4);
        config.cfg_type_out = static_cast<uint8_t>(dtype::TypeCode::INT);
        config.cfg_wd_out = static_cast<uint8_t>(dtype::WidthCode::W16);
        config.cfg_type_orig = static_cast<uint8_t>(dtype::TypeCode::INT);
        config.cfg_wd_orig = static_cast<uint8_t>(dtype::WidthCode::W8);

        // Auto-calculate input dimensions
        uint8_t input_type = (config.cfg_type_in << 3) | config.cfg_wd_in;
        calculateDimensions(input_type, 256, config.cfg_size_dim0b_in, config.cfg_rem_dim0_in);
        config.cfg_stride_dim1_in = 0;  // Not used for GEMV

        // Auto-calculate output dimensions
        uint8_t weight_type = (config.cfg_type_wt << 3) | config.cfg_wd_wt;
        uint8_t output_type = (config.cfg_type_out << 3) | config.cfg_wd_out;
        uint32_t output_cols = calculateOutputCols(weight_type);
        calculateDimensions(
            output_type, output_cols, config.cfg_size_dim0b_out, config.cfg_rem_dim0_out);
        config.cfg_stride_dim1_out = 0;  // Not used for GEMV

        config.cfg_size_dim1_in = 0;  // Not used for GEMV
        config.cfg_accu = 0;          // No accumulation
        config.cfg_act = 0;           // No activation
        config.cfg_shift = 0;         // No shift

        configureOperation(config);

        // Configure weights (all 1's for simplicity)
        uint32_t page_idx = 0;
        initializeWeights(weight_type, page_idx);

        std::vector<float> input_stimulus(256);
        for (uint32_t i = 0; i < 256; i++)
        {
            input_stimulus[i] = -1.0f;
        }

        // Prepare input data using the stimulus
        auto input_data = prepareInputData(input_type, input_stimulus.data());

        // Write input data to memory
        uint64_t byte_base_in = 0x1000;  // Input base address
        uint64_t word_offset_in = byte_base_in / 32;

        for (uint32_t i = 0; i < input_data.size(); i++)
        {
            if (!writeMemory(0, word_offset_in + i, input_data[i]))
            {
                SC_REPORT_ERROR("MPUTestBench", "Failed to write input data");
                test_results.push_back({test_name, false, "Failed to write input data"});
                return;
            }
        }

        // Setup output memory area
        uint64_t byte_base_out = 0x2000;  // Output base address
        uint64_t byte_base_orig = 0;      // No original data for accumulation

        // Trigger the GEMV operation
        setupAndTriggerOperation(byte_base_in, byte_base_out, byte_base_orig, page_idx, false);

        // Compute reference results
        std::vector<float> reference_results =
            computeReferenceGEMV(input_stimulus.data(), output_cols);

        // Verify results
        bool verification_result =
            verifyResults(output_type, byte_base_out, output_cols, reference_results.data());
        test_results.push_back(
            {test_name,
             verification_result,
             verification_result ? "" : "Output values did not match expected values"});
        /*
        test2:
            input: -1.0
            weight: 1.0
            act = 1
            output = -256
            after act: 0
            reference: 0.0
         */
        std::string test_name2 = "GEMV INT8->INT4->INT16 with ACT";
        config.cfg_act = 1;
        configureOperation(config);
        setupAndTriggerOperation(byte_base_in, byte_base_out, byte_base_orig, page_idx, false);
        for (uint32_t i = 0; i < output_cols; i++)
        {
            reference_results[i] = 0.0f;
        }
        verification_result =
            verifyResults(output_type, byte_base_out, output_cols, reference_results.data());
        test_results.push_back(
            {test_name2,
             verification_result,
             verification_result ? "" : "Output values did not match expected values"});
        /*
        test3:
            input: 1.0
            weight: 1.0
            act = 0
            shift = 2
            output = 256
            after shift: 64
            reference: 64.0
         */
        std::string test_name3 = "GEMV INT8->INT4->INT16 with SHIFT";
        config.cfg_shift = 2;
        config.cfg_act = 0;
        for (uint32_t i = 0; i < 256; i++)
        {
            input_stimulus[i] = 1.0f;
        }
        input_data = prepareInputData(input_type, input_stimulus.data());
        for (uint32_t i = 0; i < input_data.size(); i++)
        {
            if (!writeMemory(0, word_offset_in + i, input_data[i]))
            {
                SC_REPORT_ERROR("MPUTestBench", "Failed to write input data");
                test_results.push_back({test_name, false, "Failed to write input data"});
                return;
            }
        }
        configureOperation(config);
        setupAndTriggerOperation(byte_base_in, byte_base_out, byte_base_orig, page_idx, false);
        for (uint32_t i = 0; i < output_cols; i++)
        {
            reference_results[i] = 64.0f;
        }
        verification_result =
            verifyResults(output_type, byte_base_out, output_cols, reference_results.data());
        test_results.push_back(
            {test_name3,
             verification_result,
             verification_result ? "" : "Output values did not match expected values"});

        /*
        test4:
            input: 1.0 FP16
            weight: 1.0 INT8
            act = 0
            shift = 0
            output = 256 FP16
            after shift: 256
            reference: 256
         */
        std::string test_name4 = "GEMV FP16->INT8->FP16";
        config.cfg_type_in = static_cast<uint8_t>(dtype::TypeCode::FP);
        config.cfg_wd_in = static_cast<uint8_t>(dtype::WidthCode::W16);
        config.cfg_type_wt = static_cast<uint8_t>(dtype::TypeCode::INT);
        config.cfg_wd_wt = static_cast<uint8_t>(dtype::WidthCode::W8);
        config.cfg_type_out = static_cast<uint8_t>(dtype::TypeCode::FP);
        config.cfg_wd_out = static_cast<uint8_t>(dtype::WidthCode::W16);
        config.cfg_type_orig = static_cast<uint8_t>(dtype::TypeCode::FP);
        config.cfg_wd_orig = static_cast<uint8_t>(dtype::WidthCode::W16);

        input_type = (config.cfg_type_in << 3) | config.cfg_wd_in;
        calculateDimensions(input_type, 256, config.cfg_size_dim0b_in, config.cfg_rem_dim0_in);
        config.cfg_stride_dim1_in = 0;  // Not used for GEMV

        weight_type = (config.cfg_type_wt << 3) | config.cfg_wd_wt;
        output_type = (config.cfg_type_out << 3) | config.cfg_wd_out;
        output_cols = calculateOutputCols(weight_type);
        calculateDimensions(
            output_type, output_cols, config.cfg_size_dim0b_out, config.cfg_rem_dim0_out);
        config.cfg_stride_dim1_out = 0;  // Not used for GEMV

        configureOperation(config);
        initializeWeights(weight_type, page_idx);
        for (uint32_t i = 0; i < 256; i++)
        {
            input_stimulus[i] = 1.0f;
        }
        input_data = prepareInputData(input_type, input_stimulus.data());
        for (uint32_t i = 0; i < input_data.size(); i++)
        {
            if (!writeMemory(0, word_offset_in + i, input_data[i]))
            {
                SC_REPORT_ERROR("MPUTestBench", "Failed to write input data");
                test_results.push_back({test_name, false, "Failed to write input data"});
                return;
            }
        }
        setupAndTriggerOperation(byte_base_in, byte_base_out, byte_base_orig, page_idx, false);
        reference_results = computeReferenceGEMV(input_stimulus.data(), output_cols);
        verification_result =
            verifyResults(output_type, byte_base_out, output_cols, reference_results.data());
        test_results.push_back(
            {test_name4,
             verification_result,
             verification_result ? "" : "Output values did not match expected values"});

        /*
        test5:
            input: 1.0 FP16
            weight: 1.0 FP16
            act = 0
            shift = 0
            output = 256 FP16
            after shift: 256
            reference: 256
         */
        std::string test_name5 = "GEMV FP16->FP16->FP16";
        config.cfg_type_in = static_cast<uint8_t>(dtype::TypeCode::FP);
        config.cfg_wd_in = static_cast<uint8_t>(dtype::WidthCode::W16);
        config.cfg_type_wt = static_cast<uint8_t>(dtype::TypeCode::FP);
        config.cfg_wd_wt = static_cast<uint8_t>(dtype::WidthCode::W16);
        config.cfg_type_out = static_cast<uint8_t>(dtype::TypeCode::FP);
        config.cfg_wd_out = static_cast<uint8_t>(dtype::WidthCode::W16);
        input_type = (config.cfg_type_in << 3) | config.cfg_wd_in;
        calculateDimensions(input_type, 256, config.cfg_size_dim0b_in, config.cfg_rem_dim0_in);
        config.cfg_stride_dim1_in = 0;  // Not used for GEMV

        weight_type = (config.cfg_type_wt << 3) | config.cfg_wd_wt;
        output_type = (config.cfg_type_out << 3) | config.cfg_wd_out;
        output_cols = calculateOutputCols(weight_type);
        calculateDimensions(
            output_type, output_cols, config.cfg_size_dim0b_out, config.cfg_rem_dim0_out);
        config.cfg_stride_dim1_out = 0;  // Not used for GEMV

        configureOperation(config);
        initializeWeights(weight_type, page_idx, input_stimulus.data());
        setupAndTriggerOperation(byte_base_in, byte_base_out, byte_base_orig, page_idx, false);
        reference_results = computeReferenceGEMV(input_stimulus.data(), output_cols);
        verification_result =
            verifyResults(output_type, byte_base_out, output_cols, reference_results.data());
        test_results.push_back(
            {test_name5,
             verification_result,
             verification_result ? "" : "Output values did not match expected values"});

        /*
        test6:
            input: 1.0 FP16
            weight: 1.0 FP16
            act = 0
            shift = 0
            output = 256 INT8
         */
        std::string test_name6 = "GEMV FP16->FP16->INT8";
        config.cfg_type_out = static_cast<uint8_t>(dtype::TypeCode::INT);
        config.cfg_wd_out = static_cast<uint8_t>(dtype::WidthCode::W16);
        output_type = (config.cfg_type_out << 3) | config.cfg_wd_out;
        output_cols = calculateOutputCols(weight_type);
        calculateDimensions(
            output_type, output_cols, config.cfg_size_dim0b_out, config.cfg_rem_dim0_out);
        config.cfg_stride_dim1_out = 0;  // Not used for GEMV
        config.cfg_size_dim1_in = 0;     // Not used for GEMV
        config.cfg_accu = 0;             // No accumulation
        config.cfg_act = 0;              // No activation
        config.cfg_shift = 0;            // No shift
        configureOperation(config);
        initializeWeights(weight_type, page_idx, input_stimulus.data());
        setupAndTriggerOperation(byte_base_in, byte_base_out, byte_base_orig, page_idx, false);
        reference_results = computeReferenceGEMV(input_stimulus.data(), output_cols);
        verification_result =
            verifyResults(output_type, byte_base_out, output_cols, reference_results.data());
        test_results.push_back(
            {test_name6,
             verification_result,
             verification_result ? "" : "Output values did not match expected values"});
    }

    // Test GEMM with INT8 activations, INT4 weights, INT16 output
    void test_gemm()
    {
        // Test name
        std::string test_name = "GEMM INT8->INT4->INT16";
        std::cout << "Running test: " << test_name << std::endl;

        clearMemory();

        // Setup configuration
        MPUConfig config;
        config.cfg_type_in = static_cast<uint8_t>(dtype::TypeCode::INT);
        config.cfg_wd_in = static_cast<uint8_t>(dtype::WidthCode::W8);
        config.cfg_type_wt = static_cast<uint8_t>(dtype::TypeCode::INT);
        config.cfg_wd_wt = static_cast<uint8_t>(dtype::WidthCode::W4);
        config.cfg_type_out = static_cast<uint8_t>(dtype::TypeCode::INT);
        config.cfg_wd_out = static_cast<uint8_t>(dtype::WidthCode::W16);
        config.cfg_type_orig = static_cast<uint8_t>(dtype::TypeCode::INT);
        config.cfg_wd_orig = static_cast<uint8_t>(dtype::WidthCode::W8);

        uint8_t input_type = (config.cfg_type_in << 3) | config.cfg_wd_in;
        calculateDimensions(input_type, 256, config.cfg_size_dim0b_in, config.cfg_rem_dim0_in);
        config.cfg_stride_dim1_in = config.cfg_size_dim0b_in;  // Not used for GEMM

        uint8_t weight_type = (config.cfg_type_wt << 3) | config.cfg_wd_wt;
        uint8_t output_type = (config.cfg_type_out << 3) | config.cfg_wd_out;
        uint32_t output_cols = calculateOutputCols(weight_type);
        calculateDimensions(
            output_type, output_cols, config.cfg_size_dim0b_out, config.cfg_rem_dim0_out);
        config.cfg_stride_dim1_out = config.cfg_size_dim0b_out;  // Not used for GEMM
        config.cfg_size_dim1_in = m_gemm_rows;                   // Process 16 rows
        config.cfg_accu = 0;                                     // No accumulation
        config.cfg_act = 0;                                      // No activation
        config.cfg_shift = 0;                                    // No shift

        configureOperation(config);

        // Configure weights (all 1's)

        uint32_t page_idx = 0;
        initializeWeights(weight_type, page_idx);

        // Prepare input data (all 1's for all rows)
        std::vector<float> input_stimulus(256);
        for (uint32_t i = 0; i < 256; i++)
        {
            input_stimulus[i] = 1.0f;
        }
        auto row_input_data = prepareInputData(input_type, input_stimulus.data());

        // Write input data to memory for all rows
        uint64_t byte_base_in = 0x1000;
        uint64_t word_offset_in = byte_base_in / 32;

        for (uint32_t row = 0; row < m_gemm_rows; row++)
        {
            for (uint32_t i = 0; i < row_input_data.size(); i++)
            {
                uint64_t offset = word_offset_in + row * config.cfg_stride_dim1_in + i;
                if (!writeMemory(0, offset, row_input_data[i]))
                {
                    SC_REPORT_ERROR("MPUTestBench", "Failed to write input data for GEMM");
                    test_results.push_back({test_name, false, "Failed to write input data"});
                    return;
                }
            }
        }

        // Setup output memory area
        uint64_t byte_base_out = 0x10000;  // Output base address
        uint64_t byte_base_orig = 0;       // No original data for accumulation

        // Trigger the GEMM operation
        setupAndTriggerOperation(byte_base_in, byte_base_out, byte_base_orig, page_idx, true);

        // Verify results for each row
        bool all_rows_passed = true;
        for (uint32_t row = 0; row < m_gemm_rows; row++)
        {
            uint64_t row_output_base = byte_base_out + (row * config.cfg_stride_dim1_out * 32);

            // Compute reference results for this row
            std::vector<float> row_reference_results =
                computeReferenceGEMV(input_stimulus.data(), output_cols);

            bool row_passed = verifyResults((config.cfg_type_out << 3) | config.cfg_wd_out,
                                            row_output_base,
                                            output_cols,
                                            row_reference_results.data());

            if (!row_passed)
            {
                all_rows_passed = false;
                break;
            }
        }

        test_results.push_back(
            {test_name,
             all_rows_passed,
             all_rows_passed ? "" : "GEMM output values did not match expected values"});
    }
};

int sc_main(int argc, char* argv[])
{
    // Create instances
    DcimCluster dcim_cluster;
    LocalMemory local_mem("local_mem", &dcim_cluster);
    feat_mpu mpu("feat_mpu", &dcim_cluster);
    MPUTestBench testbench("mpu_testbench", &local_mem, &dcim_cluster);

    // Connect modules
    testbench.cfg_socket.bind(mpu.cfg_socket);
    mpu.mem_socket.bind(local_mem.target_socket);

    // Start simulation
    std::cout << "Starting SystemC simulation..." << std::endl;
    sc_start(10000, SC_NS);
    std::cout << "Simulation completed." << std::endl;

    return 0;
}
