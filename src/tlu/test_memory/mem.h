#pragma once
#include <tlm.h>
#include <tlm_utils/simple_initiator_socket.h>
#include <tlm_utils/simple_target_socket.h>
#include <systemc>
#include <vector>
#include "sysc/kernel/sc_module.h"

// Memory size settings (256-bit stride)
#define MEM_SIZE (16 * 256 / 8)
#define dim0 MEM_SIZE
#define dim1 MEM_SIZE
#define dim2 MEM_SIZE

#define STRIDE_DIM0 (32)
#define STRIDE_DIM1 (16 * 32)
#define STRIDE_DIM2 (16 * 16 * 32)

class Memory : public sc_core::sc_module
{
  public:
    tlm_utils::simple_target_socket<Memory> socket_lmem;
    tlm_utils::simple_target_socket<Memory> socket_gmem;

    Memory(sc_core::sc_module_name name);
    void b_transport_lmem(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    void b_transport_gmem(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    SC_HAS_PROCESS(Memory);
  private:
    std::vector<uint8_t> lmem;  // Local memory
    std::vector<uint8_t> gmem;  // Global memory
};