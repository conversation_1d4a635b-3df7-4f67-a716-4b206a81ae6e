#include <tlm_utils/simple_initiator_socket.h>
#include <tlm_utils/simple_target_socket.h>
#include <map>
#include <systemc>
#include <tlm>
#include "../test_memory/mem.h"
#include "../tlu_cfg_reg/tlu_cfg_reg.h"
#include "../tlu_desc_gen/tlu_desc_gen.h"

class TestWriteLmem : public sc_core::sc_module
{
  public:
    tlm_utils::simple_initiator_socket<TestWriteLmem> tlu_cfg_socket;

    SC_HAS_PROCESS(TestWriteLmem);
    TestWriteLmem(sc_core::sc_module_name name) : sc_module(name), tlu_cfg_socket("tlu_cfg_socket")
    {
        SC_THREAD(test_process);
    }

    void configure_tlu(const TluConfig& cfg, const TluADDR& addr)
    {
        tlm::tlm_generic_payload trans;
        sc_core::sc_time delay = sc_core::SC_ZERO_TIME;

        // Configure TLU registers using TLU_Command
        TLU_Command cmd;
        cmd.func7 = TLU_CMD_TYPE::TST_CFG;
        cmd.xs2 = true;
        cmd.xs1 = true;
        cmd.xd = false;

        // Configure each field using FIELD_DEFS
        const uint32_t cfg_data[] = {cfg.cfg_type,
                                     cfg.cfg_wd,
                                     cfg.cfg_rem_dim0,
                                     cfg.cfg_size_dim0b,
                                     cfg.cfg_size_dim1,
                                     cfg.cfg_size_dim2,
                                     cfg.cfg_stride_dim1_gmem,
                                     cfg.cfg_stride_dim2_gmem,
                                     cfg.cfg_stride_dim1_lmem,
                                     cfg.cfg_stride_dim2_lmem};

        // Create a map to store register values
        std::map<uint32_t, uint32_t> reg_values;

        // First pass: combine fields for the same register
        for (int i = 0; i < FIELD_COUNT; i++)
        {
            uint32_t reg_addr = FIELD_DEFS[i].addr;
            uint32_t field_value = cfg_data[i];
            uint32_t field_mask = ((1u << (FIELD_DEFS[i].left - FIELD_DEFS[i].right + 1)) - 1)
                                  << FIELD_DEFS[i].right;

            reg_values[reg_addr] &= ~field_mask;
            reg_values[reg_addr] |= (field_value << FIELD_DEFS[i].right) & field_mask;
        }

        // Second pass: write combined register values
        for (const auto& reg : reg_values)
        {
            cmd.rs1val = reg.first;   // Register address
            cmd.rs2val = reg.second;  // Combined register value

            trans.set_command(tlm::TLM_WRITE_COMMAND);
            trans.set_address(0);  // Command address is 0
            trans.set_data_ptr(reinterpret_cast<unsigned char*>(&cmd));
            trans.set_data_length(sizeof(TLU_Command));
            trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

            tlu_cfg_socket->b_transport(trans, delay);
            wait(0, sc_core::SC_NS);
            if (trans.is_response_error())
            {
                SC_REPORT_ERROR("TestWriteLmem", "Configuration write failed");
            }
        }

        // Configure addresses using TST_DRV command
        cmd.func7 = TLU_CMD_TYPE::TST_DRV;
        cmd.rs1val = addr.byte_base_lmem;
        cmd.rs2val = addr.byte_base_gmem;

        trans.set_command(tlm::TLM_WRITE_COMMAND);
        trans.set_address(0);  // Command address is 0
        trans.set_data_ptr(reinterpret_cast<unsigned char*>(&cmd));
        trans.set_data_length(sizeof(TLU_Command));
        trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

        tlu_cfg_socket->b_transport(trans, delay);

        if (trans.is_response_error())
        {
            SC_REPORT_ERROR("TestWriteLmem", "Address configuration failed");
        }
    }

    void test_process()
    {
        // Wait for initialization
        wait(10, sc_core::SC_NS);

        // Example test configuration
        TluConfig test_cfg;
        test_cfg.cfg_type = 0x1;  // Write operation
        test_cfg.cfg_wd = 0;      // 32-bit width
        test_cfg.cfg_rem_dim0 = 0;
        test_cfg.cfg_size_dim0b = 8;  // Size of dimension 0b
        test_cfg.cfg_size_dim1 = 2;   // Size of dimension 1
        test_cfg.cfg_size_dim2 = 2;   // Size of dimension 2
        test_cfg.cfg_stride_dim1_gmem = 16;
        test_cfg.cfg_stride_dim2_gmem = 16 * 16;
        test_cfg.cfg_stride_dim1_lmem = 16;
        test_cfg.cfg_stride_dim2_lmem = 16 * 16;

        TluADDR test_addr;
        test_addr.byte_base_lmem = 0x0000;
        test_addr.byte_base_gmem = 0x0000;

        // Configure TLU with test values
        configure_tlu(test_cfg, test_addr);
        wait(0, sc_core::SC_NS);

        // Start from (idx_dim0b,idx_dim1,idx_dim2) = (1,1,2)
        test_addr.byte_base_lmem = 0x0000 + 16 * 16 * 2 * 32 + 16 * 1 * 32 + 32;
        test_addr.byte_base_gmem = 0x0000 + 16 * 16 * 2 * 32 + 16 * 1 * 32 + 32;
        configure_tlu(test_cfg, test_addr);
        wait(0, sc_core::SC_NS);

        // Wait for operation to complete
        wait(10, sc_core::SC_NS);
    }
};

// Test module instantiation
class Top : public sc_core::sc_module
{
  public:
    TestWriteLmem test_write_lmem;
    Memory lmem;
    TluDescGen tlu_desc_gen;

    Top(sc_core::sc_module_name name)
        : sc_module(name),
          test_write_lmem("test_write_lmem"),
          lmem("lmem"),
          tlu_desc_gen("tlu_desc_gen")
    {
        test_write_lmem.tlu_cfg_socket.bind(tlu_desc_gen.target_tlu_cfg);
        tlu_desc_gen.initiator_lmem.bind(lmem.socket_lmem);
        tlu_desc_gen.initiator_gmem.bind(lmem.socket_gmem);
    }
};

int sc_main(int argc, char* argv[])
{
    Top top("top");
    sc_core::sc_start(1000, sc_core::SC_NS);
    return 0;
}