#pragma once
#include <cstdint>
#include <systemc>
#include "../tlu_cfg_reg/tlu_cfg_reg.h"
#include "npu_config.h"
#include "sysc/kernel/sc_event.h"
#include "tlm_core/tlm_2/tlm_generic_payload/tlm_gp.h"
#include "tlm_utils/simple_initiator_socket.h"
#include "tlm_utils/simple_target_socket.h"

struct TLU_Command
{
    uint8_t func7;
    bool xs2;
    bool xs1;
    bool xd;
    std::conditional_t<static_cast<int>(NPUConfig::RV_XLEN) == 32, uint32_t, uint64_t> rs2val;
    std::conditional_t<static_cast<int>(NPUConfig::RV_XLEN) == 32, uint32_t, uint64_t> rs1val;
};

struct TluADDR
{
    std::conditional_t<static_cast<int>(NPUConfig::RV_XLEN) == 32, uint32_t, uint64_t> byte_base_lmem;
    std::conditional_t<static_cast<int>(NPUConfig::RV_XLEN) == 32, uint32_t, uint64_t> byte_base_gmem;
};

enum TLU_CMD_TYPE : uint8_t
{
    TST_CFG = 0b00100000,
    TST_DRV = 0b00101000
};

class TluDescGen : public sc_core::sc_module
{
  public:
    tlm_utils::simple_target_socket<TluDescGen> target_tlu_cfg;
    tlm_utils::simple_initiator_socket<TluDescGen> initiator_gmem;
    tlm_utils::simple_initiator_socket<TluDescGen> initiator_lmem;

    TluDescGen(sc_core::sc_module_name name);
    SC_HAS_PROCESS(TluDescGen);
    void b_transport(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    TluConfig cfg;

  private:
    sc_core::sc_time req_lmem_delay, req_gmem_delay;
    tlm::tlm_generic_payload trans;
    TLU_Command tlu_cmd;
    void thread_cmd();
    void thread_gmem_read();
    sc_core::sc_event gmem_read_event;
    sc_core::sc_event recv_event;
    RegisterManager reg_manager;
    TluADDR tlu_addr;
    uint8_t data_buf[32];
};