#pragma once
#include <map>
#include <stdexcept>
#include <systemc>

// 定义配置结构体
struct TsuConfig
{
    uint32_t cfg_type;
    uint32_t cfg_wd;
    uint32_t cfg_rem_dim0;
    uint32_t cfg_size_dim0b;
    uint32_t cfg_size_dim1;
    uint32_t cfg_size_dim2;
    uint32_t cfg_stride_dim1_gmem;
    uint32_t cfg_stride_dim2_gmem;
    uint32_t cfg_stride_dim1_lmem;
    uint32_t cfg_stride_dim2_lmem;
};

// 字段定义结构体
struct FieldDef
{
    uint32_t addr;
    uint32_t left;
    uint32_t right;
    const char* name;
};

// 字段枚举
enum FieldIndex
{
    CFG_TYPE,
    CFG_WD,
    CFG_REM_DIM0,
    CFG_SIZE_DIM0B,
    CFG_SIZE_DIM1,
    CFG_SIZE_DIM2,
    CFG_STRIDE_DIM1_GMEM,
    CFG_STRIDE_DIM2_GMEM,
    CFG_STRIDE_DIM1_LMEM,
    CFG_STRIDE_DIM2_LMEM,
    FIELD_COUNT
};

// 字段定义表
const FieldDef FIELD_DEFS[FIELD_COUNT] = {{0, 1, 0, "cfg_type"},
                                          {0, 4, 2, "cfg_wd"},
                                          {1, 5, 0, "cfg_rem_dim0"},
                                          {1, 16, 6, "cfg_size_dim0b"},
                                          {2, 12, 0, "cfg_size_dim1"},
                                          {2, 25, 13, "cfg_size_dim2"},
                                          {3, 12, 0, "cfg_stride_dim1_gmem"},
                                          {4, 24, 0, "cfg_stride_dim2_gmem"},
                                          {5, 12, 0, "cfg_stride_dim1_lmem"},
                                          {6, 24, 0, "cfg_stride_dim2_lmem"}};

class RegisterManager
{
  private:
    std::map<uint32_t, sc_dt::sc_uint<32>> registers;

    // 从寄存器中读取指定字段的值
    uint32_t readField(const FieldDef& field)
    {
        if (registers.find(field.addr) == registers.end())
        {
            registers[field.addr] = 0;
        }
        return (registers[field.addr].range(field.left, field.right)).to_uint();
    }

  public:
    RegisterManager()
    {
        // 初始化所有寄存器为0
        for (int i = 0; i < FIELD_COUNT; i++)
        {
            registers[FIELD_DEFS[i].addr] = 0;
        }
    }

    // 写入寄存器
    bool writeReg(uint32_t addr, uint32_t value)
    {
        try
        {
            // 找到所有与该地址相关的字段
            std::vector<const FieldDef*> addr_fields;
            for (int i = 0; i < FIELD_COUNT; i++)
            {
                if (FIELD_DEFS[i].addr == addr)
                {
                    addr_fields.push_back(&FIELD_DEFS[i]);
                }
            }

            if (addr_fields.empty())
            {
                throw std::runtime_error("Invalid register address");
            }

            // 如果寄存器不存在，初始化为0
            if (registers.find(addr) == registers.end())
            {
                registers[addr] = 0;
            }

            // 保存原始值
            registers[addr] = value;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cerr << "Error writing register: " << e.what() << std::endl;
            return false;
        }
    }

    // 读取寄存器
    uint32_t readReg(uint32_t addr)
    {
        try
        {
            const FieldDef* field = nullptr;
            for (int i = 0; i < FIELD_COUNT; i++)
            {
                if (FIELD_DEFS[i].addr == addr)
                {
                    field = &FIELD_DEFS[i];
                    break;
                }
            }

            if (!field)
            {
                throw std::runtime_error("Invalid register address");
            }

            return readField(*field);
        }
        catch (const std::exception& e)
        {
            std::cerr << "Error reading register: " << e.what() << std::endl;
            return 0;
        }
    }

    // 获取完整配置
    TsuConfig getConfig()
    {
        TsuConfig cfg;

        cfg.cfg_type = readField(FIELD_DEFS[CFG_TYPE]);
        cfg.cfg_wd = readField(FIELD_DEFS[CFG_WD]);
        cfg.cfg_rem_dim0 = readField(FIELD_DEFS[CFG_REM_DIM0]);
        cfg.cfg_size_dim0b = readField(FIELD_DEFS[CFG_SIZE_DIM0B]);
        cfg.cfg_size_dim1 = readField(FIELD_DEFS[CFG_SIZE_DIM1]);
        cfg.cfg_size_dim2 = readField(FIELD_DEFS[CFG_SIZE_DIM2]);
        cfg.cfg_stride_dim1_gmem = readField(FIELD_DEFS[CFG_STRIDE_DIM1_GMEM]);
        cfg.cfg_stride_dim2_gmem = readField(FIELD_DEFS[CFG_STRIDE_DIM2_GMEM]);
        cfg.cfg_stride_dim1_lmem = readField(FIELD_DEFS[CFG_STRIDE_DIM1_LMEM]);
        cfg.cfg_stride_dim2_lmem = readField(FIELD_DEFS[CFG_STRIDE_DIM2_LMEM]);
        return cfg;
    }

    // 设置完整配置
    bool setConfig(const TsuConfig& cfg)
    {
        bool success = true;
        success &= writeReg(FIELD_DEFS[CFG_TYPE].addr, cfg.cfg_type);
        success &= writeReg(FIELD_DEFS[CFG_WD].addr, cfg.cfg_wd);
        success &= writeReg(FIELD_DEFS[CFG_REM_DIM0].addr, cfg.cfg_rem_dim0);
        success &= writeReg(FIELD_DEFS[CFG_SIZE_DIM0B].addr, cfg.cfg_size_dim0b);
        success &= writeReg(FIELD_DEFS[CFG_SIZE_DIM1].addr, cfg.cfg_size_dim1);
        success &= writeReg(FIELD_DEFS[CFG_SIZE_DIM2].addr, cfg.cfg_size_dim2);
        success &= writeReg(FIELD_DEFS[CFG_STRIDE_DIM1_GMEM].addr, cfg.cfg_stride_dim1_gmem);
        success &= writeReg(FIELD_DEFS[CFG_STRIDE_DIM2_GMEM].addr, cfg.cfg_stride_dim2_gmem);
        success &= writeReg(FIELD_DEFS[CFG_STRIDE_DIM1_LMEM].addr, cfg.cfg_stride_dim1_lmem);
        success &= writeReg(FIELD_DEFS[CFG_STRIDE_DIM2_LMEM].addr, cfg.cfg_stride_dim2_lmem);
        return success;
    }

    // 重置所有寄存器
    void reset()
    {
        for (auto& reg : registers)
        {
            reg.second = 0;
        }
    }

    // 打印所有寄存器的值
    void printAllRegs()
    {
        std::cout << "Register Values:" << std::endl;
        for (int i = 0; i < FIELD_COUNT; i++)
        {
            const auto& field = FIELD_DEFS[i];
            std::cout << field.name << " [" << field.left << ":" << field.right << "] = 0x"
                      << std::hex << readField(field) << std::dec << std::endl;
        }
    }

    // 打印当前配置
    void printConfig()
    {
        TsuConfig cfg = getConfig();
        std::cout << "Current Configuration:" << std::endl;
        std::cout << "cfg_type: " << cfg.cfg_type << std::endl;
        std::cout << "cfg_wd: " << cfg.cfg_wd << std::endl;
        std::cout << "cfg_rem_dim0: " << cfg.cfg_rem_dim0 << std::endl;
        std::cout << "cfg_size_dim0b: " << cfg.cfg_size_dim0b << std::endl;
        std::cout << "cfg_size_dim1: " << cfg.cfg_size_dim1 << std::endl;
        std::cout << "cfg_size_dim2: " << cfg.cfg_size_dim2 << std::endl;
        std::cout << "cfg_stride_dim1_gmem: " << cfg.cfg_stride_dim1_gmem << std::endl;
        std::cout << "cfg_stride_dim2_gmem: " << cfg.cfg_stride_dim2_gmem << std::endl;
        std::cout << "cfg_stride_dim1_lmem: " << cfg.cfg_stride_dim1_lmem << std::endl;
        std::cout << "cfg_stride_dim2_lmem: " << cfg.cfg_stride_dim2_lmem << std::endl;
    }
};