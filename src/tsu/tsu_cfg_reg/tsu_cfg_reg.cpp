#include "tsu_cfg_reg.h"

int sc_main(int argc, char* argv[])
{
    RegisterManager regMgr;

    // 单个寄存器操作
    regMgr.writeReg(0, 0x1023012);

    // 使用结构体设置所有配置
    TsuConfig cfg;
    cfg.cfg_type = 1;
    cfg.cfg_wd = 2;
    cfg.cfg_rem_dim0 = 32;
    cfg.cfg_size_dim0b = 1;
    cfg.cfg_size_dim1 = 2;
    cfg.cfg_size_dim2 = 3;
    cfg.cfg_stride_dim1_gmem = 4;
    cfg.cfg_stride_dim2_gmem = 5;
    cfg.cfg_stride_dim1_lmem = 6;
    cfg.cfg_stride_dim2_lmem = 7;
    regMgr.setConfig(cfg);

    // 打印配置
    regMgr.printConfig();

    // 打印所有寄存器值
    regMgr.printAllRegs();

    // 重置
    regMgr.reset();

    return 0;
}