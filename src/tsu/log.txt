---test_tsu.cpp
#include <systemc>
#include <tlm>
#include <map>
#include <tlm_utils/simple_initiator_socket.h>
#include <tlm_utils/simple_target_socket.h>
#include "../test_memory/mem.h"
#include "../tsu_cfg_reg/tsu_cfg_reg.h"
#include "../tsu_desc_gen/tsu_desc_gen.h"

// Number of configuration fields

class TestReadLmem : public sc_core::sc_module {
public:
    tlm_utils::simple_initiator_socket<TestReadLmem> tsu_cfg_socket;

    SC_HAS_PROCESS(TestReadLmem);
    TestReadLmem(sc_core::sc_module_name name) : 
        sc_module(name),
        tsu_cfg_socket("tsu_cfg_socket")
    {
        SC_THREAD(test_process);
    }

    void configure_tsu(const TsuConfig& cfg, const TsuADDR& addr) {
        tlm::tlm_generic_payload trans;
        sc_core::sc_time delay = sc_core::SC_ZERO_TIME;

        // Configure TSU registers using TSU_Command
        TSU_Command cmd;
        cmd.func7 = TSU_CMD_TYPE::TST_CFG;
        cmd.xs2 = true;
        cmd.xs1 = true;
        cmd.xd = false;

        // Configure each field using FIELD_DEFS
        const uint32_t cfg_data[] = {
            cfg.cfg_type,
            cfg.cfg_wd,
            cfg.cfg_rem_dim0,
            cfg.cfg_size_dim0b,
            cfg.cfg_size_dim1,
            cfg.cfg_size_dim2,
            cfg.cfg_stride_dim1_gmem,
            cfg.cfg_stride_dim2_gmem,
            cfg.cfg_stride_dim1_lmem,
            cfg.cfg_stride_dim2_lmem
        };

        // Create a map to store register values
        std::map<uint32_t, uint32_t> reg_values;

        // First pass: combine fields for the same register
        for (int i = 0; i < FIELD_COUNT; i++) {
            uint32_t reg_addr = FIELD_DEFS[i].addr;
            uint32_t field_value = cfg_data[i];
            uint32_t field_mask = ((1u << (FIELD_DEFS[i].left - FIELD_DEFS[i].right + 1)) - 1) << FIELD_DEFS[i].right;
            
            // Clear the bits in the field position and set new value
            reg_values[reg_addr] &= ~field_mask;
            reg_values[reg_addr] |= (field_value << FIELD_DEFS[i].right) & field_mask;
        }

        // Second pass: write combined register values
        for (const auto& reg : reg_values) {
            cmd.rs1val = reg.first ;  // Register address
            cmd.rs2val = reg.second;                    // Combined register value

            trans.set_command(tlm::TLM_WRITE_COMMAND);
            trans.set_address(0);  // Command address is 0
            trans.set_data_ptr(reinterpret_cast<unsigned char*>(&cmd));
            trans.set_data_length(sizeof(TSU_Command));
            trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

            tsu_cfg_socket->b_transport(trans, delay);
            wait(0,sc_core::SC_NS);
            if (trans.is_response_error()) {
                SC_REPORT_ERROR("TestReadLmem", "Configuration write failed");
            }
        }

        // Configure addresses using TST_DRV command
        cmd.func7 = TSU_CMD_TYPE::TST_DRV;
        cmd.rs1val = addr.byte_base_lmem;
        cmd.rs2val = addr.byte_base_gmem;

        trans.set_command(tlm::TLM_WRITE_COMMAND);
        trans.set_address(0);  // Command address is 0
        trans.set_data_ptr(reinterpret_cast<unsigned char*>(&cmd));
        trans.set_data_length(sizeof(TSU_Command));
        trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

        tsu_cfg_socket->b_transport(trans, delay);
        
        if (trans.is_response_error()) {
            SC_REPORT_ERROR("TestReadLmem", "Address configuration failed");
        }
    }

    void test_process() {
        // Wait for initialization
        wait(10, sc_core::SC_NS);

        // Example test configuration
        TsuConfig test_cfg;
        test_cfg.cfg_type = 0x1;          // Read operation
        test_cfg.cfg_wd = 0;             // 32-bit width
        test_cfg.cfg_rem_dim0 = 0;
        test_cfg.cfg_size_dim0b = 8;     // Size of dimension 0b
        test_cfg.cfg_size_dim1 = 2;       // Size of dimension 1
        test_cfg.cfg_size_dim2 = 2;       // Size of dimension 2
        test_cfg.cfg_stride_dim1_gmem = 16;
        test_cfg.cfg_stride_dim2_gmem = 16*16;
        test_cfg.cfg_stride_dim1_lmem = 16;
        test_cfg.cfg_stride_dim2_lmem = 16*16;

        TsuADDR test_addr;
        test_addr.byte_base_lmem = 0x0000;
        test_addr.byte_base_gmem = 0x0000;

        // Configure TSU with test values
        configure_tsu(test_cfg, test_addr);
        wait(0,sc_core::SC_NS);
        // 从(idx_dim0b,idx_dim1,idx_dim2) = (1,1,2)开始读
        test_addr.byte_base_lmem = 0x0000 + 16*16*2*32 + 16*1*32 + 32;
        test_addr.byte_base_gmem = 0x0000 + 16*16*2*32 + 16*1*32 + 32;   
        configure_tsu(test_cfg, test_addr);
        wait(0,sc_core::SC_NS);
        // Wait for operation to complete
        wait(10, sc_core::SC_NS);
    }

private:
    
};

// Test module instantiation
class Top:public sc_core::sc_module{
    public:
    TestReadLmem test_read_lmem;
    Memory lmem;
    TsuDescGen tsu_desc_gen;
    Top(sc_core::sc_module_name name):sc_module(name),test_read_lmem("test_read_lmem"),lmem("lmem"),tsu_desc_gen("tsu_desc_gen"){
        test_read_lmem.tsu_cfg_socket.bind(tsu_desc_gen.target_tsu_cfg);
        tsu_desc_gen.initiator_lmem.bind(lmem.socket_lmem);
        tsu_desc_gen.initiator_gmem.bind(lmem.socket_gmem);
    }
};

int sc_main(int argc, char* argv[]){
    Top top("top");
    sc_core::sc_start(1000,sc_core::SC_NS);
    return 0;
}---log.txt
---input.txt
---tsu_desc_gen.cpp
#include "tsu_desc_gen.h"
#include "sysc/kernel/sc_module.h"
#include "sysc/kernel/sc_time.h"
#include "tlm_core/tlm_2/tlm_generic_payload/tlm_gp.h"
#include <cstdint>
#include <vector>
#include <magic_enum.hpp>
using namespace sc_core;
using namespace std;

TsuDescGen::TsuDescGen(sc_core::sc_module_name name)
    : sc_module(name)
    , target_tsu_cfg("target_tsu_cfg")
    , initiator_gmem("initiator_gmem")
    , initiator_lmem("initiator_lmem")
    , req_lmem_delay(sc_core::SC_ZERO_TIME)
    , req_gmem_delay(sc_core::SC_ZERO_TIME)
{
    target_tsu_cfg.register_b_transport(this, &TsuDescGen::b_transport);
    SC_THREAD(thread_cmd);
}

void TsuDescGen::b_transport(tlm::tlm_generic_payload &trans, sc_time &delay) {
    tsu_cmd = *(TSU_Command*)trans.get_data_ptr();
    trans.set_response_status(tlm::TLM_OK_RESPONSE);
    std::cout << "TsuDescGen: Received command, func7=" << magic_enum::enum_name(static_cast<TSU_CMD_TYPE>(tsu_cmd.func7))
              << ", rs1val=0x" << std::hex << tsu_cmd.rs1val
              << ", rs2val=0x" << std::hex << tsu_cmd.rs2val << std::endl;
    recv_event.notify();
}

void TsuDescGen::thread_cmd(){
    while(true){
        wait(recv_event);
        // 根据tsu_cmd func7 
        if(static_cast<TSU_CMD_TYPE>(tsu_cmd.func7)==TSU_CMD_TYPE::TST_CFG){
            // 配置寄存器
            reg_manager.writeReg(tsu_cmd.rs1val,tsu_cmd.rs2val);
            // std::cout << "TsuDescGen thread_cmd: Received command, func7=" << magic_enum::enum_name(static_cast<TSU_CMD_TYPE>(tsu_cmd.func7))
            //           << ", rs1val=0x" << std::hex << tsu_cmd.rs1val
            //           << ", rs2val=0x" << std::hex << tsu_cmd.rs2val << std::endl;
        }else if(tsu_cmd.func7==TSU_CMD_TYPE::TST_DRV){
            // 配置地址
            tsu_addr.byte_base_lmem = tsu_cmd.rs1val;
            tsu_addr.byte_base_gmem = tsu_cmd.rs2val;
            // 获取配置
            cfg = reg_manager.getConfig();
            std::cout << "cfg.cfg_type: " << std::dec << cfg.cfg_type << std::endl;
            std::cout << "cfg.cfg_wd: " << std::dec << cfg.cfg_wd << std::endl;
            std::cout << "cfg.cfg_rem_dim0: " << std::dec << cfg.cfg_rem_dim0 << std::endl;
            std::cout << "cfg.cfg_size_dim0b: " << std::dec << cfg.cfg_size_dim0b << std::endl;
            std::cout << "cfg.cfg_size_dim1: " << std::dec << cfg.cfg_size_dim1 << std::endl;
            std::cout << "cfg.cfg_size_dim2: " << std::dec << cfg.cfg_size_dim2 << std::endl;
            std::cout << "cfg.cfg_stride_dim1_gmem: " << std::dec << cfg.cfg_stride_dim1_gmem << std::endl;
            std::cout << "cfg.cfg_stride_dim2_gmem: " << std::dec << cfg.cfg_stride_dim2_gmem << std::endl;
            std::cout << "cfg.cfg_stride_dim1_lmem: " << std::dec << cfg.cfg_stride_dim1_lmem << std::endl;
            std::cout << "cfg.cfg_stride_dim2_lmem: " << std::dec << cfg.cfg_stride_dim2_lmem << std::endl;
            // 触发开始
            thread_lmem_read();
            // std::cout << "TsuDescGen thread_cmd: Received command, func7=" << magic_enum::enum_name(static_cast<TSU_CMD_TYPE>(tsu_cmd.func7))
            //           << ", rs1val=0x" << std::hex << tsu_cmd.rs1val
            //           << ", rs2val=0x" << std::hex << tsu_cmd.rs2val << std::endl;
        }
    }
}

void TsuDescGen::thread_lmem_read(){
  
        // std::cout << "lmem_read_event" << std::endl;
        // Convert strides from 256-bit units to bytes
        uint32_t stride_dim0b_bytes = 32; // 256 bits = 32 bytes
        uint32_t stride_dim2_bytes_gmem = cfg.cfg_stride_dim2_gmem * 32;
        uint32_t stride_dim1_bytes_gmem = cfg.cfg_stride_dim1_gmem *32;
        uint32_t stride_dim2_bytes_lmem = cfg.cfg_stride_dim2_lmem * 32;
        uint32_t stride_dim1_bytes_lmem = cfg.cfg_stride_dim1_lmem *32;
        uint32_t read_length = 0;
        // uint32_t write_length = cfg.cfg_size_dim1 *32;

        for(uint32_t idx_dim2=0;idx_dim2<cfg.cfg_size_dim2;idx_dim2++){
            for(uint32_t idx_dim1=0;idx_dim1<cfg.cfg_size_dim1;idx_dim1++){
                for (uint32_t idx_dim0b=0; idx_dim0b<cfg.cfg_size_dim0b; idx_dim0b++) {
                    // func send addr to local memory
                    // 考虑 dim0_rem
                    if(idx_dim0b==(cfg.cfg_size_dim0b-1) && cfg.cfg_rem_dim0!=0){
                        // read length = cfg.rem_dim0
                        read_length = cfg.cfg_rem_dim0;
                    } else {
                        // read length = 32
                        read_length = 32;
                    }
                    uint64_t lmem_byte_addr = tsu_addr.byte_base_lmem + stride_dim2_bytes_lmem*idx_dim2 + stride_dim1_bytes_lmem*idx_dim1 + stride_dim0b_bytes*idx_dim0b;
                    // Setup and send TLM trans
                    trans.set_command(tlm::TLM_READ_COMMAND);
                    trans.set_address(lmem_byte_addr);
                    trans.set_data_ptr(data_buf);
                    trans.set_data_length(read_length);
                    trans.set_streaming_width(read_length);
                    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
                        
                    initiator_lmem->b_transport(trans, req_lmem_delay);
                        
                    if (trans.get_response_status() != tlm::TLM_OK_RESPONSE) {
                        SC_REPORT_ERROR("TsuDescGen", "TLM transaction failed");
                    }
                    // setup and send TLM trans
                    uint64_t gmem_byte_addr = tsu_addr.byte_base_gmem +stride_dim2_bytes_gmem*idx_dim2 +stride_dim0b_bytes*idx_dim0b+stride_dim1_bytes_gmem*idx_dim1;
                    trans.set_command(tlm::TLM_WRITE_COMMAND);
                    trans.set_address(gmem_byte_addr);
                    trans.set_data_ptr(data_buf);
                    trans.set_data_length(read_length);
                    trans.set_streaming_width(read_length);
                    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
                    initiator_gmem->b_transport(trans, req_gmem_delay);
                    if (trans.get_response_status() != tlm::TLM_OK_RESPONSE) {
                        SC_REPORT_ERROR("TsuDescGen", "TLM transaction failed");
                    }
                }

            }
        }
}---tsu_desc_gen.h
#pragma once
#include "sysc/kernel/sc_event.h"
#include "tlm_core/tlm_2/tlm_generic_payload/tlm_gp.h"
#include "tlm_utils/simple_initiator_socket.h"
#include "tlm_utils/simple_target_socket.h"
#include <cstdint>
#include <systemc>
#include "../tsu_cfg_reg/tsu_cfg_reg.h"
#include "npu_config.h"

struct TSU_Command{
    uint8_t func7;
    bool xs2;
    bool xs1;
    bool xd;
    std::conditional_t<NPUConfig::RV_XLEN == 32, uint32_t, uint64_t> rs2val;
    std::conditional_t<NPUConfig::RV_XLEN == 32, uint32_t, uint64_t> rs1val;
};

struct TsuADDR{
    std::conditional_t<NPUConfig::RV_XLEN == 32, uint32_t, uint64_t> byte_base_lmem;
    std::conditional_t<NPUConfig::RV_XLEN == 32, uint32_t, uint64_t> byte_base_gmem;
};

enum TSU_CMD_TYPE:uint8_t{
    TST_CFG = 0b00100000,
    TST_DRV = 0b00101000
};


class TsuDescGen : public sc_core::sc_module{
    public:
    tlm_utils::simple_target_socket<TsuDescGen> target_tsu_cfg;
    tlm_utils::simple_initiator_socket<TsuDescGen> initiator_gmem;
    tlm_utils::simple_initiator_socket<TsuDescGen> initiator_lmem;

    TsuDescGen(sc_core::sc_module_name name);
    void b_transport(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    TsuConfig cfg;
    private:
    sc_core::sc_time req_lmem_delay,req_gmem_delay;
    tlm::tlm_generic_payload trans;
    TSU_Command tsu_cmd;
    void thread_cmd();
    // void thread_desc_gen();
    void thread_lmem_read();
    sc_core::sc_event lmem_read_event;
    sc_core::sc_event recv_event;
    RegisterManager reg_manager;
    TsuADDR tsu_addr;
    uint8_t data_buf[32];
    std::vector<uint8_t> par2ser_buf;
};---mem.h
#pragma once
#include <tlm.h>
#include <tlm_utils/simple_initiator_socket.h>
#include <tlm_utils/simple_target_socket.h>
#include <systemc>
#include "../tsu_desc_gen/tsu_desc_gen.h"
// 为了测试，设置内存大小为16*16*16(以256bit为步长)
#define MEM_SIZE (16*256/8)        
#define dim0 MEM_SIZE
#define dim1 MEM_SIZE
#define dim2 MEM_SIZE

#define STRIDE_DIM0 (32)
#define STRIDE_DIM1 (16*32)
#define STRIDE_DIM2 (16*16*32)

class Memory : public sc_core::sc_module {
public:
    tlm_utils::simple_target_socket<Memory> socket_lmem;
    tlm_utils::simple_target_socket<Memory> socket_gmem;
    Memory(sc_core::sc_module_name name);
    void b_transport_lmem(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    void b_transport_gmem(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    private:
    std::vector<uint8_t> lmem;  // 使用 vector 替代固定大小数组
    std::vector<uint8_t> gmem;  // 使用 vector 替代固定大小数组
};---mem.cpp
#include "mem.h"

    Memory::Memory(sc_core::sc_module_name name):sc_module(name),socket_lmem("socket_lmem"),socket_gmem("socket_gmem"){
    socket_lmem.register_b_transport(this,&Memory::b_transport_lmem);
    socket_gmem.register_b_transport(this,&Memory::b_transport_gmem);
    lmem.resize(dim0*dim1*dim2);
    gmem.resize(dim0*dim1*dim2);
    // Initialize memory
    for(int i=0;i<dim2;i++){
        for(int j=0;j<dim1;j++){
            for(int k=0;k<dim0;k++){
                lmem[i*dim1*dim0 + j*dim0 + k] = i*dim1*dim0 + j*dim0 + k;
                gmem[i*dim1*dim0 + j*dim0 + k] = 0;
            }
        }
    }
}

void Memory::b_transport_lmem(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay){
    tlm::tlm_command cmd = trans.get_command();
    sc_dt::uint64 addr = trans.get_address();
    unsigned char* ptr = trans.get_data_ptr();
    unsigned int len = trans.get_data_length();
    uint32_t idx_dim2 = addr / STRIDE_DIM2;
    uint32_t idx_dim1 = (addr % STRIDE_DIM2) / STRIDE_DIM1;
    uint32_t idx_dim0 = (addr % STRIDE_DIM1) / STRIDE_DIM0;
    if(cmd == tlm::TLM_READ_COMMAND){
        std::cout << "Local Memory Read: addr=" << std::dec << addr << ", len=" << len 
              << ", idx_dim2=" << idx_dim2 << ", idx_dim1=" << idx_dim1 << ", idx_dim0=" << idx_dim0 << std::endl;
        for(unsigned int i = 0; i < len; i++) {
            ptr[i] = lmem[addr+i];
        }
    }
    trans.set_response_status(tlm::TLM_OK_RESPONSE);
    // delay += sc_core::SC_ZERO_TIME;
}

void Memory::b_transport_gmem(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay){
    tlm::tlm_command cmd = trans.get_command();
    sc_dt::uint64 addr = trans.get_address();
    unsigned char* ptr = trans.get_data_ptr();
    unsigned int len = trans.get_data_length();
    if(cmd == tlm::TLM_WRITE_COMMAND){
        std::cout << "Global Memory Write: addr=" << std::dec << addr << ", len=" << len << std::endl;
        for(unsigned int i = 0; i < len; i++) {
            gmem[addr+i] = ptr[i];
        }
    }
    trans.set_response_status(tlm::TLM_OK_RESPONSE);
}---tsu_cfg_reg.h
#pragma once
#include <systemc>
#include <map>
#include <stdexcept>


// 定义配置结构体
struct TsuConfig {
    uint32_t cfg_type;
    uint32_t cfg_wd;
    uint32_t cfg_rem_dim0;
    uint32_t cfg_size_dim0b;
    uint32_t cfg_size_dim1;
    uint32_t cfg_size_dim2;
    uint32_t cfg_stride_dim1_gmem;
    uint32_t cfg_stride_dim2_gmem;
    uint32_t cfg_stride_dim1_lmem;
    uint32_t cfg_stride_dim2_lmem;
};

// 字段定义结构体
struct FieldDef {
    uint32_t addr;
    uint32_t left;
    uint32_t right;
    const char* name;
};

// 字段枚举
enum FieldIndex {
    CFG_TYPE,
    CFG_WD,
    CFG_REM_DIM0,
    CFG_SIZE_DIM0B,
    CFG_SIZE_DIM1,
    CFG_SIZE_DIM2,
    CFG_STRIDE_DIM1_GMEM,
    CFG_STRIDE_DIM2_GMEM,
    CFG_STRIDE_DIM1_LMEM,
    CFG_STRIDE_DIM2_LMEM,
    FIELD_COUNT
};

// 字段定义表
const FieldDef FIELD_DEFS[FIELD_COUNT] = {
    {0,  1,  0, "cfg_type"},
    {0,  4,  2, "cfg_wd"},
    {1,  5,  0, "cfg_rem_dim0"},
    {1, 16,  6, "cfg_size_dim0b"},
    {2, 12,  0, "cfg_size_dim1"},
    {2, 25, 13, "cfg_size_dim2"},
    {3, 12,  0, "cfg_stride_dim1_gmem"},
    {4, 24,  0, "cfg_stride_dim2_gmem"},
    {5, 12,  0, "cfg_stride_dim1_lmem"},
    {6, 24,  0, "cfg_stride_dim2_lmem"}
};

class RegisterManager {
private:
    std::map<uint32_t, sc_dt::sc_uint<32>> registers;

    // 从寄存器中读取指定字段的值
    uint32_t readField(const FieldDef& field) {
        if (registers.find(field.addr) == registers.end()) {
            registers[field.addr] = 0;
        }
        return (registers[field.addr].range(field.left, field.right)).to_uint();
    }

public:
    RegisterManager() {
        // 初始化所有寄存器为0
        for (int i = 0; i < FIELD_COUNT; i++) {
            registers[FIELD_DEFS[i].addr] = 0;
        }
    }

    // 写入寄存器
    bool writeReg(uint32_t addr, uint32_t value) {
        try {
            // 找到所有与该地址相关的字段
            std::vector<const FieldDef*> addr_fields;
            for (int i = 0; i < FIELD_COUNT; i++) {
                if (FIELD_DEFS[i].addr == addr) {
                    addr_fields.push_back(&FIELD_DEFS[i]);
                }
            }
            
            if (addr_fields.empty()) {
                throw std::runtime_error("Invalid register address");
            }

            // 如果寄存器不存在，初始化为0
            if (registers.find(addr) == registers.end()) {
                registers[addr] = 0;
            }

            // 保存原始值
            registers[addr] = value;
            return true;

        } catch (const std::exception& e) {
            std::cerr << "Error writing register: " << e.what() << std::endl;
            return false;
        }
    }

    // 读取寄存器
    uint32_t readReg(uint32_t addr) {
        try {
            const FieldDef* field = nullptr;
            for (int i = 0; i < FIELD_COUNT; i++) {
                if (FIELD_DEFS[i].addr == addr) {
                    field = &FIELD_DEFS[i];
                    break;
                }
            }
            
            if (!field) {
                throw std::runtime_error("Invalid register address");
            }

            return readField(*field);

        } catch (const std::exception& e) {
            std::cerr << "Error reading register: " << e.what() << std::endl;
            return 0;
        }
    }

    // 获取完整配置
    TsuConfig getConfig() {
        TsuConfig cfg;
        
        cfg.cfg_type = readField(FIELD_DEFS[CFG_TYPE]);
        cfg.cfg_wd = readField(FIELD_DEFS[CFG_WD]);
        cfg.cfg_rem_dim0 = readField(FIELD_DEFS[CFG_REM_DIM0]);
        cfg.cfg_size_dim0b = readField(FIELD_DEFS[CFG_SIZE_DIM0B]);
        cfg.cfg_size_dim1 = readField(FIELD_DEFS[CFG_SIZE_DIM1]);
        cfg.cfg_size_dim2 = readField(FIELD_DEFS[CFG_SIZE_DIM2]);
        cfg.cfg_stride_dim1_gmem = readField(FIELD_DEFS[CFG_STRIDE_DIM1_GMEM]);
        cfg.cfg_stride_dim2_gmem = readField(FIELD_DEFS[CFG_STRIDE_DIM2_GMEM]);
        cfg.cfg_stride_dim1_lmem = readField(FIELD_DEFS[CFG_STRIDE_DIM1_LMEM]);
        cfg.cfg_stride_dim2_lmem = readField(FIELD_DEFS[CFG_STRIDE_DIM2_LMEM]);
        return cfg;
    }

    // 设置完整配置
    bool setConfig(const TsuConfig& cfg) {
        bool success = true;
        success &= writeReg(FIELD_DEFS[CFG_TYPE].addr, cfg.cfg_type);
        success &= writeReg(FIELD_DEFS[CFG_WD].addr, cfg.cfg_wd);
        success &= writeReg(FIELD_DEFS[CFG_REM_DIM0].addr, cfg.cfg_rem_dim0);
        success &= writeReg(FIELD_DEFS[CFG_SIZE_DIM0B].addr, cfg.cfg_size_dim0b);
        success &= writeReg(FIELD_DEFS[CFG_SIZE_DIM1].addr, cfg.cfg_size_dim1);
        success &= writeReg(FIELD_DEFS[CFG_SIZE_DIM2].addr, cfg.cfg_size_dim2);
        success &= writeReg(FIELD_DEFS[CFG_STRIDE_DIM1_GMEM].addr, cfg.cfg_stride_dim1_gmem);
        success &= writeReg(FIELD_DEFS[CFG_STRIDE_DIM2_GMEM].addr, cfg.cfg_stride_dim2_gmem);
        success &= writeReg(FIELD_DEFS[CFG_STRIDE_DIM1_LMEM].addr, cfg.cfg_stride_dim1_lmem);
        success &= writeReg(FIELD_DEFS[CFG_STRIDE_DIM2_LMEM].addr, cfg.cfg_stride_dim2_lmem);
        return success;
    }

    // 重置所有寄存器
    void reset() {
        for (auto& reg : registers) {
            reg.second = 0;
        }
    }

    // 打印所有寄存器的值
    void printAllRegs() {
        std::cout << "Register Values:" << std::endl;
        for (int i = 0; i < FIELD_COUNT; i++) {
            const auto& field = FIELD_DEFS[i];
            std::cout << field.name << " [" << field.left << ":" << field.right 
                     << "] = 0x" << std::hex << readField(field) 
                     << std::dec << std::endl;
        }
    }

    // 打印当前配置
    void printConfig() {
        TsuConfig cfg = getConfig();
        std::cout << "Current Configuration:" << std::endl;
        std::cout << "cfg_type: " << cfg.cfg_type << std::endl;
        std::cout << "cfg_wd: " << cfg.cfg_wd << std::endl;
        std::cout << "cfg_rem_dim0: " << cfg.cfg_rem_dim0 << std::endl;
        std::cout << "cfg_size_dim0b: " << cfg.cfg_size_dim0b << std::endl;
        std::cout << "cfg_size_dim1: " << cfg.cfg_size_dim1 << std::endl;
        std::cout << "cfg_size_dim2: " << cfg.cfg_size_dim2 << std::endl;
        std::cout << "cfg_stride_dim1_gmem: " << cfg.cfg_stride_dim1_gmem << std::endl;
        std::cout << "cfg_stride_dim2_gmem: " << cfg.cfg_stride_dim2_gmem << std::endl;
        std::cout << "cfg_stride_dim1_lmem: " << cfg.cfg_stride_dim1_lmem << std::endl;
        std::cout << "cfg_stride_dim2_lmem: " << cfg.cfg_stride_dim2_lmem << std::endl;
    }
};---tsu_cfg_reg.cpp
#include "tsu_cfg_reg.h"



int sc_main(int argc, char* argv[]) {
    RegisterManager regMgr;

    // 单个寄存器操作
    regMgr.writeReg(0, 0x1023012);
    
    // 使用结构体设置所有配置
    TsuConfig cfg;
    cfg.cfg_type = 1;
    cfg.cfg_wd = 2;
    cfg.cfg_rem_dim0 = 32;
    cfg.cfg_size_dim0b = 1;
    cfg.cfg_size_dim1 = 2;
    cfg.cfg_size_dim2 = 3;
    cfg.cfg_stride_dim1_gmem = 4;
    cfg.cfg_stride_dim2_gmem = 5;
    cfg.cfg_stride_dim1_lmem = 6;
    cfg.cfg_stride_dim2_lmem = 7;
    regMgr.setConfig(cfg);
    
    // 打印配置
    regMgr.printConfig();
    
    // 打印所有寄存器值
    regMgr.printAllRegs();
    
    // 重置
    regMgr.reset();
    
    return 0;
}