#pragma once
#include <cstdint>
#include <systemc>
#include "../tsu_cfg_reg/tsu_cfg_reg.h"
#include "npu_config.h"
#include "sysc/kernel/sc_event.h"
#include "tlm_core/tlm_2/tlm_generic_payload/tlm_gp.h"
#include "tlm_utils/simple_initiator_socket.h"
#include "tlm_utils/simple_target_socket.h"

struct TSU_Command
{
    uint8_t func7;
    bool xs2;
    bool xs1;
    bool xd;
    std::conditional_t<static_cast<int>(NPUConfig::RV_XLEN) == 32, uint32_t, uint64_t> rs2val;
    std::conditional_t<static_cast<int>(NPUConfig::RV_XLEN) == 32, uint32_t, uint64_t> rs1val;
};

struct TsuADDR
{
    std::conditional_t<static_cast<int>(NPUConfig::RV_XLEN) == 32, uint32_t, uint64_t> byte_base_lmem;
    std::conditional_t<static_cast<int>(NPUConfig::RV_XLEN) == 32, uint32_t, uint64_t> byte_base_gmem;
};

enum TSU_CMD_TYPE : uint8_t
{
    TST_CFG = 0b00100000,
    TST_DRV = 0b00101000
};

class TsuDescGen : public sc_core::sc_module
{
  public:
    tlm_utils::simple_target_socket<TsuDescGen> target_tsu_cfg;
    tlm_utils::simple_initiator_socket<TsuDescGen> initiator_gmem;
    tlm_utils::simple_initiator_socket<TsuDescGen> initiator_lmem;

    TsuDescGen(sc_core::sc_module_name name);
    void b_transport(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    TsuConfig cfg;

  private:
    sc_core::sc_time req_lmem_delay, req_gmem_delay;
    tlm::tlm_generic_payload trans;
    TSU_Command tsu_cmd;
    void thread_cmd();
    // void thread_desc_gen();
    void thread_lmem_read();
    sc_core::sc_event lmem_read_event;
    sc_core::sc_event recv_event;
    RegisterManager reg_manager;
    TsuADDR tsu_addr;
    uint8_t data_buf[32];
};