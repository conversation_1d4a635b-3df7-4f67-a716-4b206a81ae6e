#include "tsu_desc_gen.h"
#include <cstdint>
#include <magic_enum.hpp>
#include <vector>
#include "sysc/kernel/sc_module.h"
#include "sysc/kernel/sc_time.h"
#include "tlm_core/tlm_2/tlm_generic_payload/tlm_gp.h"
using namespace sc_core;
using namespace std;

TsuDescGen::TsuDescGen(sc_core::sc_module_name name)
    : sc_module(name),
      target_tsu_cfg("target_tsu_cfg"),
      initiator_gmem("initiator_gmem"),
      initiator_lmem("initiator_lmem"),
      req_lmem_delay(sc_core::SC_ZERO_TIME),
      req_gmem_delay(sc_core::SC_ZERO_TIME)
{
    target_tsu_cfg.register_b_transport(this, &TsuDescGen::b_transport);
    SC_THREAD(thread_cmd);
}

void TsuDescGen::b_transport(tlm::tlm_generic_payload& trans, sc_time& delay)
{
    tsu_cmd = *(TSU_Command*)trans.get_data_ptr();
    trans.set_response_status(tlm::TLM_OK_RESPONSE);
    std::cout << "TsuDescGen: Received command, func7="
              << magic_enum::enum_name(static_cast<TSU_CMD_TYPE>(tsu_cmd.func7)) << ", rs1val=0x"
              << std::hex << tsu_cmd.rs1val << ", rs2val=0x" << std::hex << tsu_cmd.rs2val
              << std::endl;
    recv_event.notify();
}

void TsuDescGen::thread_cmd()
{
    while (true)
    {
        wait(recv_event);
        // 根据tsu_cmd func7
        if (static_cast<TSU_CMD_TYPE>(tsu_cmd.func7) == TSU_CMD_TYPE::TST_CFG)
        {
            // 配置寄存器
            reg_manager.writeReg(tsu_cmd.rs1val, tsu_cmd.rs2val);
            // std::cout << "TsuDescGen thread_cmd: Received command, func7=" <<
            // magic_enum::enum_name(static_cast<TSU_CMD_TYPE>(tsu_cmd.func7))
            //           << ", rs1val=0x" << std::hex << tsu_cmd.rs1val
            //           << ", rs2val=0x" << std::hex << tsu_cmd.rs2val << std::endl;
        }
        else if (tsu_cmd.func7 == TSU_CMD_TYPE::TST_DRV)
        {
            // 配置地址
            tsu_addr.byte_base_lmem = tsu_cmd.rs1val;
            tsu_addr.byte_base_gmem = tsu_cmd.rs2val;
            // 获取配置
            cfg = reg_manager.getConfig();
            std::cout << "cfg.cfg_type: " << std::dec << cfg.cfg_type << std::endl;
            std::cout << "cfg.cfg_wd: " << std::dec << cfg.cfg_wd << std::endl;
            std::cout << "cfg.cfg_rem_dim0: " << std::dec << cfg.cfg_rem_dim0 << std::endl;
            std::cout << "cfg.cfg_size_dim0b: " << std::dec << cfg.cfg_size_dim0b << std::endl;
            std::cout << "cfg.cfg_size_dim1: " << std::dec << cfg.cfg_size_dim1 << std::endl;
            std::cout << "cfg.cfg_size_dim2: " << std::dec << cfg.cfg_size_dim2 << std::endl;
            std::cout << "cfg.cfg_stride_dim1_gmem: " << std::dec << cfg.cfg_stride_dim1_gmem
                      << std::endl;
            std::cout << "cfg.cfg_stride_dim2_gmem: " << std::dec << cfg.cfg_stride_dim2_gmem
                      << std::endl;
            std::cout << "cfg.cfg_stride_dim1_lmem: " << std::dec << cfg.cfg_stride_dim1_lmem
                      << std::endl;
            std::cout << "cfg.cfg_stride_dim2_lmem: " << std::dec << cfg.cfg_stride_dim2_lmem
                      << std::endl;
            // 触发开始
            thread_lmem_read();
            // std::cout << "TsuDescGen thread_cmd: Received command, func7=" <<
            // magic_enum::enum_name(static_cast<TSU_CMD_TYPE>(tsu_cmd.func7))
            //           << ", rs1val=0x" << std::hex << tsu_cmd.rs1val
            //           << ", rs2val=0x" << std::hex << tsu_cmd.rs2val << std::endl;
        }
    }
}

void TsuDescGen::thread_lmem_read()
{
    // std::cout << "lmem_read_event" << std::endl;
    // Convert strides from 256-bit units to bytes
    uint32_t stride_dim0b_bytes = 32;  // 256 bits = 32 bytes
    uint32_t stride_dim2_bytes_gmem = cfg.cfg_stride_dim2_gmem * 32;
    uint32_t stride_dim1_bytes_gmem = cfg.cfg_stride_dim1_gmem * 32;
    uint32_t stride_dim2_bytes_lmem = cfg.cfg_stride_dim2_lmem * 32;
    uint32_t stride_dim1_bytes_lmem = cfg.cfg_stride_dim1_lmem * 32;
    uint32_t read_length = 0;
    // uint32_t write_length = cfg.cfg_size_dim1 *32;

    for (uint32_t idx_dim2 = 0; idx_dim2 < cfg.cfg_size_dim2; idx_dim2++)
    {
        for (uint32_t idx_dim1 = 0; idx_dim1 < cfg.cfg_size_dim1; idx_dim1++)
        {
            for (uint32_t idx_dim0b = 0; idx_dim0b < cfg.cfg_size_dim0b; idx_dim0b++)
            {
                // func send addr to local memory
                // 考虑 dim0_rem
                if (idx_dim0b == (cfg.cfg_size_dim0b - 1) && cfg.cfg_rem_dim0 != 0)
                {
                    // read length = cfg.rem_dim0
                    read_length = cfg.cfg_rem_dim0;
                }
                else
                {
                    // read length = 32
                    read_length = 32;
                }
                uint64_t lmem_byte_addr =
                    tsu_addr.byte_base_lmem + stride_dim2_bytes_lmem * idx_dim2 +
                    stride_dim1_bytes_lmem * idx_dim1 + stride_dim0b_bytes * idx_dim0b;
                // Setup and send TLM trans
                trans.set_command(tlm::TLM_READ_COMMAND);
                trans.set_address(lmem_byte_addr);
                trans.set_data_ptr(data_buf);
                trans.set_data_length(read_length);
                trans.set_streaming_width(read_length);
                trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

                initiator_lmem->b_transport(trans, req_lmem_delay);

                if (trans.get_response_status() != tlm::TLM_OK_RESPONSE)
                {
                    SC_REPORT_ERROR("TsuDescGen", "TLM transaction failed");
                }
                // setup and send TLM trans
                uint64_t gmem_byte_addr =
                    tsu_addr.byte_base_gmem + stride_dim2_bytes_gmem * idx_dim2 +
                    stride_dim0b_bytes * idx_dim0b + stride_dim1_bytes_gmem * idx_dim1;
                trans.set_command(tlm::TLM_WRITE_COMMAND);
                trans.set_address(gmem_byte_addr);
                trans.set_data_ptr(data_buf);
                trans.set_data_length(read_length);
                trans.set_streaming_width(read_length);
                trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
                initiator_gmem->b_transport(trans, req_gmem_delay);
                if (trans.get_response_status() != tlm::TLM_OK_RESPONSE)
                {
                    SC_REPORT_ERROR("TsuDescGen", "TLM transaction failed");
                }
            }
        }
    }
}