#include "mem.h"

Memory::Memory(sc_core::sc_module_name name)
    : sc_module(name), socket_lmem("socket_lmem"), socket_gmem("socket_gmem")
{
    socket_lmem.register_b_transport(this, &Memory::b_transport_lmem);
    socket_gmem.register_b_transport(this, &Memory::b_transport_gmem);
    lmem.resize(dim0 * dim1 * dim2);
    gmem.resize(dim0 * dim1 * dim2);
    // Initialize memory
    for (int i = 0; i < dim2; i++)
    {
        for (int j = 0; j < dim1; j++)
        {
            for (int k = 0; k < dim0; k++)
            {
                lmem[i * dim1 * dim0 + j * dim0 + k] = i * dim1 * dim0 + j * dim0 + k;
                gmem[i * dim1 * dim0 + j * dim0 + k] = 0;
            }
        }
    }
}

void Memory::b_transport_lmem(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
{
    tlm::tlm_command cmd = trans.get_command();
    sc_dt::uint64 addr = trans.get_address();
    unsigned char* ptr = trans.get_data_ptr();
    unsigned int len = trans.get_data_length();
    uint32_t idx_dim2 = addr / STRIDE_DIM2;
    uint32_t idx_dim1 = (addr % STRIDE_DIM2) / STRIDE_DIM1;
    uint32_t idx_dim0 = (addr % STRIDE_DIM1) / STRIDE_DIM0;
    if (cmd == tlm::TLM_READ_COMMAND)
    {
        std::cout << "Local Memory Read: addr=" << std::dec << addr << ", len=" << len
                  << ", idx_dim2=" << idx_dim2 << ", idx_dim1=" << idx_dim1
                  << ", idx_dim0=" << idx_dim0 << std::endl;
        for (unsigned int i = 0; i < len; i++)
        {
            ptr[i] = lmem[addr + i];
        }
    }
    trans.set_response_status(tlm::TLM_OK_RESPONSE);
    // delay += sc_core::SC_ZERO_TIME;
}

void Memory::b_transport_gmem(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
{
    tlm::tlm_command cmd = trans.get_command();
    sc_dt::uint64 addr = trans.get_address();
    unsigned char* ptr = trans.get_data_ptr();
    unsigned int len = trans.get_data_length();
    if (cmd == tlm::TLM_WRITE_COMMAND)
    {
        std::cout << "Global Memory Write: addr=" << std::dec << addr << ", len=" << len
                  << std::endl;
        for (unsigned int i = 0; i < len; i++)
        {
            gmem[addr + i] = ptr[i];
        }
    }
    trans.set_response_status(tlm::TLM_OK_RESPONSE);
}