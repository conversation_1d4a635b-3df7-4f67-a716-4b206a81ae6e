#pragma once
#include <tlm.h>
#include <tlm_utils/simple_initiator_socket.h>
#include <tlm_utils/simple_target_socket.h>
#include <systemc>
#include "../tsu_desc_gen/tsu_desc_gen.h"
// 为了测试，设置内存大小为16*16*16(以256bit为步长)
#define MEM_SIZE (16 * 256 / 8)
#define dim0 MEM_SIZE
#define dim1 MEM_SIZE
#define dim2 MEM_SIZE

#define STRIDE_DIM0 (32)
#define STRIDE_DIM1 (16 * 32)
#define STRIDE_DIM2 (16 * 16 * 32)

class Memory : public sc_core::sc_module
{
  public:
    tlm_utils::simple_target_socket<Memory> socket_lmem;
    tlm_utils::simple_target_socket<Memory> socket_gmem;
    Memory(sc_core::sc_module_name name);
    void b_transport_lmem(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    void b_transport_gmem(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);

  private:
    std::vector<uint8_t> lmem;  // 使用 vector 替代固定大小数组
    std::vector<uint8_t> gmem;  // 使用 vector 替代固定大小数组
};