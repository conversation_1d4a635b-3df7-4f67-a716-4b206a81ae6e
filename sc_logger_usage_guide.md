# SystemC Logger 使用指南

## 1. 功能概述

SystemC Logger 是一个基于 spdlog 的高性能日志库，专为 SystemC 仿真环境设计，提供：

- ✅ **统一的日志接口**：完全基于 spdlog，移除了 SystemC 原生报告系统的复杂性
- ✅ **自动上下文捕获**：模块名、进程名、时间戳、文件位置
- ✅ **完美对齐**：所有日志字段都经过格式化对齐，便于阅读
- ✅ **自定义类型支持**：通过 fmt::formatter 特化支持任意结构体
- ✅ **C++11 兼容**：完全兼容 C++11 标准

## 2. 基本使用

### 2.1 初始化

```cpp
#include "sc_logger.h"

int sc_main(int argc, char* argv[]) {
    // 初始化日志系统
    sc_logger::initialize(
        spdlog::level::info,     // 控制台输出级别
        "simulation.log",        // 日志文件名
        spdlog::level::debug     // 文件输出级别
    );
    
    // 您的 SystemC 代码...
    return 0;
}
```

### 2.2 日志宏使用

```cpp
SC_LOG_TRACE("详细跟踪信息: {}", value);
SC_LOG_DEBUG("调试信息: variable = {}", var);
SC_LOG_INFO("一般信息: 操作完成");
SC_LOG_WARN("警告: 发现异常情况 {}", condition);
SC_LOG_ERROR("错误: 操作失败，错误码 {}", error_code);
SC_LOG_CRITICAL("严重错误: 系统即将停止");
```

### 2.3 日志输出格式

```
[模块名.进程名              ][级别    ][文件:行号][时间    ] 消息
[producer.producer.produce  ][debug   ][test.cpp:17  ][    10 ns] TX Details: BusTx(W Addr:0x1000, Data:0x0)
[consumer.consumer.consume  ][info    ][test.cpp:49  ][    20 ns] Consumed transaction: BusTx(R Addr:0x1000, Data:0x0)
```

## 3. 层级关系说明

日志中的模块层级遵循以下模式：`实例名.模块类名.进程名`

```cpp
SC_MODULE(Producer) {          // 模块类名
    void produce() {           // 进程名
        SC_LOG_INFO("消息");
    }
    SC_CTOR(Producer) {
        SC_THREAD(produce);
    }
};

int sc_main(int argc, char* argv[]) {
    Producer prod("producer");  // 实例名
    // 输出: [producer.Producer.produce][info]...
}
```

特殊情况：
- `sc_main.elaboration`：在 sc_main 函数的构建阶段
- `sc_main.simulation`：在仿真运行阶段（非进程上下文）

## 4. 自定义结构体日志支持

### 4.1 步骤概述

为您的自定义结构体添加日志支持需要三个步骤：

1. **定义结构体**（包含必要的构造函数和操作符）
2. **实现 fmt::formatter 特化**
3. **在代码中使用**

### 4.2 完整示例

```cpp
// 步骤1：定义结构体
struct MemoryAccess {
    uint64_t address;
    uint32_t size;
    std::string operation;
    bool valid;
    
    // 必需的构造函数
    MemoryAccess() : address(0), size(0), operation("IDLE"), valid(false) {}
    MemoryAccess(uint64_t addr, uint32_t sz, const std::string& op, bool v) 
        : address(addr), size(sz), operation(op), valid(v) {}
    
    // 如果用于 SystemC 信号，还需要比较操作符
    bool operator==(const MemoryAccess& other) const {
        return address == other.address && size == other.size && 
               operation == other.operation && valid == other.valid;
    }
    bool operator!=(const MemoryAccess& other) const {
        return !(*this == other);
    }
};

// 步骤2：实现 fmt::formatter 特化
template <>
struct fmt::formatter<MemoryAccess> {
    constexpr auto parse(fmt::format_parse_context& ctx) -> decltype(ctx.begin()) { 
        return ctx.begin(); 
    }
    
    template <typename FormatContext>
    auto format(const MemoryAccess& m, FormatContext& ctx) const -> decltype(ctx.out()) {
        return fmt::format_to(ctx.out(),
            "MemAccess({} Addr:{:#x}, Size:{}, Valid:{})",
            m.operation, m.address, m.size, m.valid ? "Y" : "N");
    }
};

// 步骤3：使用
void example_usage() {
    MemoryAccess mem(0x1000, 64, "READ", true);
    SC_LOG_INFO("Memory operation: {}", mem);
    // 输出: Memory operation: MemAccess(READ Addr:0x1000, Size:64, Valid:Y)
}
```

### 4.3 多个结构体的管理

建议的项目结构：

```
include/
├── utils/
│   ├── sc_logger.h           # 核心日志库
│   └── custom_formatters.h   # 您的自定义格式化器
└── your_structs/
    ├── bus_types.h           # 总线相关结构体
    ├── memory_types.h        # 内存相关结构体
    └── cpu_types.h           # CPU相关结构体
```

`custom_formatters.h` 示例：
```cpp
#ifndef CUSTOM_FORMATTERS_H
#define CUSTOM_FORMATTERS_H

#include "sc_logger.h"
#include "your_structs/bus_types.h"
#include "your_structs/memory_types.h"
#include "your_structs/cpu_types.h"

// 为每个结构体实现 fmt::formatter 特化
template <> struct fmt::formatter<BusTransaction> { /* ... */ };
template <> struct fmt::formatter<MemoryAccess> { /* ... */ };
template <> struct fmt::formatter<CPUInstruction> { /* ... */ };

#endif
```

### 4.4 格式化器模板

```cpp
template <>
struct fmt::formatter<YourStruct> {
    constexpr auto parse(fmt::format_parse_context& ctx) -> decltype(ctx.begin()) { 
        return ctx.begin(); 
    }
    
    template <typename FormatContext>
    auto format(const YourStruct& obj, FormatContext& ctx) const -> decltype(ctx.out()) {
        return fmt::format_to(ctx.out(),
            "YourStruct(field1:{}, field2:{:#x}, field3:{})",
            obj.field1, obj.field2, obj.field3 ? "true" : "false");
    }
};
```

## 5. 高级用法

### 5.1 条件日志

```cpp
if (debug_enabled) {
    SC_LOG_DEBUG("详细调试信息: {}", complex_computation());
}
```

### 5.2 性能敏感场景

由于 spdlog 的高性能设计和条件检查，性能影响极小：

```cpp
// 如果 debug 级别未启用，format 不会执行
SC_LOG_DEBUG("复杂计算结果: {}", expensive_function());
```

### 5.3 多线程安全

spdlog 内部处理线程安全，无需额外同步。

## 6. 常见问题

### Q: 为什么移除了 SystemC 原生报告系统？
A: 原生系统复杂且功能有限，spdlog 提供更好的性能、格式化和功能。

### Q: 如何调整日志级别？
A: 在 `sc_logger::initialize()` 中设置不同的控制台和文件级别。

### Q: 自定义结构体在 SystemC 信号中使用时出错？
A: 确保实现了 `operator==` 和 `operator!=`，这是 SystemC 的要求。

### Q: 时间戳格式可以自定义吗？
A: 当前使用 SystemC 的默认格式，如需自定义可修改 `systemc_time_flag` 类。

## 7. 最佳实践

1. **统一初始化**：在 `sc_main` 开始时调用 `sc_logger::initialize()`
2. **适当的日志级别**：开发时使用 `debug`，生产时使用 `info` 或更高
3. **有意义的消息**：包含足够的上下文信息
4. **结构化格式**：对复杂数据使用自定义格式化器
5. **性能考虑**：对于高频日志，考虑使用条件判断

## 8. 示例输出

```
[sc_main.elaboration         ][info    ][test.cpp:64  ][       0 s] Simulation starting...
[producer.producer.produce   ][debug   ][test.cpp:17  ][       0 s]   TX Details: BusTx(W Addr:0x1000, Data:0x0)
[producer.producer.produce   ][debug   ][test.cpp:17  ][     10 ns]   TX Details: BusTx(W Addr:0x1004, Data:0x2710)
[consumer.consumer.consume   ][info    ][test.cpp:49  ][     10 ns] Consumed transaction: BusTx(R Addr:0x1000, Data:0x0)
[producer.producer.produce   ][warning ][test.cpp:24  ][     40 ns] Address space wrapping around.
[sc_main.elaboration         ][critical][test.cpp:82  ][    100 ns] Simulation finished.
```

这种格式提供了：
- **清晰的层级关系**：模块和进程的完整路径
- **对齐的字段**：便于快速扫描和分析
- **精确的时间信息**：SystemC 仿真时间
- **源码定位**：文件名和行号
- **结构化数据**：自定义类型的友好显示 