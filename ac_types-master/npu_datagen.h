#pragma once

#include <cstdint>
#include <vector>
#include "../include/utils/utils.h"
#include "npu_datatypes.h"

// 数据在内存中按照C->H->W的顺序排布，C维度根据pad_en进行256bit对齐，否则不对齐
// pad_en:
// 是否进行padding，如果为true，则C维度进行256bit对齐，否则按照C维度实际大小存储,其中如果为INT4类型，则每个字节存储两个INT4值，且当最后一个字节只存储一个INT4值时，需要补齐一个字节
std::vector<uint8_t> generate_3d_matrix_data(int H,
                                             int W,
                                             int C,
                                             uint8_t prec_in,
                                             bool pad_en,
                                             uint32_t& padded_C);

// 生成4D权重矩阵 [kh][kw][ic][oc] 布局
// 数据在内存中按照oc->ic->kh->kw的顺序排布
std::vector<uint8_t> generate_weight_data(int out_channels,
                                          int in_channels,
                                          int kernel_h,
                                          int kernel_w,
                                          uint8_t prec_in);

// 将4D权重矩阵转换为2D矩阵 [kh*kw*ic][oc]
std::vector<uint8_t> weight_img2col(const std::vector<uint8_t>& weight_data,
                                    int out_channels,
                                    int in_channels,
                                    int kernel_h,
                                    int kernel_w,
                                    uint8_t prec_in);
