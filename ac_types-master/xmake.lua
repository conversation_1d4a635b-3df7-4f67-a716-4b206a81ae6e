


-- target("test_int")
--     set_kind("binary")
--     add_files("test/test_int.cpp")
--     add_includedirs("include", {public = true})
--     add_deps("ac_types")

-- target("test_float")
--     set_kind("binary")
--     add_files("test/test_float.cpp")
--     add_includedirs("include", {public = true})
--     add_deps("ac_types")


target("test_npu_datatypes")
    set_kind("binary")
    add_includedirs(".", {public = true})
    add_files("test/npu_datatype_test.cpp")
    add_includedirs("include", {public = true})
    add_deps("ac_types")
    add_packages("gtest")


target("npu_datagen")
    set_kind("static")
    add_files("npu_datagen.cpp")
    add_includedirs(".", {public = true})
    add_includedirs("include", {public = true})
    add_deps("ac_types")

target("test_npu_macunit")
    set_kind("binary")
    add_includedirs(".", {public = true})
    add_files("test/test_npu_macunit.cpp")
    add_files("*.cpp")
    add_includedirs("include", {public = true})
    add_deps("ac_types")
