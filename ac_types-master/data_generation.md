# 实现文档：generate_3d_matrix_data 函数 (修订版 v3)

## 1. 函数功能

生成指定形状 [H, W, C] 和精度 `prec_in` 的三维矩阵数据，并以 `std::vector<uint8_t>` 的形式输出。数据存储顺序为 C, W, H，并在 C 维度进行 256-bit 对齐填充。 特别针对 INT4 数据类型进行优化，将两个 INT4 数据打包到一个 uint8_t 中存储。

## 2. 函数原型

```cpp
#include <vector>
#include <cstdint>

std::vector<uint8_t> generate_3d_matrix_data(int H, int W, int C, uint8_t prec_in);
```

## 3. 包含头文件

*   `<vector>`: 用于 `std::vector`。
*   `<cstdint>`: 用于 `uint8_t`, `uint16_t`, `uint32_t` 等整型类型。
*   `<cmath>`: 用于 `ceil` 函数。
*   `<stdexcept>`: 用于异常处理，例如 `std::invalid_argument`。
*   `"ac_datatypes.h"`:  包含 `dtype` 命名空间，`width_code_to_bits` 函数，`npu_data_variant_t`, `generate_random_data`, `to_storage_format` 等定义 (假设头文件名为 "ac_datatypes.h"，请根据实际情况修改)。
*   `<cstring>`: 用于 `std::memcpy`

## 4. 函数实现步骤

```cpp
#include <vector>
#include <cstdint>
#include <cmath>
#include <stdexcept>
#include <cstring> // For memcpy
#include "ac_datatypes.h" // 假设包含 NPU 数据类型定义的头文件

std::vector<uint8_t> generate_3d_matrix_data(int H, int W, int C, uint8_t prec_in) {
    // 1. 获取 prec_in 的位宽
    uint32_t bit_width = width_code_to_bits(static_cast<dtype::WidthCode>(prec_in & 0x07));
    if (bit_width == 0) {
        throw std::invalid_argument("Invalid prec_in value.");
    }
    float element_bytes_float = static_cast<float>(bit_width) / 8.0; // 每个元素占用的字节数 (浮点类型)
    uint32_t element_bytes = static_cast<uint32_t>(std::ceil(element_bytes_float)); // 向上取整字节数，用于非 INT4 类型

    // 2. 计算 size_dim0a, size_dim0b, rem_dim0
    uint32_t size_dim0a = 256 / bit_width;
    uint32_t size_dim0b = static_cast<uint32_t>(std::ceil(static_cast<double>(C) / size_dim0a));
    uint32_t rem_dim0 = C % size_dim0a;
    uint32_t padded_C = size_dim0b * size_dim0a; // 填充后的 C 维度

    // 3. 计算缓冲区大小
    uint32_t buffer_size_bytes = static_cast<uint32_t>(std::ceil(padded_C * W * H * element_bytes_float)); // 使用浮点数计算后向上取整
    std::vector<uint8_t> buffer(buffer_size_bytes, 0); // 初始化为 0

    // 4. 生成随机数据并填充缓冲区 (CWH 顺序)
    uint32_t buffer_index = 0;

    for (int h = 0; h < H; ++h) {
        for (int w = 0; w < W; ++w) {
            for (int c = 0; c < C; ++c) {
                if (bit_width == 4) { // INT4 特殊处理
                    npu_data_variant_t random_data_low = generate_random_data(prec_in); // 生成第一个 INT4 数据 (低 4 位)
                    npu_data_variant_t random_data_high = generate_random_data(prec_in); // 生成第二个 INT4 数据 (高 4 位)

                    uint8_t data_uint8_low = to_storage_format<uint8_t>(prec_in, random_data_low);
                    uint8_t data_uint8_high = to_storage_format<uint8_t>(prec_in, random_data_high);

                    uint8_t int4_val_low = data_uint8_low & 0x0F;   // 获取低 4 位
                    uint8_t int4_val_high = (data_uint8_high & 0x0F) << 4; // 获取高 4 位并左移 4 位

                    buffer[buffer_index++] = int4_val_low | int4_val_high; // 合并为一个字节并写入
                 }
                 else if (bit_width <= 8) {
                    npu_data_variant_t random_data = generate_random_data(prec_in);
                    uint8_t data_uint8 = to_storage_format<uint8_t>(prec_in, random_data);
                    buffer[buffer_index++] = data_uint8;
                } else if (bit_width <= 16) {
                    npu_data_variant_t random_data = generate_random_data(prec_in);
                    uint16_t data_uint16 = to_storage_format<uint16_t>(prec_in, random_data);
                    std::memcpy(&buffer[buffer_index], &data_uint16, sizeof(uint16_t));
                    buffer_index += sizeof(uint16_t);
                } else if (bit_width <= 32) {
                    npu_data_variant_t random_data = generate_random_data(prec_in);
                    uint32_t data_uint32 = to_storage_format<uint32_t>(prec_in, random_data);
                    std::memcpy(&buffer[buffer_index], &data_uint32, sizeof(uint32_t));
                    buffer_index += sizeof(uint32_t);
                }
            }
            // 5. C 维度填充 (如果需要)
            if (rem_dim0 != 0) {
                buffer_index += static_cast<uint32_t>((size_dim0a - rem_dim0) * element_bytes); // 填充 0 已经在缓冲区初始化时完成，这里只需要移动 buffer_index
            }
        }
    }

    return buffer;
}
```

## 5. 使用示例

```cpp
#include <iostream>
#include <vector>
#include "ac_datatypes.h" // 假设包含 NPU 数据类型定义的头文件

int main() {
    int H = 3;
    int W = 4;
    int C = 127;
    uint8_t prec_in = dtype::INT4;

    std::vector<uint8_t> data_buffer = generate_3d_matrix_data(H, W, C, prec_in);

    std::cout << "Generated data buffer size: " << data_buffer.size() << " bytes" << std::endl;
    // 可以进一步处理 data_buffer，例如写入文件

    return 0;
}
```

## 6. 注意事项

*   **头文件依赖**:  请确保 `"ac_datatypes.h"` 头文件存在，并且包含了 NPU 数据类型和相关函数的定义。
*   **错误处理**:  代码中包含了基本的参数校验 (`prec_in` 是否有效)。您可以根据需要添加更完善的错误处理机制。
*   **数据类型支持**:  目前代码框架支持 `prec_in` 指定的各种 NPU 数据类型，**并特别优化了 INT4 类型的处理，将两个 INT4 值打包到一个字节中。**
*   **性能优化**:  对于性能敏感的应用，可以考虑使用更高效的内存分配方式，或者针对特定的数据类型进行优化。
*   **位宽处理**:  代码中使用了 `uint8_t` 作为通用的存储单位。`to_storage_format` 函数负责处理不同位宽到存储格式的转换。**对于 INT4 类型，实现了生成两个 4-bit 值并打包成一个 8-bit 字节的逻辑。**
*   **缓冲区大小计算**:  缓冲区大小计算已更新，使用浮点数计算 `element_bytes`，并向上取整最终的缓冲区大小，确保能容纳填充后的数据。
*   **数据拷贝**: 使用 `std::memcpy` 替代逐字节写入，提高效率 (针对位宽大于 8 bit 的数据类型)。
*   **C 维度填充**:  填充部分的代码进行了简化，利用缓冲区初始化为 0 的特性，直接移动 `buffer_index` 来实现填充效果。
*   **INT4 打包**:  **INT4 数据的打包逻辑已更新。 现在每次循环会生成两个随机 INT4 值，并将它们打包到一个 `uint8_t` 中。**

---
