# npu_datatypes.h 函数文档

## generate_random_data

### 功能描述

通用函数，根据指定的精度代码生成随机数据。可以生成 NPU 支持的各种数据类型，例如 INT4, INT8, INT16, INT32, FP16, FP32, BF16, BBF16。可以指定生成随机数的最小值和最大值，如果未指定，则使用该数据类型的默认最小值和最大值。

### 函数原型

```cpp
inline npu_data_variant_t generate_random_data(uint8_t prec_in, double* min = nullptr, double* max = nullptr)
```

### 参数说明

*   `prec_in` (uint8_t): 精度代码，指定要生成的数据类型。可以使用 `dtype` 命名空间中定义的常量，例如 `dtype::INT4`, `dtype::FP16` 等。
*   `min` (double*, 可选):  指向最小值的指针。如果提供，则生成的随机数将不小于该值。如果为 `nullptr`，则使用该数据类型的默认最小值。
*   `max` (double*, 可选):  指向最大值的指针。如果提供，则生成的随机数将不大于该值。如果为 `nullptr`，则使用该数据类型的默认最大值。

### 返回值

`npu_data_variant_t`:  变体类型，包含生成的随机值。具体的类型取决于 `prec_in` 参数指定的精度代码。

### 使用示例

**生成 INT4 类型的随机数 (默认范围):**

```cpp
npu_data_variant_t random_int4 = generate_random_data(dtype::INT4);
npu_int4_t int4_val = std::get<npu_int4_t>(random_int4);
// int4_val 现在包含一个在默认范围 [-8, 7] 内的随机 INT4 值
```

**生成 FP32 类型的随机数 (指定范围):**

```cpp
double min_val = 1.0;
double max_val = 100.0;
npu_data_variant_t random_fp32 = generate_random_data(dtype::FP32, &min_val, &max_val);
npu_fp32_t fp32_val = std::get<npu_fp32_t>(random_fp32);
// fp32_val 现在包含一个在范围 [1.0, 100.0] 内的随机 FP32 值
```

### 注意事项

*   `prec_in` 参数必须是有效的精度代码，否则会抛出 `std::invalid_argument` 异常。
*   如果指定了 `min` 和 `max` 参数，请确保 `min` 不大于 `max`，且指定的范围在数据类型支持的范围内。

---

## from_storage_format

### 功能描述

通用函数，将存储格式的数据转换为 NPU 数据类型。支持将 `uint8_t`, `uint16_t`, 或 `uint32_t` 类型的存储数据转换为 NPU 的各种数据类型，例如 INT4, INT8, INT16, INT32, FP16, FP32, BF16, BBF16。

### 函数原型

```cpp
template<typename T>
inline npu_data_variant_t from_storage_format(uint8_t prec_in, T data)
```

### 参数说明

*   `prec_in` (uint8_t): 精度代码，指定要转换成的 NPU 数据类型。可以使用 `dtype` 命名空间中定义的常量，例如 `dtype::INT4`, `dtype::FP16` 等。
*   `data` (T): 存储格式的数据。类型 `T` 必须是 `uint8_t`, `uint16_t`, 或 `uint32_t` 中的一种，且必须与 `prec_in` 指定的 NPU 数据类型兼容。

### 返回值

`npu_data_variant_t`:  变体类型，包含转换后的 NPU 数据类型值。

### 使用示例

**将 `uint8_t` 存储的 INT4 数据转换为 `npu_int4_t`:**

```cpp
uint8_t int4_uint8 = 5;
npu_data_variant_t int4_variant = from_storage_format<uint8_t>(dtype::INT4, int4_uint8);
npu_int4_t int4_val = std::get<npu_int4_t>(int4_variant);
// int4_val 现在包含从 uint8_t 转换来的 INT4 值 5
```

**将 `uint16_t` 存储的 FP16 数据转换为 `npu_fp16_t`:**

```cpp
uint16_t fp16_uint16 = 0x3c00; // 1.0f 的 FP16 表示
npu_data_variant_t fp16_variant = from_storage_format<uint16_t>(dtype::FP16, fp16_uint16);
npu_fp16_t fp16_val = std::get<npu_fp16_t>(fp16_variant);
// fp16_val 现在包含从 uint16_t 转换来的 FP16 值 1.0f
```

### 注意事项

*   `prec_in` 参数必须是有效的精度代码，否则会抛出 `std::invalid_argument` 异常。
*   存储数据类型 `T` 必须与 `prec_in` 指定的 NPU 数据类型兼容，否则会抛出 `std::bad_variant_access` 异常。 例如，INT4, INT8 应该使用 `uint8_t` 存储，INT16, FP16, BF16, BBF16 应该使用 `uint16_t` 存储，INT32, FP32 应该使用 `uint32_t` 存储。
*   如果 `prec_in` 参数无效，或者存储数据类型不兼容，会抛出 `std::invalid_argument` 异常。

---

## to_storage_format

### 功能描述

通用函数，将 NPU 数据类型转换为存储格式。支持将 NPU 的各种数据类型 (例如 INT4, INT8, INT16, INT32, FP16, FP32, BF16, BBF16) 转换为 `uint8_t`, `uint16_t`, 或 `uint32_t` 类型的存储数据。

### 函数原型

```cpp
template<typename T>
inline T to_storage_format(uint8_t prec_in, const npu_data_variant_t& data_variant)
```

### 参数说明

*   `prec_in` (uint8_t): 精度代码，指定要转换的 NPU 数据类型。可以使用 `dtype` 命名空间中定义的常量，例如 `dtype::INT4`, `dtype::FP16` 等。
*   `data_variant` (const npu_data_variant_t&):  包含 NPU 数据类型值的变体类型。

### 返回值

`T`:  存储格式的数据。类型 `T` 取决于 `prec_in` 指定的 NPU 数据类型，通常为 `uint8_t`, `uint16_t`, 或 `uint32_t`。

### 使用示例

**将 `npu_int4_t` 类型的数据转换为 `uint8_t` 存储格式:**

```cpp
npu_int4_t int4_val(5);
uint8_t int4_uint8 = to_storage_format<uint8_t>(dtype::INT4, npu_data_variant_t(int4_val));
// int4_uint8 现在包含 npu_int4_t 值 5 的 uint8_t 存储格式
```

**将 `npu_fp16_t` 类型的数据转换为 `uint16_t` 存储格式:**

```cpp
npu_fp16_t fp16_val(1.0f);
uint16_t fp16_uint16 = to_storage_format<uint16_t>(dtype::FP16, npu_data_variant_t(fp16_val));
// fp16_uint16 现在包含 npu_fp16_t 值 1.0f 的 uint16_t 存储格式
```

### 注意事项

*   `prec_in` 参数必须是有效的精度代码，否则会抛出 `std::invalid_argument` 异常。
*   返回值类型 `T` 必须与 `prec_in` 指定的 NPU 数据类型兼容，否则会导致编译错误。 例如，INT4, INT8 应该转换为 `uint8_t` 存储，INT16, FP16, BF16, BBF16 应该转换为 `uint16_t` 存储，INT32, FP32 应该转换为 `uint32_t` 存储。
*   如果 `prec_in` 参数无效，或者返回值类型不兼容，会抛出 `std::invalid_argument` 异常或编译错误。

---