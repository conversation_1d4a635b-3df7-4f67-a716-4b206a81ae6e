#include "npu_datagen.h"
#include <cmath>
#include <cstdint>
#include <cstring>
#include <stdexcept>
#include <vector>
#include "../include/utils/utils.h"

using namespace npu;

std::vector<uint8_t> generate_3d_matrix_data(int H,
                                             int W,
                                             int C,
                                             uint8_t prec_in,
                                             bool pad_en,
                                             uint32_t& padded_C)
{
    // 1. 获取 prec_in 的位宽
    uint32_t bit_width = width_code_to_bits(static_cast<dtype::WidthCode>(prec_in & 0x07));
    if (bit_width == 0)
    {
        throw std::invalid_argument("Invalid prec_in value.");
    }

    // 2. 计算 size_dim0a, size_dim0b, rem_dim0
    uint32_t size_dim0a = 256 / bit_width;
    uint32_t size_dim0b = static_cast<uint32_t>(std::ceil(static_cast<double>(C) / size_dim0a));
    uint32_t rem_dim0 = C % size_dim0a;
    padded_C = pad_en ? size_dim0b * size_dim0a : C;  // 根据 pad_en 决定是否 padding

    // 3. 计算缓冲区大小
    uint32_t total_elements = padded_C * W * H;
    uint32_t buffer_size_bytes;
    if (bit_width == 4)
    {
        buffer_size_bytes = (total_elements + 1) / 2;  // 每个字节存储两个INT4值
    }
    else
    {
        buffer_size_bytes = (total_elements * bit_width + 7) / 8;  // 向上取整到字节
    }
    std::vector<uint8_t> buffer(buffer_size_bytes, 0);

    // 4. 生成随机数据并填充缓冲区 (HWC 顺序)
    uint32_t buffer_index = 0;
    uint8_t current_byte = 0;
    bool is_low_nibble = true;  // 用于INT4类型，跟踪当前是否处理低4位
    double min_val = -10.0;
    double max_val = 10.0;
    for (int h = 0; h < H; ++h)
    {
        for (int w = 0; w < W; ++w)
        {
            for (int c = 0; c < C; ++c)
            {
                npu_data_variant_t random_data = generate_random_data(prec_in, &min_val, &max_val);

                if (bit_width == 4)
                {
                    uint8_t data_uint8 = to_storage_format<uint8_t>(prec_in, random_data);
                    uint8_t int4_val = data_uint8 & 0x0F;

                    if (is_low_nibble)
                    {
                        current_byte = int4_val;
                        is_low_nibble = false;
                    }
                    else
                    {
                        current_byte |= (int4_val << 4);
                        buffer[buffer_index++] = current_byte;
                        is_low_nibble = true;
                    }
                }
                else if (bit_width <= 8)
                {
                    buffer[buffer_index++] = to_storage_format<uint8_t>(prec_in, random_data);
                }
                else if (bit_width <= 16)
                {
                    uint16_t data_uint16 = to_storage_format<uint16_t>(prec_in, random_data);
                    std::memcpy(&buffer[buffer_index], &data_uint16, sizeof(uint16_t));
                    buffer_index += sizeof(uint16_t);
                }
                else if (bit_width <= 32)
                {
                    uint32_t data_uint32 = to_storage_format<uint32_t>(prec_in, random_data);
                    std::memcpy(&buffer[buffer_index], &data_uint32, sizeof(uint32_t));
                    buffer_index += sizeof(uint32_t);
                }
            }
            // 处理C维度padding
            if (pad_en && C < padded_C)
            {  // 仅当 pad_en 为 true 时执行 padding
                uint32_t remaining_elements = padded_C - C;
                if (bit_width == 4)
                {
                    // 填充剩余的INT4值(每个字节两个值)
                    if (!is_low_nibble)
                    {
                        // 确保最后一个不完整的字节被正确写入
                        buffer[buffer_index++] = current_byte;
                        is_low_nibble = true;
                    }
                    buffer_index += remaining_elements / 2;
                }
                else
                {
                    buffer_index += (remaining_elements * bit_width + 7) / 8;
                }
            }
        }
    }
    // 处理最后一个不完整的INT4字节
    if (bit_width == 4)
    {
        if (!is_low_nibble)
        {
            // 确保最后一个不完整的字节被正确写入
            buffer[buffer_index++] = current_byte;
        }
        // 验证总大小
        if (buffer_index != buffer_size_bytes)
        {
            throw std::runtime_error("Buffer index mismatch");
        }
    }

    return buffer;
}

std::vector<uint8_t> generate_weight_data(int out_channels,
                                          int in_channels,
                                          int kernel_h,
                                          int kernel_w,
                                          uint8_t prec_in)
{
    // 1. 获取 prec_in 的位宽
    uint32_t bit_width = width_code_to_bits(static_cast<dtype::WidthCode>(prec_in & 0x07));
    if (bit_width == 0)
    {
        throw std::invalid_argument("Invalid prec_in value.");
    }

    // 2. 计算缓冲区大小
    uint32_t total_elements = out_channels * in_channels * kernel_h * kernel_w;
    uint32_t buffer_size_bytes;
    if (bit_width == 4)
    {
        buffer_size_bytes = (total_elements + 1) / 2;  // 每个字节存储两个INT4值
    }
    else
    {
        buffer_size_bytes = (total_elements * bit_width + 7) / 8;  // 向上取整到字节
    }
    std::vector<uint8_t> buffer(buffer_size_bytes, 0);

    // 3. 生成随机数据并填充缓冲区 [kh][kw][ic][oc]
    uint32_t buffer_index = 0;
    uint8_t current_byte = 0;
    bool is_low_nibble = true;
    double min_val = -10.0;
    double max_val = 10.0;
    for (int kh = 0; kh < kernel_h; ++kh)
    {
        for (int kw = 0; kw < kernel_w; ++kw)
        {
            for (int ic = 0; ic < in_channels; ++ic)
            {
                for (int oc = 0; oc < out_channels; ++oc)
                {
                    npu_data_variant_t random_data =
                        generate_random_data(prec_in, &min_val, &max_val);

                    if (bit_width == 4)
                    {
                        uint8_t data_uint8 = to_storage_format<uint8_t>(prec_in, random_data);
                        uint8_t int4_val = data_uint8 & 0x0F;

                        if (is_low_nibble)
                        {
                            current_byte = int4_val;
                            is_low_nibble = false;
                        }
                        else
                        {
                            current_byte |= (int4_val << 4);
                            buffer[buffer_index++] = current_byte;
                            is_low_nibble = true;
                        }
                    }
                    else if (bit_width <= 8)
                    {
                        buffer[buffer_index++] = to_storage_format<uint8_t>(prec_in, random_data);
                    }
                    else if (bit_width <= 16)
                    {
                        uint16_t data_uint16 = to_storage_format<uint16_t>(prec_in, random_data);
                        std::memcpy(&buffer[buffer_index], &data_uint16, sizeof(uint16_t));
                        buffer_index += sizeof(uint16_t);
                    }
                    else if (bit_width <= 32)
                    {
                        uint32_t data_uint32 = to_storage_format<uint32_t>(prec_in, random_data);
                        std::memcpy(&buffer[buffer_index], &data_uint32, sizeof(uint32_t));
                        buffer_index += sizeof(uint32_t);
                    }
                }
            }
        }
    }

    // 处理最后一个不完整的INT4字节
    if (bit_width == 4)
    {
        if (!is_low_nibble)
        {
            // 确保最后一个不完整的字节被正确写入
            buffer[buffer_index++] = current_byte;
        }
        // 验证总大小
        if (buffer_index != buffer_size_bytes)
        {
            throw std::runtime_error("Buffer index mismatch");
        }
    }

    return buffer;
}

std::vector<uint8_t> weight_img2col(const std::vector<uint8_t>& weight_data,
                                    int out_channels,
                                    int in_channels,
                                    int kernel_h,
                                    int kernel_w,
                                    uint8_t prec_in)
{
    // 1. 获取 prec_in 的位宽
    uint32_t bit_width = width_code_to_bits(static_cast<dtype::WidthCode>(prec_in & 0x07));
    if (bit_width == 0)
    {
        throw std::invalid_argument("Invalid prec_in value.");
    }

    // 2. 计算输入维度的元素数量 (Kh*Kw*Ci)
    uint32_t rows = kernel_h * kernel_w * in_channels;
    uint32_t bytes_per_row;

    // 3. 计算每行(对应一个展平后的输入元素)占用的字节数
    if (bit_width == 4)
    {
        bytes_per_row = (out_channels + 1) / 2;  // 每行的所有输出通道占用的字节数
    }
    else
    {
        bytes_per_row = (out_channels * bit_width + 7) / 8;
    }

    // 4. 分配输出缓冲区
    uint32_t total_bytes = rows * bytes_per_row;
    std::vector<uint8_t> output_buffer(total_bytes, 0);

    // 5. 进行布局转换 [Kh][Kw][Ci][Co] -> [Kh*Kw*Ci, Co]
    for (int kh = 0; kh < kernel_h; ++kh)
    {
        for (int kw = 0; kw < kernel_w; ++kw)
        {
            for (int ic = 0; ic < in_channels; ++ic)
            {
                // 计算展平后的行索引
                uint32_t row_idx = (kh * kernel_w * in_channels) + (kw * in_channels) + ic;

                for (int oc = 0; oc < out_channels; ++oc)
                {
                    // 计算源数据中的元素位置
                    uint32_t src_element_idx;
                    uint8_t src_value = 0;

                    if (bit_width == 4)
                    {
                        // 计算源字节索引和位置
                        uint32_t src_byte_idx =
                            ((kh * kernel_w * in_channels * out_channels) +
                             (kw * in_channels * out_channels) + (ic * out_channels) + oc) /
                            2;
                        bool is_high_nibble =
                            (((kh * kernel_w * in_channels * out_channels) +
                              (kw * in_channels * out_channels) + (ic * out_channels) + oc) %
                                 2 ==
                             1);

                        // 读取源字节
                        uint8_t src_byte = weight_data[src_byte_idx];
                        src_value = is_high_nibble ? ((src_byte >> 4) & 0x0F) : (src_byte & 0x0F);

                        // 计算目标位置
                        uint32_t dst_byte_idx = row_idx * bytes_per_row + oc / 2;
                        bool dst_is_high_nibble = (oc % 2 == 1);

                        // 读取目标字节当前值
                        uint8_t dst_byte = output_buffer[dst_byte_idx];

                        // 更新目标字节
                        if (dst_is_high_nibble)
                        {
                            dst_byte = (dst_byte & 0x0F) | (src_value << 4);
                        }
                        else
                        {
                            dst_byte = (dst_byte & 0xF0) | src_value;
                        }

                        // 写回目标字节
                        output_buffer[dst_byte_idx] = dst_byte;
                    }
                    else if (bit_width <= 8)
                    {
                        // 8位数据
                        src_element_idx = (kh * kernel_w * in_channels * out_channels) +
                                          (kw * in_channels * out_channels) + (ic * out_channels) +
                                          oc;
                        src_value = weight_data[src_element_idx];
                        output_buffer[row_idx * bytes_per_row + oc] = src_value;
                    }
                    else if (bit_width <= 16)
                    {
                        // 16位数据
                        src_element_idx =
                            ((kh * kernel_w * in_channels * out_channels) +
                             (kw * in_channels * out_channels) + (ic * out_channels) + oc) *
                            2;
                        uint16_t src_value16;
                        std::memcpy(&src_value16, &weight_data[src_element_idx], sizeof(uint16_t));
                        std::memcpy(&output_buffer[row_idx * bytes_per_row + oc * 2],
                                    &src_value16,
                                    sizeof(uint16_t));
                    }
                    else if (bit_width <= 32)
                    {
                        // 32位数据
                        src_element_idx =
                            ((kh * kernel_w * in_channels * out_channels) +
                             (kw * in_channels * out_channels) + (ic * out_channels) + oc) *
                            4;
                        uint32_t src_value32;
                        std::memcpy(&src_value32, &weight_data[src_element_idx], sizeof(uint32_t));
                        std::memcpy(&output_buffer[row_idx * bytes_per_row + oc * 4],
                                    &src_value32,
                                    sizeof(uint32_t));
                    }
                }
            }
        }
    }

    return output_buffer;
}