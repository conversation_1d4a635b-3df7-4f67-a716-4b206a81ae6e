#pragma once

#include <cstdint>
#include <random>
#include <sstream>
#include <stdexcept>
#include <variant>
#include "../include/npu_config.h"
#include "ac_int.h"
#include "ac_std_float.h"

namespace npu
{
typedef ac_int<4, true> npu_int4_t;
typedef ac_int<8, true> npu_int8_t;
typedef ac_int<16, true> npu_int16_t;
typedef ac_int<32, true> npu_int32_t;
typedef ac::bfloat16 npu_bf16_t;
typedef ac_ieee_float16 npu_fp16_t;
typedef ac_ieee_float32 npu_fp32_t;
// Use negative max values for proper range
static const float npu_fp16_min = -npu_fp16_t::max().to_float();
static const float npu_fp16_max = npu_fp16_t::max().to_float();
static const float npu_fp32_min = -npu_fp32_t::max().to_float();
static const float npu_fp32_max = npu_fp32_t::max().to_float();
static const float npu_bf16_min = -npu_bf16_t::max().to_float();
static const float npu_bf16_max = npu_bf16_t::max().to_float();
static std::random_device rd;
static std::mt19937 gen;

// Define a variant type to hold all possible NPU data types
typedef std::
    variant<npu_int4_t, npu_int8_t, npu_int16_t, npu_int32_t, npu_fp16_t, npu_fp32_t, npu_bf16_t>
        npu_data_variant_t;

inline void init_random_generator()
{
    gen.seed(rd());
}
inline npu_int4_t generate_random_int4(int min = -8, int max = 7)
{
    static bool initialized = false;
    if (!initialized)
    {
        init_random_generator();
        initialized = true;
    }
    std::uniform_int_distribution<> distrib(min, max);
    return npu_int4_t(distrib(gen));
}
inline npu_int8_t generate_random_int8(int min = -128, int max = 127)
{
    static bool initialized = false;
    if (!initialized)
    {
        init_random_generator();
        initialized = true;
    }
    std::uniform_int_distribution<> distrib(min, max);
    return npu_int8_t(distrib(gen));
}
inline npu_int16_t generate_random_int16(int min = -32768, int max = 32767)
{
    static bool initialized = false;
    if (!initialized)
    {
        init_random_generator();
        initialized = true;
    }
    std::uniform_int_distribution<> distrib(min, max);
    return npu_int16_t(distrib(gen));
}
inline npu_int32_t generate_random_int32(int min = -2147483648, int max = 2147483647)
{
    static bool initialized = false;
    if (!initialized)
    {
        init_random_generator();
        initialized = true;
    }
    std::uniform_int_distribution<> distrib(min, max);
    return npu_int32_t(distrib(gen));
}
// Helper function to generate random floating point numbers with proper bit patterns
template <typename T>
inline T generate_random_float_impl(float min, float max, int exp_bits, int frac_bits)
{
    static bool initialized = false;
    if (!initialized)
    {
        init_random_generator();
        initialized = true;
    }

    // If min/max specified, use uniform distribution
    if (min != T::min().to_float() || max != T::max().to_float())
    {
        std::uniform_real_distribution<> distrib(min, max);
        return T(distrib(gen));
    }

    // Generate random bits for each field
    std::uniform_int_distribution<> sign_dist(0, 1);
    std::uniform_int_distribution<> exp_dist(0, (1 << exp_bits) - 1);
    std::uniform_int_distribution<> frac_dist(0, (1 << frac_bits) - 1);

    uint32_t sign = sign_dist(gen);
    uint32_t exp = exp_dist(gen);
    uint32_t frac = frac_dist(gen);

    // Combine fields based on the floating point format
    uint32_t bits;
    if constexpr (sizeof(T) == 2)
    {  // FP16 or BF16
        bits = (sign << 15) | (exp << (15 - exp_bits)) | frac;
    }
    else
    {  // FP32
        bits = (sign << 31) | (exp << (31 - exp_bits)) | frac;
    }

    T result;
    result.set_data(bits);
    return result;
}

inline npu_fp16_t generate_random_fp16(float min = npu_fp16_min, float max = npu_fp16_max)
{
    return generate_random_float_impl<npu_fp16_t>(
        min, max, 5, 10);  // FP16: 1 sign + 5 exp + 10 frac
}

inline npu_fp32_t generate_random_fp32(float min = npu_fp32_min, float max = npu_fp32_max)
{
    return generate_random_float_impl<npu_fp32_t>(
        min, max, 8, 23);  // FP32: 1 sign + 8 exp + 23 frac
}

inline npu_bf16_t generate_random_bf16(float min = npu_bf16_min, float max = npu_bf16_max)
{
    return generate_random_float_impl<npu_bf16_t>(min, max, 8, 7);  // BF16: 1 sign + 8 exp + 7 frac
}

inline uint8_t npu_int4_to_uint8_t(npu_int4_t data)
{
    return static_cast<uint8_t>(data.to_int());
}

inline npu_int4_t npu_int4_from_uint8_t(uint8_t data)
{
    return npu_int4_t(data);
}

inline uint8_t npu_int8_to_uint8_t(npu_int8_t data)
{
    return static_cast<uint8_t>(data.to_int());
}
inline npu_int8_t npu_int8_from_uint8_t(uint8_t data)
{
    return npu_int8_t(data);
}

inline uint16_t npu_int16_to_uint16_t(npu_int16_t data)
{
    return static_cast<uint16_t>(data.to_int());
}
inline npu_int16_t npu_int16_from_uint16_t(uint16_t data)
{
    return npu_int16_t(static_cast<int16_t>(data));
}
inline uint32_t npu_int32_to_uint32_t(npu_int32_t data)
{
    return static_cast<uint32_t>(data.to_int());
}
inline npu_int32_t npu_int32_from_uint32_t(uint32_t data)
{
    return npu_int32_t(static_cast<int32_t>(data));
}

inline uint16_t npu_fp16_to_uint16_t(npu_fp16_t data)
{
    return static_cast<uint16_t>(data.data());
}
inline npu_fp16_t npu_fp16_from_uint16_t(uint16_t data)
{
    npu_fp16_t data_fp16;
    data_fp16.set_data(data);
    return data_fp16;
}

inline uint16_t npu_bf16_to_uint16_t(npu_bf16_t data)
{
    return static_cast<uint16_t>(data.data());
}
inline npu_bf16_t npu_bf16_from_uint16_t(uint16_t data)
{
    npu_bf16_t result;
    result.set_data(data);
    return result;
}

inline uint32_t npu_fp32_to_uint32_t(npu_fp32_t data)
{
    return static_cast<uint32_t>(data.data());
}

inline npu_fp32_t npu_fp32_from_uint32_t(uint32_t data)
{
    npu_fp32_t result;
    result.set_data(data);
    return result;
}

/**
 * 获取指定精度类型的默认最小值
 *
 * @param prec_in 精度代码，如dtype::INT4, dtype::FP16等
 * @return 该精度类型的默认最小值
 */
inline double get_default_min(uint8_t prec_in)
{
    if (prec_in == dtype::INT4)
        return -8;
    if (prec_in == dtype::INT8)
        return -128;
    if (prec_in == dtype::INT16)
        return -32768;
    if (prec_in == dtype::INT32)
        return -2147483648.0;
    if (prec_in == dtype::FP16)
        return npu_fp16_min;
    if (prec_in == dtype::FP32)
        return npu_fp32_min;
    if (prec_in == dtype::BF16)
        return npu_bf16_min;

    throw std::invalid_argument("Invalid precision code");
}

/**
 * 获取指定精度类型的默认最大值
 *
 * @param prec_in 精度代码，如dtype::INT4, dtype::FP16等
 * @return 该精度类型的默认最大值
 */
inline double get_default_max(uint8_t prec_in)
{
    if (prec_in == dtype::INT4)
        return 7;
    if (prec_in == dtype::INT8)
        return 127;
    if (prec_in == dtype::INT16)
        return 32767;
    if (prec_in == dtype::INT32)
        return 2147483647.0;
    if (prec_in == dtype::FP16)
        return npu_fp16_max;
    if (prec_in == dtype::FP32)
        return npu_fp32_max;
    if (prec_in == dtype::BF16)
        return npu_bf16_max;

    throw std::invalid_argument("Invalid precision code");
}

/**
 * 通用函数，根据精度代码生成随机数据
 *
 * @param prec_in 精度代码，如dtype::INT4, dtype::FP16等
 * @param min 最小值，如果为nullptr则使用该类型的默认最小值
 * @param max 最大值，如果为nullptr则使用该类型的默认最大值
 * @return 包含生成的随机值的变体类型
 */
inline npu_data_variant_t generate_random_data(uint8_t prec_in,
                                               double* min = nullptr,
                                               double* max = nullptr)
{
    static bool initialized = false;
    if (!initialized)
    {
        init_random_generator();
        initialized = true;
    }

    // 获取默认的最小值和最大值
    double min_val = min ? *min : get_default_min(prec_in);
    double max_val = max ? *max : get_default_max(prec_in);

    if (prec_in == dtype::INT4)
    {
        min_val = std::max(min_val, static_cast<double>(-8));  // 限制最小值
        max_val = std::min(max_val, static_cast<double>(7));   // 限制最大值
        return generate_random_int4(static_cast<int>(min_val), static_cast<int>(max_val));
    }
    else if (prec_in == dtype::INT8)
    {
        min_val = std::max(min_val, static_cast<double>(-128));
        max_val = std::min(max_val, static_cast<double>(127));
        return generate_random_int8(static_cast<int>(min_val), static_cast<int>(max_val));
    }
    else if (prec_in == dtype::INT16)
    {
        min_val = std::max(min_val, static_cast<double>(-32768));
        max_val = std::min(max_val, static_cast<double>(32767));
        return generate_random_int16(static_cast<int>(min_val), static_cast<int>(max_val));
    }
    else if (prec_in == dtype::INT32)
    {
        min_val = std::max(min_val, static_cast<double>(-2147483648.0));
        max_val = std::min(max_val, static_cast<double>(2147483647.0));
        return generate_random_int32(static_cast<int>(min_val), static_cast<int>(max_val));
    }
    else if (prec_in == dtype::FP16)
    {
        min_val = std::max(min_val, static_cast<double>(npu_fp16_min));
        max_val = std::min(max_val, static_cast<double>(npu_fp16_max));
        return generate_random_fp16(static_cast<float>(min_val), static_cast<float>(max_val));
    }
    else if (prec_in == dtype::FP32)
    {
        min_val = std::max(min_val, static_cast<double>(npu_fp32_min));
        max_val = std::min(max_val, static_cast<double>(npu_fp32_max));
        return generate_random_fp32(static_cast<float>(min_val), static_cast<float>(max_val));
    }
    else if (prec_in == dtype::BF16)
    {
        min_val = std::max(min_val, static_cast<double>(npu_bf16_min));
        max_val = std::min(max_val, static_cast<double>(npu_bf16_max));
        return generate_random_bf16(static_cast<float>(min_val), static_cast<float>(max_val));
    }

    throw std::invalid_argument("Invalid precision code");
}

/**
 * 通用函数，将存储格式转换为NPU数据类型
 *
 * @param prec_in 精度代码，如dtype::INT4, dtype::FP16等
 * @param data 存储格式数据（uint8_t, uint16_t, 或 uint32_t）
 * @return 包含转换后值的变体类型
 */
template <typename T>
inline npu_data_variant_t from_storage_format(uint8_t prec_in, T data)
{
    // 根据精度代码直接调用相应的函数
    if (prec_in == dtype::INT4)
    {
        if constexpr (std::is_same_v<T, uint8_t>)
        {
            return npu_int4_from_uint8_t(data);
        }
    }
    else if (prec_in == dtype::INT8)
    {
        if constexpr (std::is_same_v<T, uint8_t>)
        {
            return npu_int8_from_uint8_t(data);
        }
    }
    else if (prec_in == dtype::INT16)
    {
        if constexpr (std::is_same_v<T, uint16_t>)
        {
            return npu_int16_from_uint16_t(data);
        }
    }
    else if (prec_in == dtype::INT32)
    {
        if constexpr (std::is_same_v<T, uint32_t>)
        {
            return npu_int32_from_uint32_t(data);
        }
    }
    else if (prec_in == dtype::FP16)
    {
        if constexpr (std::is_same_v<T, uint16_t>)
        {
            return npu_fp16_from_uint16_t(data);
        }
    }
    else if (prec_in == dtype::FP32)
    {
        if constexpr (std::is_same_v<T, uint32_t>)
        {
            return npu_fp32_from_uint32_t(data);
        }
    }
    else if (prec_in == dtype::BF16)
    {
        if constexpr (std::is_same_v<T, uint16_t>)
        {
            return npu_bf16_from_uint16_t(data);
        }
    }

    throw std::invalid_argument("Invalid precision code or incompatible storage type");
}

/**
 * 通用函数，将NPU数据类型转换为存储格式
 *
 * @param prec_in 精度代码，如dtype::INT4, dtype::FP16等
 * @param data_variant 包含NPU数据类型值的变体
 * @return 存储格式表示（uint8_t, uint16_t, 或 uint32_t）
 */
template <typename T>
inline T to_storage_format(uint8_t prec_in, const npu_data_variant_t& data_variant)
{
    // 根据精度代码直接调用相应的函数
    if (prec_in == dtype::INT4)
    {
        if constexpr (std::is_same_v<T, uint8_t>)
        {
            return npu_int4_to_uint8_t(std::get<npu_int4_t>(data_variant));
        }
    }
    else if (prec_in == dtype::INT8)
    {
        if constexpr (std::is_same_v<T, uint8_t>)
        {
            return npu_int8_to_uint8_t(std::get<npu_int8_t>(data_variant));
        }
    }
    else if (prec_in == dtype::INT16)
    {
        if constexpr (std::is_same_v<T, uint16_t>)
        {
            return npu_int16_to_uint16_t(std::get<npu_int16_t>(data_variant));
        }
    }
    else if (prec_in == dtype::INT32)
    {
        if constexpr (std::is_same_v<T, uint32_t>)
        {
            return npu_int32_to_uint32_t(std::get<npu_int32_t>(data_variant));
        }
    }
    else if (prec_in == dtype::FP16)
    {
        if constexpr (std::is_same_v<T, uint16_t>)
        {
            return npu_fp16_to_uint16_t(std::get<npu_fp16_t>(data_variant));
        }
    }
    else if (prec_in == dtype::FP32)
    {
        if constexpr (std::is_same_v<T, uint32_t>)
        {
            return npu_fp32_to_uint32_t(std::get<npu_fp32_t>(data_variant));
        }
    }
    else if (prec_in == dtype::BF16)
    {
        if constexpr (std::is_same_v<T, uint16_t>)
        {
            return npu_bf16_to_uint16_t(std::get<npu_bf16_t>(data_variant));
        }
    }

    throw std::invalid_argument("Invalid precision code or incompatible storage type");
}

}  // namespace npu
