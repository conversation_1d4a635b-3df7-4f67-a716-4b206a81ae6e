# NPU MAC Unit实现说明

本文档描述了NPU的矩阵乘法加法单元（MAC Unit）的实现。MAC单元是神经网络加速器中的核心计算单元，负责执行矩阵乘法和加法操作，通常用于实现卷积、全连接等神经网络层。

## 功能概述

NPU MAC单元主要支持以下功能：

1. 矩阵乘法：计算 C = A × B，其中A、B、C为矩阵
2. 矩阵加法：计算 C = A + B
3. 支持不同精度的输入数据类型，包括整数类型（INT4、INT8、INT16、INT32）和浮点类型（FP16、FP32、BF16、BBF16）
4. 精度转换：根据输入数据类型自动执行精度转换

## 精度转换规则

根据设计要求，MAC单元的精度转换规则如下：

1. 如果输入矩阵和权重矩阵都是INT类型，则输出为INT32
2. 如果任一输入为FP类型，则输出为FP32
3. 内部计算时，INT类型数据统一转为int32后执行乘法、加法运算
4. 内部计算时，FP类型数据统一转为fp32后执行乘法、加法运算

这种设计移除了针对不同精度（INT4、INT8、INT16、FP16、BF16等）的分类实现，仅需实现整数和浮点两种函数即可满足所有精度类型的计算需求。

## 矩阵布局说明

在神经网络计算中，对数据的存储和处理布局有特定要求。以卷积操作为例：

1. 输入特征图矩阵布局为 [H,W,C]，在内存中先排布C通道，然后W，最后H
2. 输入特征图经过im2col_transform转换后，布局变为 [rows,cols] = [out_height*out_width, kernel_x*kernel_y*in_channel]
3. 权重矩阵 [Kh,Kw,Ci,Co] 经过weight_img2col转换后，布局变为 [rows,cols] = [Kh*Kw*Ci, Co]

## 实现细节

### 数据表示

使用`ac_types`库提供的数据类型表示不同精度的数据：

```cpp
typedef ac_int<4, true> npu_int4_t;     // 4位整数
typedef ac_int<8, true> npu_int8_t;     // 8位整数
typedef ac_int<16, true> npu_int16_t;   // 16位整数
typedef ac_int<32, true> npu_int32_t;   // 32位整数
typedef ac::bfloat16 npu_bf16_t;        // bfloat16
typedef ac_std_float<16, 1> npu_bff16_t; // 自定义16位浮点
typedef ac_ieee_float16 npu_fp16_t;     // IEEE半精度浮点
typedef ac_ieee_float32 npu_fp32_t;     // IEEE单精度浮点
```

### 矩阵乘法实现

#### 整数矩阵乘法

```cpp
template <typename T>
void matrix_multiply_add_int(const Matrix<T>& A, const Matrix<T>& B, Matrix<T>& C) {
    // 执行矩阵乘法, C = A * B
    for (int i = 0; i < A.rows; ++i) {
        for (int j = 0; j < B.cols; ++j) {
            npu_int32_t sum(0);  // 使用int32进行累加
            for (int k = 0; k < A.cols; ++k) {
                // 内部先统一转为int32，再执行乘法和加法运算
                npu_int32_t a_val(A.at(i, k).to_int());
                npu_int32_t b_val(B.at(k, j).to_int());
                sum += a_val * b_val;
            }
            // 将结果赋给输出矩阵
            C.at(i, j) = T(sum.to_int());
        }
    }
}
```

#### 浮点矩阵乘法

```cpp
template <typename T>
void matrix_multiply_add_fp(const Matrix<T>& A, const Matrix<T>& B, Matrix<T>& C) {
    // 执行矩阵乘法, C = A * B
    for (int i = 0; i < A.rows; ++i) {
        for (int j = 0; j < B.cols; ++j) {
            npu_fp32_t sum(0.0f);  // 使用fp32进行累加
            for (int k = 0; k < A.cols; ++k) {
                // 内部先统一转为fp32，再执行乘法和加法运算
                npu_fp32_t a_val(A.at(i, k).to_float());
                npu_fp32_t b_val(B.at(k, j).to_float());
                sum += a_val * b_val;
            }
            // 将结果赋给输出矩阵
            C.at(i, j) = T(sum.to_float());
        }
    }
}
```

### 权重布局转换

`weight_img2col`函数将输入的[out_channels, in_channels, kernel_h, kernel_w]格式的权重矩阵转换为[kernel_h*kernel_w*in_channels, out_channels]格式的二维矩阵，以便直接与im2col转换后的输入特征图进行矩阵乘法。

转换过程中需考虑不同精度的数据占用的位宽，实现多精度的统一支持。

## 卷积操作仿真

卷积操作可以通过以下步骤使用矩阵乘法实现：

1. 通过im2col操作将输入特征图转换为矩阵形式
2. 通过weight_img2col操作将卷积核权重转换为矩阵形式
3. 执行矩阵乘法：output = im2col_matrix × weight_matrix
4. 将结果reshape为输出特征图的形状

这种实现方式既能充分利用矩阵乘法的并行性，又能适应不同大小的卷积核和特征图。

## 使用示例

以下示例展示了如何使用MAC单元执行整数和浮点矩阵乘法：

```cpp
// 整数矩阵乘法示例
Matrix<npu_int8_t> A(4, 3);  // 4x3矩阵
Matrix<npu_int8_t> B(3, 2);  // 3x2矩阵
Matrix<npu_int8_t> C(4, 2);  // 结果矩阵

// 初始化矩阵数据...

// 执行矩阵乘法
matrix_multiply_add_int(A, B, C);

// 浮点矩阵乘法示例
Matrix<npu_fp16_t> A_fp(3, 4);  // 3x4矩阵
Matrix<npu_fp16_t> B_fp(4, 2);  // 4x2矩阵 
Matrix<npu_fp16_t> C_fp(3, 2);  // 结果矩阵

// 初始化矩阵数据...

// 执行矩阵乘法
matrix_multiply_add_fp(A_fp, B_fp, C_fp);
```

## 性能考虑

当前实现是软件仿真模型，实际硬件实现时，可以考虑以下优化手段：

1. 数据并行：同时执行多个MAC操作
2. 流水线：将MAC操作拆分为多个阶段，提高吞吐量
3. 专用硬件：针对不同精度的数据类型设计专用计算单元
4. 存储优化：优化数据存取路径，减少访存延迟

## 扩展功能

未来可以考虑扩展以下功能：

1. 支持更多矩阵操作，如矩阵转置、矩阵求逆等
2. 支持稀疏矩阵计算，提高对稀疏神经网络的处理效率
3. 支持更多激活函数的硬件加速
4. 支持量化感知训练（QAT）中的特殊操作 