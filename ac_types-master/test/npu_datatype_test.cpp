#include "gtest/gtest.h"
#include "npu_datatypes.h"

namespace npu
{  // 显式打开 npu 命名空间

TEST(npu_datatypes_test, get_default_min_test)
{
    EXPECT_EQ(get_default_min(dtype::INT4), -8.0);
    EXPECT_EQ(get_default_min(dtype::INT8), -128.0);
    EXPECT_EQ(get_default_min(dtype::INT16), -32768.0);
    EXPECT_EQ(get_default_min(dtype::INT32), -2147483648.0);
    EXPECT_EQ(get_default_min(dtype::FP16), npu_fp16_min);
    EXPECT_EQ(get_default_min(dtype::FP32), npu_fp32_min);
    EXPECT_EQ(get_default_min(dtype::BF16), npu_bf16_min);
    EXPECT_THROW(get_default_min(255), std::invalid_argument);  // Invalid Code
}

TEST(npu_datatypes_test, get_default_max_test)
{
    EXPECT_EQ(get_default_max(dtype::INT4), 7.0);
    EXPECT_EQ(get_default_max(dtype::INT8), 127.0);
    EXPECT_EQ(get_default_max(dtype::INT16), 32767.0);
    EXPECT_EQ(get_default_max(dtype::INT32), 2147483647.0);
    EXPECT_EQ(get_default_max(dtype::FP16), npu_fp16_max);
    EXPECT_EQ(get_default_max(dtype::FP32), npu_fp32_max);
    EXPECT_EQ(get_default_max(dtype::BF16), npu_bf16_max);
    EXPECT_THROW(get_default_max(255), std::invalid_argument);  // Invalid Code
}

TEST(npu_datatype_max_min_test, test)
{
    static const float npu_fp16_min = npu_fp16_t::min().to_float();
    static const float npu_fp16_max = npu_fp16_t::max().to_float();
    static const float npu_fp32_min = npu_fp32_t::min().to_float();
    static const float npu_fp32_max = npu_fp32_t::max().to_float();
    static const float npu_bf16_min = npu_bf16_t::min().to_float();
    static const float npu_bf16_max = npu_bf16_t::max().to_float();
    std::cout << "npu_fp16_min: " << npu_fp16_min << std::endl;
    std::cout << "npu_fp16_max: " << npu_fp16_max << std::endl;
    std::cout << "npu_fp32_min: " << npu_fp32_min << std::endl;
    std::cout << "npu_fp32_max: " << npu_fp32_max << std::endl;
    std::cout << "npu_bf16_min: " << npu_bf16_min << std::endl;
    std::cout << "npu_bf16_max: " << npu_bf16_max << std::endl;
}

TEST(npu_datatypes_test, generate_random_data_test)
{
    // INT4 默认范围
    for (int i = 0; i < 100; ++i)
    {
        npu_data_variant_t data = generate_random_data(dtype::INT4);
        EXPECT_TRUE(std::holds_alternative<npu_int4_t>(data));
        npu_int4_t val = std::get<npu_int4_t>(data);
        EXPECT_GE(val.to_int(), -8);
        EXPECT_LE(val.to_int(), 7);
    }

    // INT8 默认范围
    for (int i = 0; i < 100; ++i)
    {
        npu_data_variant_t data = generate_random_data(dtype::INT8);
        EXPECT_TRUE(std::holds_alternative<npu_int8_t>(data));
        npu_int8_t val = std::get<npu_int8_t>(data);
        EXPECT_GE(val.to_int(), -128);
        EXPECT_LE(val.to_int(), 127);
    }

    // INT16 默认范围
    for (int i = 0; i < 100; ++i)
    {
        npu_data_variant_t data = generate_random_data(dtype::INT16);
        EXPECT_TRUE(std::holds_alternative<npu_int16_t>(data));
        npu_int16_t val = std::get<npu_int16_t>(data);
        EXPECT_GE(val.to_int(), -32768);
        EXPECT_LE(val.to_int(), 32767);
    }

    // INT32 默认范围
    for (int i = 0; i < 100; ++i)
    {
        npu_data_variant_t data = generate_random_data(dtype::INT32);
        EXPECT_TRUE(std::holds_alternative<npu_int32_t>(data));
        npu_int32_t val = std::get<npu_int32_t>(data);
        EXPECT_GE(val.to_int(), -2147483648);
        EXPECT_LE(val.to_int(), 2147483647);
    }

    // FP16 默认范围
    // Without min/max
    std::cout << "\nTesting without min/max:" << std::endl;
    for (int i = 0; i < 5; ++i)
    {
        npu_data_variant_t data = generate_random_data(dtype::FP16);
        npu_fp16_t val = std::get<npu_fp16_t>(data);
        std::cout << "Raw bits: 0x" << std::hex << val.data() << " Float value: " << std::dec
                  << val.to_float() << std::endl;
    }
    // With min/max
    double min_val = -100.0;
    double max_val = 100.0;
    std::cout << "\nTesting with min=" << min_val << ", max=" << max_val << std::endl;
    for (int i = 0; i < 5; ++i)
    {
        npu_data_variant_t data = generate_random_data(dtype::FP16, &min_val, &max_val);
        npu_fp16_t val = std::get<npu_fp16_t>(data);
        std::cout << "Raw bits: 0x" << std::hex << val.data() << " Float value: " << std::dec
                  << val.to_float() << std::endl;
    }
    std::cout << "FP16 range: " << std::scientific << npu_fp16_min << " to " << npu_fp16_max
              << std::endl;
    std::cout << "Min raw bits: 0x" << std::hex << npu_fp16_t(npu_fp16_min).data()
              << " Max raw bits: 0x" << npu_fp16_t(npu_fp16_max).data() << std::endl;
    // FP32 默认范围
    for (int i = 0; i < 100; ++i)
    {
        npu_data_variant_t data = generate_random_data(dtype::FP32);
        EXPECT_TRUE(std::holds_alternative<npu_fp32_t>(data));
        npu_fp32_t val = std::get<npu_fp32_t>(data);
        EXPECT_GE(val.to_float(), npu_fp32_min);
        EXPECT_LE(val.to_float(), npu_fp32_max);
    }

    // BF16 默认范围
    for (int i = 0; i < 100; ++i)
    {
        npu_data_variant_t data = generate_random_data(dtype::BF16);
        EXPECT_TRUE(std::holds_alternative<npu_bf16_t>(data));
        npu_bf16_t val = std::get<npu_bf16_t>(data);
        EXPECT_GE(val.to_float(), npu_bf16_min);
        EXPECT_LE(val.to_float(), npu_bf16_max);
    }

    // INT4 指定范围
    for (int i = 0; i < 100; ++i)
    {
        double min_val = -5.0;
        double max_val = 5.0;
        npu_data_variant_t data = generate_random_data(dtype::INT4, &min_val, &max_val);
        EXPECT_TRUE(std::holds_alternative<npu_int4_t>(data));
        npu_int4_t val = std::get<npu_int4_t>(data);
        EXPECT_GE(val.to_int(), -5);
        EXPECT_LE(val.to_int(), 5);
    }

    // FP32 指定范围
    for (int i = 0; i < 100; ++i)
    {
        double min_val = 1.0;
        double max_val = 100.0;
        npu_data_variant_t data = generate_random_data(dtype::FP32, &min_val, &max_val);
        EXPECT_TRUE(std::holds_alternative<npu_fp32_t>(data));
        npu_fp32_t val = std::get<npu_fp32_t>(data);
        float fp_val{val};
        EXPECT_GE(fp_val, 1.0);
        EXPECT_LE(fp_val, 100.0);
    }

    // Invalid Code
    EXPECT_THROW(generate_random_data(255), std::invalid_argument);
}

TEST(npu_datatypes_test, from_storage_format_test)
{
    // INT4 from uint8_t
    uint8_t int4_uint8 = 5;
    npu_data_variant_t int4_variant = from_storage_format<uint8_t>(dtype::INT4, int4_uint8);
    EXPECT_TRUE(std::holds_alternative<npu_int4_t>(int4_variant));
    EXPECT_EQ(std::get<npu_int4_t>(int4_variant).to_int(), 5);

    // INT8 from uint8_t
    uint8_t int8_uint8 = 100;
    npu_data_variant_t int8_variant = from_storage_format<uint8_t>(dtype::INT8, int8_uint8);
    EXPECT_TRUE(std::holds_alternative<npu_int8_t>(int8_variant));
    EXPECT_EQ(std::get<npu_int8_t>(int8_variant).to_int(), 100);

    // INT16 from uint16_t
    uint16_t int16_uint16 = 30000;
    npu_data_variant_t int16_variant = from_storage_format<uint16_t>(dtype::INT16, int16_uint16);
    EXPECT_TRUE(std::holds_alternative<npu_int16_t>(int16_variant));
    EXPECT_EQ(std::get<npu_int16_t>(int16_variant).to_int(), 30000);

    // INT32 from uint32_t
    uint32_t int32_uint32 = 2000000000;
    npu_data_variant_t int32_variant = from_storage_format<uint32_t>(dtype::INT32, int32_uint32);
    EXPECT_TRUE(std::holds_alternative<npu_int32_t>(int32_variant));
    EXPECT_EQ(std::get<npu_int32_t>(int32_variant).to_int(), 2000000000);

    // FP16 from uint16_t
    uint16_t fp16_uint16 = 0x3c00;
    npu_data_variant_t fp16_variant = from_storage_format<uint16_t>(dtype::FP16, fp16_uint16);
    EXPECT_TRUE(std::holds_alternative<npu_fp16_t>(fp16_variant));
    EXPECT_FLOAT_EQ(std::get<npu_fp16_t>(fp16_variant).to_float(), 1.0f);

    // FP32 from uint32_t
    uint32_t fp32_uint32 = 0x3f800000;
    npu_data_variant_t fp32_variant = from_storage_format<uint32_t>(dtype::FP32, fp32_uint32);
    EXPECT_TRUE(std::holds_alternative<npu_fp32_t>(fp32_variant));
    EXPECT_FLOAT_EQ(std::get<npu_fp32_t>(fp32_variant).to_float(), 1.0f);

    // BF16 from uint16_t
    uint16_t bf16_uint16 = 0x3f80;
    npu_data_variant_t bf16_variant = from_storage_format<uint16_t>(dtype::BF16, bf16_uint16);
    EXPECT_TRUE(std::holds_alternative<npu_bf16_t>(bf16_variant));
    EXPECT_FLOAT_EQ(std::get<npu_bf16_t>(bf16_variant).to_float(), 1.0f);

    // Invalid Code
    uint8_t invalid_uint8 = 5;
    EXPECT_THROW(from_storage_format<uint8_t>(255, invalid_uint8), std::invalid_argument);

    // Incompatible storage type (INT4 from uint16_t)
    uint16_t incompatible_uint16 = 5;
    EXPECT_THROW(from_storage_format<uint16_t>(dtype::INT4, incompatible_uint16),
                 std::invalid_argument);
}

TEST(npu_datatypes_test, to_storage_format_test)
{
    // INT4 to uint8_t
    npu_int4_t int4_val(5);
    uint8_t int4_uint8 = to_storage_format<uint8_t>(dtype::INT4, npu_data_variant_t(int4_val));
    EXPECT_EQ(int4_uint8, 5);

    // INT8 to uint8_t
    npu_int8_t int8_val(100);
    uint8_t int8_uint8 = to_storage_format<uint8_t>(dtype::INT8, npu_data_variant_t(int8_val));
    EXPECT_EQ(int8_uint8, 100);

    // INT16 to uint16_t
    npu_int16_t int16_val(30000);
    uint16_t int16_uint16 =
        to_storage_format<uint16_t>(dtype::INT16, npu_data_variant_t(int16_val));
    EXPECT_EQ(int16_uint16, 30000);

    // INT32 to uint32_t
    npu_int32_t int32_val(2000000000);
    uint32_t int32_uint32 =
        to_storage_format<uint32_t>(dtype::INT32, npu_data_variant_t(int32_val));
    EXPECT_EQ(int32_uint32, 2000000000);

    // FP16 to uint16_t
    npu_fp16_t fp16_val(1.0f);
    uint16_t fp16_uint16 = to_storage_format<uint16_t>(dtype::FP16, npu_data_variant_t(fp16_val));
    EXPECT_EQ(fp16_uint16, 0x3c00);

    // FP32 to uint32_t
    npu_fp32_t fp32_val(1.0f);
    uint32_t fp32_uint32 = to_storage_format<uint32_t>(dtype::FP32, npu_data_variant_t(fp32_val));
    EXPECT_EQ(fp32_uint32, 0x3f800000);

    // BF16 to uint16_t
    npu_bf16_t bf16_val(1.0f);
    uint16_t bf16_uint16 = to_storage_format<uint16_t>(dtype::BF16, npu_data_variant_t(bf16_val));
    EXPECT_EQ(bf16_uint16, 0x3f80);

    // Invalid Code
    npu_int4_t invalid_int4_val(5);
    EXPECT_THROW(to_storage_format<uint8_t>(255, npu_data_variant_t(invalid_int4_val)),
                 std::invalid_argument);

    // Incompatible NPU data type (INT4 to uint16_t - should be uint8_t)
    npu_fp16_t incompatible_fp16_val(1.0f);
    EXPECT_THROW(to_storage_format<uint8_t>(dtype::INT4, npu_data_variant_t(incompatible_fp16_val)),
                 std::bad_variant_access);
}

}  // namespace npu

int main(int argc, char** argv)
{
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}