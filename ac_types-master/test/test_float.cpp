#include <ac_std_float.h>
#include <iostream>

int main()
{
    ac_std_float<16, 5> fp16_val;
    std::cout << "largeest value:" << fp16_val.max() << std::endl;
    std::cout << "smallest value:" << fp16_val.min() << std::endl;
    fp16_val.set_data(16896);                              // 直接设置底层位表示
    std::cout << "FP16 value: " << fp16_val << std::endl;  // 输出 FP16 值
    std::cout << "FP16 data: " << fp16_val.data() << std::endl;
    std::cout << "to float: " << fp16_val.to_float() << std::endl;
    ac_std_float<16, 5> a(2.99999999);
    ac_std_float<16, 5> b(3.0);
    std::cout << "a :" << a.data() << std::endl;
    // 加法
    ac_std_float<16, 5> sum = a + b;
    std::cout << "Sum: " << sum << std::endl;
    std::cout << "Sum data: " << sum.data() << std::endl;
    // 减法
    ac_std_float<16, 5> diff = a - b;
    std::cout << "Difference: " << diff << std::endl;

    // 乘法
    ac_std_float<16, 5> prod = a * b;
    std::cout << "Product: " << prod << std::endl;

    // 除法
    ac_std_float<16, 5> quot = a / b;
    std::cout << "Quotient: " << quot << std::endl;

    // 饱和处理 (FP16 的饱和通常指的是 NaN 和 Inf)
    ac_std_float<16, 5> max_val = ac_std_float<16, 5>::max();
    ac_std_float<16, 5> overflow = max_val * ac_std_float<16, 5>(2.0);  // 溢出
    std::cout << "Overflow: " << overflow << std::endl;                 // 输出 inf

    ac_std_float<16, 5> min_val = ac_std_float<16, 5>::min();
    ac_std_float<16, 5> underflow =
        min_val / ac_std_float<16, 5>(100000.0);  // 下溢 (结果会非常接近 0)
    std::cout << "Underflow: " << underflow << std::endl;

    return 0;
}
