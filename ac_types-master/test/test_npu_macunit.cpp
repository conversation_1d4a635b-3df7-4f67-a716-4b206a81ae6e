#include <algorithm>
#include <cassert>
#include <cstdint>
#include <cstring>
#include <iostream>
#include <stdexcept>
#include <vector>
#include "../include/ac_fixed.h"
#include "../include/ac_float.h"
#include "../npu_datagen.h"
#include "../npu_datatypes.h"
// Helper functions for matrix operations
namespace detail
{

// Get storage size based on precision width
inline size_t get_storage_size(uint8_t prec_in)
{
    uint32_t width_bits = width_code_to_bits(static_cast<dtype::WidthCode>(prec_in & 0x7));
    if (width_bits <= 8)
        return sizeof(uint8_t);
    if (width_bits <= 16)
        return sizeof(uint16_t);
    return sizeof(uint32_t);
}

// Write value to binary data with appropriate size
inline void write_storage_value(std::vector<uint8_t>& binary_data,
                                size_t offset,
                                const npu::npu_data_variant_t& data,
                                uint8_t prec_in)
{
    uint32_t width_bits = width_code_to_bits(static_cast<dtype::WidthCode>(prec_in & 0x7));
    if (width_bits <= 8)
    {
        uint8_t val = npu::to_storage_format<uint8_t>(prec_in, data);
        std::memcpy(&binary_data[offset], &val, sizeof(uint8_t));
    }
    else if (width_bits <= 16)
    {
        uint16_t val = npu::to_storage_format<uint16_t>(prec_in, data);
        std::memcpy(&binary_data[offset], &val, sizeof(uint16_t));
    }
    else
    {
        uint32_t val = npu::to_storage_format<uint32_t>(prec_in, data);
        std::memcpy(&binary_data[offset], &val, sizeof(uint32_t));
    }
}

}  // namespace detail

using namespace npu;

// 定义矩阵结构
template <typename T>
struct Matrix
{
    std::vector<T> data;
    int rows;
    int cols;

    Matrix(int r, int c) : rows(r), cols(c)
    {
        if (r <= 0 || c <= 0)
        {
            throw std::invalid_argument("Matrix dimensions must be positive");
        }
        data.resize(r * c);
        std::fill(data.begin(), data.end(), T());  // Zero-initialize with default constructor
    }

    // Copy constructor
    Matrix(const Matrix& other) : data(other.data), rows(other.rows), cols(other.cols)
    {
    }

    // Copy assignment operator
    Matrix& operator=(const Matrix& other)
    {
        if (this != &other)
        {
            rows = other.rows;
            cols = other.cols;
            data = other.data;
        }
        return *this;
    }

    T& at(int r, int c)
    {
        if (r < 0 || r >= rows || c < 0 || c >= cols)
        {
            throw std::out_of_range("Matrix index out of bounds");
        }
        return data[r * cols + c];
    }

    void set(int r, int c, const T& value)
    {
        if (r < 0 || r >= rows || c < 0 || c >= cols)
        {
            throw std::out_of_range("Matrix index out of bounds");
        }
        data[r * cols + c] = value;
    }

    const T& at(int r, int c) const
    {
        if (r < 0 || r >= rows || c < 0 || c >= cols)
        {
            throw std::out_of_range("Matrix index out of bounds");
        }
        return data[r * cols + c];
    }

    // 打印矩阵内容
    void print(const std::string& name = "") const
    {
        if (!name.empty())
        {
            std::cout << name << " (" << rows << "x" << cols << "):" << std::endl;
        }
        // 根据类型选择适当的输出方法
        if constexpr (std::is_same_v<T, npu_int4_t> || std::is_same_v<T, npu_int8_t> ||
                      std::is_same_v<T, npu_int16_t> || std::is_same_v<T, npu_int32_t>)
        {
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    std::cout << at(i, j).to_int() << " ";
                }
                std::cout << std::endl;
            }
        }
        else if constexpr (std::is_same_v<T, npu_fp16_t> || std::is_same_v<T, npu_fp32_t> ||
                           std::is_same_v<T, npu_bf16_t>)
        {
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    std::cout << at(i, j).to_float() << " ";
                }
                std::cout << std::endl;
            }
        }
        else
        {
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    std::cout << at(i, j) << " ";
                }
                std::cout << std::endl;
            }
        }
    }
};

// 根据精度进行乘法加法操作，适用于整数类型
template <typename T>
void matrix_multiply_add_int(const Matrix<T>& A, const Matrix<T>& B, Matrix<npu_int32_t>& C)
{
    if (A.cols != B.rows || A.rows != C.rows || B.cols != C.cols)
    {
        throw std::invalid_argument("Matrix dimensions mismatch for multiplication");
    }

    // 执行矩阵乘法, C = A * B
    for (int i = 0; i < A.rows; ++i)
    {
        for (int j = 0; j < B.cols; ++j)
        {
            npu_int32_t sum(0);  // 使用int32进行累加
            for (int k = 0; k < A.cols; ++k)
            {
                // 内部先统一转为int32，再执行乘法和加法运算
                npu_int32_t a_val(A.at(i, k).to_int());
                npu_int32_t b_val(B.at(k, j).to_int());
                sum += a_val * b_val;
            }
            // 将结果赋给输出矩阵
            C.at(i, j) = npu_int32_t(sum.to_int());
        }
    }
}

// 根据精度进行乘法加法操作，适用于浮点类型
template <typename T>
void matrix_multiply_add_fp(const Matrix<T>& A, const Matrix<T>& B, Matrix<npu_fp32_t>& C)
{
    if (A.cols != B.rows || A.rows != C.rows || B.cols != C.cols)
    {
        throw std::invalid_argument("Matrix dimensions mismatch for multiplication");
    }

    // 执行矩阵乘法, C = A * B
    for (int i = 0; i < A.rows; ++i)
    {
        for (int j = 0; j < B.cols; ++j)
        {
            float sum(0.0f);  // 使用fp32进行累加
            for (int k = 0; k < A.cols; ++k)
            {
                // 内部先统一转为fp32，再执行乘法和加法运算
                float a_val(A.at(i, k).to_float());
                float b_val(B.at(k, j).to_float());
                sum += a_val * b_val;
            }
            // 将结果赋给输出矩阵
            npu_fp32_t result(sum);
            C.set(i, j, result);
        }
    }
}

// 从生成的二进制数据中加载矩阵数据
template <typename T, typename StorageT>
void load_matrix_data(Matrix<T>& matrix, const std::vector<uint8_t>& binary_data, uint8_t prec_in)
{
    // 检查数据大小是否足够
    const size_t element_size = sizeof(StorageT);
    size_t expected_size = matrix.rows * matrix.cols * element_size;
    if (binary_data.size() < expected_size)
    {
        std::stringstream ss;
        ss << "Binary data size is insufficient. Expected " << expected_size << " bytes but got "
           << binary_data.size() << " bytes";
        throw std::runtime_error(ss.str());
    }

    // 解析二进制数据
    StorageT storage_val;
    for (int i = 0; i < matrix.rows; ++i)
    {
        for (int j = 0; j < matrix.cols; ++j)
        {
            try
            {
                size_t idx = (i * matrix.cols + j) * element_size;
                if (idx + element_size > binary_data.size())
                {
                    throw std::out_of_range("Binary data access out of bounds");
                }

                // 使用memcpy读取任意大小的存储类型
                std::memcpy(&storage_val, &binary_data[idx], element_size);

                if (!std::is_same_v<StorageT, uint8_t> && !std::is_same_v<StorageT, uint16_t> &&
                    !std::is_same_v<StorageT, uint32_t>)
                {
                    throw std::runtime_error("Unsupported StorageT type");
                }

                // 从存储格式转换为NPU数据类型
                npu_data_variant_t data_variant = from_storage_format(prec_in, storage_val);

                // 根据矩阵类型T和精度类型进行赋值
                if constexpr (std::is_same_v<T, npu_int4_t>)
                {
                    if (prec_in != dtype::INT4)
                        throw std::invalid_argument("Precision mismatch for INT4");
                    matrix.at(i, j) = T(std::get<npu_int4_t>(data_variant));
                }
                else if constexpr (std::is_same_v<T, npu_int8_t>)
                {
                    if (prec_in != dtype::INT8)
                        throw std::invalid_argument("Precision mismatch for INT8");
                    matrix.at(i, j) = T(std::get<npu_int8_t>(data_variant));
                }
                else if constexpr (std::is_same_v<T, npu_int16_t>)
                {
                    if (prec_in != dtype::INT16)
                        throw std::invalid_argument("Precision mismatch for INT16");
                    matrix.at(i, j) = T(std::get<npu_int16_t>(data_variant));
                }
                else if constexpr (std::is_same_v<T, npu_int32_t>)
                {
                    if (prec_in != dtype::INT32)
                        throw std::invalid_argument("Precision mismatch for INT32");
                    matrix.at(i, j) = T(std::get<npu_int32_t>(data_variant));
                }
                else if constexpr (std::is_same_v<T, npu_fp16_t>)
                {
                    if (prec_in != dtype::FP16)
                        throw std::invalid_argument("Precision mismatch for FP16");
                    matrix.at(i, j) = T(std::get<npu_fp16_t>(data_variant));
                }
                else if constexpr (std::is_same_v<T, npu_fp32_t>)
                {
                    if (prec_in != dtype::FP32)
                        throw std::invalid_argument("Precision mismatch for FP32");
                    matrix.at(i, j) = T(std::get<npu_fp32_t>(data_variant));
                }
                else if constexpr (std::is_same_v<T, npu_bf16_t>)
                {
                    if (prec_in != dtype::BF16)
                        throw std::invalid_argument("Precision mismatch for BF16");
                    matrix.at(i, j) = T(std::get<npu_bf16_t>(data_variant));
                }
                else
                {
                    throw std::invalid_argument("Unsupported matrix type");
                }
            }
            catch (const std::exception& e)
            {
                throw std::runtime_error(std::string("Error loading matrix data: ") + e.what());
            }
        }
    }
}

// 测试整数矩阵乘法
Matrix<npu_int32_t> test_int_matrix_multiply(int A_rows,
                                             int A_cols,
                                             int B_rows,
                                             int B_cols,
                                             uint8_t prec_in = dtype::INT8)
{
    if (A_cols != B_rows)
    {
        throw std::invalid_argument("Matrix dimensions mismatch for multiplication");
    }

    try
    {
        int C_rows = A_rows, C_cols = B_cols;

        // 初始化矩阵A
        Matrix<npu_int8_t> A(A_rows, A_cols);
        for (int i = 0; i < A_rows; ++i)
        {
            for (int j = 0; j < A_cols; ++j)
            {
                npu_data_variant_t random_data = generate_random_data(prec_in);
                A.set(i, j, std::get<npu_int8_t>(random_data));
            }
        }

        // 初始化矩阵B
        Matrix<npu_int8_t> B(B_rows, B_cols);
        for (int i = 0; i < B_rows; ++i)
        {
            for (int j = 0; j < B_cols; ++j)
            {
                npu_data_variant_t random_data = generate_random_data(prec_in);
                B.set(i, j, std::get<npu_int8_t>(random_data));
            }
        }

        // 执行矩阵乘法
        Matrix<npu_int32_t> C(C_rows, C_cols);
        matrix_multiply_add_int(A, B, C);

        // 可选: 打印矩阵数据
        std::cout << "Matrix Dimensions: A(" << A_rows << "x" << A_cols << ") * B(" << B_rows << "x"
                  << B_cols << ") = C(" << C_rows << "x" << C_cols << ")" << std::endl;
        A.print("Matrix A");
        B.print("Matrix B");
        C.print("Result Matrix C (A * B)");
        return C;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error in test_int_matrix_multiply: " << e.what() << std::endl;
        throw;
    }
}

// 测试浮点矩阵乘法
Matrix<npu_fp32_t> test_fp_matrix_multiply(int A_rows,
                                           int A_cols,
                                           int B_rows,
                                           int B_cols,
                                           uint8_t prec_in = dtype::FP16,
                                           double min_val = -10.0,
                                           double max_val = 10.0)
{
    if (A_cols != B_rows)
    {
        throw std::invalid_argument("Matrix dimensions mismatch for multiplication");
    }

    try
    {
        int C_rows = A_rows, C_cols = B_cols;

        // 初始化矩阵A
        Matrix<npu_fp16_t> A(A_rows, A_cols);
        for (int i = 0; i < A_rows; ++i)
        {
            for (int j = 0; j < A_cols; ++j)
            {
                npu_data_variant_t random_data = generate_random_data(prec_in, &min_val, &max_val);
                A.set(i, j, std::get<npu_fp16_t>(random_data));
            }
        }

        // 初始化矩阵B
        Matrix<npu_fp16_t> B(B_rows, B_cols);
        for (int i = 0; i < B_rows; ++i)
        {
            for (int j = 0; j < B_cols; ++j)
            {
                npu_data_variant_t random_data = generate_random_data(prec_in, &min_val, &max_val);
                B.set(i, j, std::get<npu_fp16_t>(random_data));
            }
        }

        // 执行矩阵乘法
        Matrix<npu_fp32_t> C(C_rows, C_cols);
        matrix_multiply_add_fp(A, B, C);

        // 可选: 打印矩阵数据
        std::cout << "Matrix Dimensions: A(" << A_rows << "x" << A_cols << ") * B(" << B_rows << "x"
                  << B_cols << ") = C(" << C_rows << "x" << C_cols << ")" << std::endl;
        A.print("Matrix A");
        B.print("Matrix B");
        C.print("Result Matrix C (A * B)");
        return C;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error in test_fp_matrix_multiply: " << e.what() << std::endl;
        throw;
    }
}

// 使用随机生成数据测试整数矩阵乘法
Matrix<npu::npu_int32_t> test_int_matrix_multiply_with_random_data(int A_rows,
                                                                   int A_cols,
                                                                   int B_rows,
                                                                   int B_cols,
                                                                   uint8_t prec_in = dtype::INT8)
{
    std::cout << "Testing integer matrix multiplication with random generated data..." << std::endl;

    if (A_cols != B_rows)
    {
        throw std::invalid_argument("Matrix dimensions mismatch for multiplication");
    }

    try
    {
        int C_rows = A_rows, C_cols = B_cols;

        // Get storage size based on precision
        size_t storage_size = detail::get_storage_size(prec_in);

        // Allocate binary data
        std::vector<uint8_t> A_binary_data(A_rows * A_cols * storage_size);
        std::vector<uint8_t> B_binary_data(B_rows * B_cols * storage_size);

        // Generate matrix data
        for (int i = 0; i < A_rows * A_cols; ++i)
        {
            npu::npu_data_variant_t random_data = generate_random_data(prec_in);
            detail::write_storage_value(A_binary_data, i * storage_size, random_data, prec_in);
        }

        for (int i = 0; i < B_rows * B_cols; ++i)
        {
            npu::npu_data_variant_t random_data = generate_random_data(prec_in);
            detail::write_storage_value(B_binary_data, i * storage_size, random_data, prec_in);
        }

        // Create output matrix
        Matrix<npu::npu_int32_t> C(C_rows, C_cols);

        // Create and load matrices based on precision
        switch (prec_in)
        {
            case dtype::INT4:
            {
                Matrix<npu::npu_int4_t> A(A_rows, A_cols), B(B_rows, B_cols);
                load_matrix_data<npu::npu_int4_t, uint8_t>(A, A_binary_data, prec_in);
                load_matrix_data<npu::npu_int4_t, uint8_t>(B, B_binary_data, prec_in);
                matrix_multiply_add_int(A, B, C);
                A.print("Matrix A");
                B.print("Matrix B");
                break;
            }
            case dtype::INT8:
            {
                Matrix<npu::npu_int8_t> A(A_rows, A_cols), B(B_rows, B_cols);
                load_matrix_data<npu::npu_int8_t, uint8_t>(A, A_binary_data, prec_in);
                load_matrix_data<npu::npu_int8_t, uint8_t>(B, B_binary_data, prec_in);
                matrix_multiply_add_int(A, B, C);
                A.print("Matrix A");
                B.print("Matrix B");
                break;
            }
            case dtype::INT16:
            {
                Matrix<npu::npu_int16_t> A(A_rows, A_cols), B(B_rows, B_cols);
                load_matrix_data<npu::npu_int16_t, uint16_t>(A, A_binary_data, prec_in);
                load_matrix_data<npu::npu_int16_t, uint16_t>(B, B_binary_data, prec_in);
                matrix_multiply_add_int(A, B, C);
                A.print("Matrix A");
                B.print("Matrix B");
                break;
            }
            case dtype::INT32:
            {
                Matrix<npu::npu_int32_t> A(A_rows, A_cols), B(B_rows, B_cols);
                load_matrix_data<npu::npu_int32_t, uint32_t>(A, A_binary_data, prec_in);
                load_matrix_data<npu::npu_int32_t, uint32_t>(B, B_binary_data, prec_in);
                matrix_multiply_add_int(A, B, C);
                A.print("Matrix A");
                B.print("Matrix B");
                break;
            }
            default:
                throw std::invalid_argument("Unsupported integer precision type");
        }

        // Print results
        C.print("Result Matrix C (A * B)");

        return C;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error in test_int_matrix_multiply_with_random_data: " << e.what()
                  << std::endl;
        throw;
    }
}

// 使用随机生成数据测试浮点矩阵乘法
Matrix<npu::npu_fp32_t> test_fp_matrix_multiply_with_random_data(int A_rows,
                                                                 int A_cols,
                                                                 int B_rows,
                                                                 int B_cols,
                                                                 uint8_t prec_in = dtype::FP16,
                                                                 double min_val = -10.0,
                                                                 double max_val = 10.0)
{
    std::cout << "Testing floating-point matrix multiplication with random generated data..."
              << std::endl;

    if (A_cols != B_rows)
    {
        throw std::invalid_argument("Matrix dimensions mismatch for multiplication");
    }

    try
    {
        int C_rows = A_rows, C_cols = B_cols;

        // Get storage size based on precision
        size_t storage_size = detail::get_storage_size(prec_in);

        // Allocate binary data
        std::vector<uint8_t> A_binary_data(A_rows * A_cols * storage_size);
        std::vector<uint8_t> B_binary_data(B_rows * B_cols * storage_size);

        // Generate matrix data
        for (int i = 0; i < A_rows * A_cols; ++i)
        {
            npu::npu_data_variant_t random_data = generate_random_data(prec_in, &min_val, &max_val);
            detail::write_storage_value(A_binary_data, i * storage_size, random_data, prec_in);
        }

        for (int i = 0; i < B_rows * B_cols; ++i)
        {
            npu::npu_data_variant_t random_data = generate_random_data(prec_in, &min_val, &max_val);
            detail::write_storage_value(B_binary_data, i * storage_size, random_data, prec_in);
        }

        // Create output matrix
        Matrix<npu::npu_fp32_t> C(C_rows, C_cols);

        // Create and load matrices based on precision
        switch (prec_in)
        {
            case dtype::FP16:
            {
                Matrix<npu::npu_fp16_t> A(A_rows, A_cols), B(B_rows, B_cols);
                load_matrix_data<npu::npu_fp16_t, uint16_t>(A, A_binary_data, prec_in);
                load_matrix_data<npu::npu_fp16_t, uint16_t>(B, B_binary_data, prec_in);
                matrix_multiply_add_fp(A, B, C);
                A.print("Matrix A");
                B.print("Matrix B");
                break;
            }
            case dtype::FP32:
            {
                Matrix<npu::npu_fp32_t> A(A_rows, A_cols), B(B_rows, B_cols);
                load_matrix_data<npu::npu_fp32_t, uint32_t>(A, A_binary_data, prec_in);
                load_matrix_data<npu::npu_fp32_t, uint32_t>(B, B_binary_data, prec_in);
                matrix_multiply_add_fp(A, B, C);
                A.print("Matrix A");
                B.print("Matrix B");
                break;
            }
            case dtype::BF16:
            {
                Matrix<npu::npu_bf16_t> A(A_rows, A_cols), B(B_rows, B_cols);
                load_matrix_data<npu::npu_bf16_t, uint16_t>(A, A_binary_data, prec_in);
                load_matrix_data<npu::npu_bf16_t, uint16_t>(B, B_binary_data, prec_in);
                matrix_multiply_add_fp(A, B, C);
                A.print("Matrix A");
                B.print("Matrix B");
                break;
            }

            default:
                throw std::invalid_argument("Unsupported floating-point precision type");
        }

        // Print results
        C.print("Result Matrix C (A * B)");

        return C;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error in test_fp_matrix_multiply_with_random_data: " << e.what() << std::endl;
        throw;
    }
}

int main()
{
    std::cout << "NPU MAC Unit Test" << std::endl;

    try
    {
        // 测试整数矩阵乘法
        test_int_matrix_multiply(4, 3, 3, 2, dtype::INT8);
        std::cout << std::endl;

        // 测试浮点矩阵乘法
        test_fp_matrix_multiply(3, 4, 4, 2, dtype::FP16, -10.0, 10.0);
        std::cout << std::endl;

        // 测试使用随机生成数据的整数矩阵乘法
        test_int_matrix_multiply_with_random_data(4, 3, 3, 2, dtype::INT32);
        std::cout << std::endl;
        // 测试使用随机生成数据的浮点矩阵乘法
        test_fp_matrix_multiply_with_random_data(3, 4, 4, 2, dtype::FP32, -10.0, 10.0);
        std::cout << std::endl;

        return 0;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
}