## 测试方案文档

**文件:** `ac_types-master/npu_datatypes.h`

**目标函数:** `generate_random_data`, `from_storage_format`, `to_storage_format`

### 1. 函数: `generate_random_data(uint8_t prec_in, double* min = nullptr, double* max = nullptr)`

**描述:**
该通用函数根据精度代码 `prec_in` 生成随机数据。可以指定最小值 `min` 和最大值 `max`，如果 `min` 或 `max` 为空指针，则使用该精度类型的默认最小值和最大值。

**测试策略:**
- 针对每种支持的精度类型，验证函数是否能生成指定类型范围内的随机数据。
- 验证当 `min` 和 `max` 参数为空指针时，函数是否使用默认的最小值和最大值。
- 验证当 `min` 和 `max` 参数有效时，函数是否使用指定的最小值和最大值。
- 验证生成的随机数据是否在指定的 [min, max] 范围内。
- 针对无效的精度代码，验证函数是否抛出 `std::invalid_argument` 异常。

**测试用例:**

| 测试用例 ID | 精度代码 (`prec_in`) | `min` 参数 | `max` 参数 | 预期行为                                                                 | 描述                                                                   |
|-------------|-----------------------|------------|------------|---------------------------------------------------------------------------|------------------------------------------------------------------------|
| GRD_TC01    | `dtype::INT4`        | `nullptr`  | `nullptr`  | 生成 `npu_int4_t` 类型的随机数据，范围在 [-8, 7] 内                               | INT4 默认范围随机数据                                                    |
| GRD_TC02    | `dtype::INT8`        | `nullptr`  | `nullptr`  | 生成 `npu_int8_t` 类型的随机数据，范围在 [-128, 127] 内                              | INT8 默认范围随机数据                                                    |
| GRD_TC03    | `dtype::INT16`       | `nullptr`  | `nullptr`  | 生成 `npu_int16_t` 类型的随机数据，范围在 [-32768, 32767] 内                            | INT16 默认范围随机数据                                                   |
| GRD_TC04    | `dtype::INT32`       | `nullptr`  | `nullptr`  | 生成 `npu_int32_t` 类型的随机数据，范围在 [-2147483648, 2147483647] 内                  | INT32 默认范围随机数据                                                   |
| GRD_TC05    | `dtype::FP16`        | `nullptr`  | `nullptr`  | 生成 `npu_fp16_t` 类型的随机数据，范围在 `[npu_fp16_min, npu_fp16_max]` 内             | FP16 默认范围随机数据                                                    |
| GRD_TC06    | `dtype::FP32`        | `nullptr`  | `nullptr`  | 生成 `npu_fp32_t` 类型的随机数据，范围在 `[npu_fp32_min, npu_fp32_max]` 内             | FP32 默认范围随机数据                                                    |
| GRD_TC07    | `dtype::BF16`        | `nullptr`  | `nullptr`  | 生成 `npu_bf16_t` 类型的随机数据，范围在 `[npu_bf16_min, npu_bf16_max]` 内             | BF16 默认范围随机数据                                                    |
| GRD_TC08    | `dtype::BBF16`       | `nullptr`  | `nullptr`  | 生成 `npu_bff16_t` 类型的随机数据，范围在 `[npu_bff16_min, npu_bff16_max]` 内            | BBF16 默认范围随机数据                                                   |
| GRD_TC09    | `dtype::INT4`        | `-5.0`     | `5.0`      | 生成 `npu_int4_t` 类型随机数据，范围在 [-5, 5] 内 (限制在 INT4 的有效范围内 [-8, 7])       | INT4 指定范围随机数据                                                    |
| GRD_TC10    | `dtype::FP32`        | `1.0`      | `100.0`    | 生成 `npu_fp32_t` 类型随机数据，范围在 [1.0, 100.0] 内                                | FP32 指定范围随机数据                                                    |
| GRD_TC11    | `Invalid Code (255)` | `nullptr`  | `nullptr`  | `std::invalid_argument` 异常                                                        | 无效精度代码，应抛出异常                                                  |

**覆盖目标:**
- 覆盖所有有效的精度代码分支。
- 覆盖 `min` 和 `max` 参数为空指针和有效指针的情况。
- 验证生成的随机数据是否在指定范围内。
- 覆盖无效精度代码的异常处理分支。

### 2. 函数: `from_storage_format(uint8_t prec_in, T data)`

**描述:**
该通用函数模板 `from_storage_format` 根据精度代码 `prec_in` 和存储格式数据 `data`，将存储格式数据转换为 NPU 数据类型。

**测试策略:**
- 针对每种支持的精度类型，验证函数是否能从正确的存储类型转换为对应的 NPU 数据类型。
- 针对无效的精度代码或不兼容的存储类型，验证函数是否抛出 `std::invalid_argument` 异常。
- 验证转换后的数据类型是否正确。

**测试用例:**

| 测试用例 ID | 精度代码 (`prec_in`) | 存储数据类型 (`T`) | 存储数据值 | 预期 NPU 数据类型 | 描述                                     |
|-------------|-----------------------|--------------------|------------|-------------------|------------------------------------------|
| FSF_TC01    | `dtype::INT4`        | `uint8_t`          | `5`        | `npu_int4_t`      | INT4 从 `uint8_t` 转换                     |
| FSF_TC02    | `dtype::INT8`        | `uint8_t`          | `100`      | `npu_int8_t`      | INT8 从 `uint8_t` 转换                     |
| FSF_TC03    | `dtype::INT16`       | `uint16_t`         | `30000`    | `npu_int16_t`     | INT16 从 `uint16_t` 转换                    |
| FSF_TC04    | `dtype::INT32`       | `uint32_t`         | `2000000000`| `npu_int32_t`     | INT32 从 `uint32_t` 转换                    |
| FSF_TC05    | `dtype::FP16`        | `uint16_t`         | `0x3c00`   | `npu_fp16_t`      | FP16 从 `uint16_t` 转换 (表示 1.0)         |
| FSF_TC06    | `dtype::FP32`        | `uint32_t`         | `0x3f800000`| `npu_fp32_t`      | FP32 从 `uint32_t` 转换 (表示 1.0)         |
| FSF_TC07    | `dtype::BF16`        | `uint16_t`         | `0x3f80`   | `npu_bf16_t`      | BF16 从 `uint16_t` 转换 (表示 1.0)         |
| FSF_TC08    | `dtype::BBF16`       | `uint16_t`         | `0x3f80`   | `npu_bff16_t`     | BBF16 从 `uint16_t` 转换 (表示 1.0)        |
| FSF_TC09    | `Invalid Code (255)` | `uint8_t`          | `5`        | `std::invalid_argument` | 无效精度代码，应抛出异常                   |
| FSF_TC10    | `dtype::INT4`        | `uint16_t`         | `5`        | `std::invalid_argument` | 存储类型不兼容 (INT4 应该从 uint8_t 转换) |

**覆盖目标:**
- 覆盖所有有效的精度代码和对应的存储类型分支。
- 覆盖无效精度代码和不兼容存储类型的异常处理分支。
- 验证模板函数对不同存储类型的处理。

### 3. 函数: `to_storage_format(uint8_t prec_in, const npu_data_variant_t& data_variant)`

**描述:**
该通用函数模板 `to_storage_format` 根据精度代码 `prec_in` 和 NPU 数据类型 `data_variant`，将 NPU 数据类型转换为存储格式。

**测试策略:**
- 针对每种支持的精度类型，验证函数是否能将对应的 NPU 数据类型转换为正确的存储类型。
- 针对无效的精度代码或不兼容的 NPU 数据类型，验证函数是否抛出 `std::invalid_argument` 异常。
- 验证转换后的存储数据类型和值是否正确。

**测试用例:**

| 测试用例 ID | 精度代码 (`prec_in`) | NPU 数据类型          | NPU 数据值         | 预期存储数据类型 (`T`) | 预期存储数据值 | 描述                                     |
|-------------|-----------------------|-----------------------|--------------------|-----------------------|----------------|------------------------------------------|
| TSF_TC01    | `dtype::INT4`        | `npu_int4_t`          | `5`                | `uint8_t`             | `5`              | INT4 转换为 `uint8_t`                     |
| TSF_TC02    | `dtype::INT8`        | `npu_int8_t`          | `100`              | `uint8_t`             | `100`            | INT8 转换为 `uint8_t`                     |
| TSF_TC03    | `dtype::INT16`       | `npu_int16_t`         | `30000`            | `uint16_t`            | `30000`          | INT16 转换为 `uint16_t`                    |
| TSF_TC04    | `dtype::INT32`       | `npu_int32_t`         | `2000000000`       | `uint32_t`            | `2000000000`     | INT32 转换为 `uint32_t`                    |
| TSF_TC05    | `npu_fp16_t`        | `npu_fp16_t`          | `npu_fp16_t(1.0)`  | `uint16_t`            | `0x3c00`         | FP16 转换为 `uint16_t` (表示 1.0)         |
| TSF_TC06    | `dtype::FP32`        | `npu_fp32_t`          | `npu_fp32_t(1.0)`  | `uint32_t`            | `0x3f800000`     | FP32 转换为 `uint32_t` (表示 1.0)         |
| TSF_TC07    | `dtype::BF16`        | `npu_bf16_t`          | `npu_bf16_t(1.0)`  | `uint16_t`            | `0x3f80`         | BF16 转换为 `uint16_t` (表示 1.0)         |
| TSF_TC08    | `dtype::BBF16`       | `npu_bff16_t`         | `npu_bff16_t(1.0)` | `uint16_t`            | `0x3f80`         | BBF16 转换为 `uint16_t` (表示 1.0)        |
| TSF_TC09    | `Invalid Code (255)` | `npu_int4_t`          | `5`                | `std::invalid_argument` | N/A              | 无效精度代码，应抛出异常                   |
| TSF_TC10    | `dtype::INT4`        | `npu_fp16_t`          | `npu_fp16_t(1.0)`  | `uint8_t`             | N/A              | NPU 数据类型不兼容 (INT4 应该转换为 uint8_t) |

**覆盖目标:**
- 覆盖所有有效的精度代码和对应的 NPU 数据类型分支。
- 覆盖无效精度代码和不兼容 NPU 数据类型的异常处理分支。
- 验证模板函数对不同存储类型的处理。


