#include <ac_int.h>
#include <cstdint>
#include <iostream>
using namespace ac_intN;

int main()
{
    ac_int<8, true> a = -8;
    ac_int<4, true> d = 8;
    a.set_val<ac_special_val::AC_VAL_MAX>();
    std::cout << "a :" << std::dec << a.to_int() << "size:" << sizeof(a) << std::endl;
    a.set_val<ac_special_val::AC_VAL_MIN>();
    std::cout << "a :" << std::dec << a.to_int() << "size:" << sizeof(a) << std::endl;
    int32_t c = a.to_int();
    int32_t e = d.to_int();
    std::cout << "a :" << std::dec << c << "size:" << sizeof(c) << std::endl;
    std::cout << "d :" << std::dec << e << "size:" << sizeof(e) << std::endl;
    ac_int<8, false> b = 10;
    // 加法 (无饱和)
    ac_int<8, false> sum = a + b;
    std::cout << "Sum (no saturation): " << sum << std::endl;  // 输出 4 (260 % 256)

    // 加法 (带饱和)
    ac_int<9, false> sum_temp = a + b;  // 使用更大的类型来避免中间结果溢出
    ac_int<8, false> sum_sat =
        sum_temp > ac_int<9, false>(255) ? ac_int<8, false>(255) : sum_temp.template slc<8>(0);
    std::cout << "Sum (with saturation): " << sum_sat << std::endl;  // 输出 255

    // 减法 (无饱和)
    ac_int<8, false> diff = a - b;
    std::cout << "diff (no saturation): " << diff << std::endl;  // 输出 240

    // 减法 (带饱和)
    ac_int<9, true> diff_temp = a - b;
    ac_int<8, false> diff_sat;
    if (diff_temp < 0)
    {
        diff_sat = 0;
    }
    else
    {
        diff_sat = diff_temp.template slc<8>(0);
    }
    std::cout << "diff (with saturation): " << diff_sat << std::endl;

    // 乘法 (无饱和)
    ac_int<8, false> prod = a * b;
    std::cout << "Product (no saturation): " << prod << std::endl;  // 输出 208 (2500 % 256)

    // 乘法 (带饱和)
    ac_int<16, false> prod_temp = a * b;
    ac_int<8, false> prod_sat =
        prod_temp > 255 ? ac_int<8, false>(255) : prod_temp.template slc<8>(0);
    std::cout << "Product (with saturation): " << prod_sat << std::endl;  // 输出 255

    // 除法
    ac_int<8, false> quot = a / b;
    std::cout << "Quotient: " << quot << std::endl;  // 输出 25

    // 带有上下限饱和的加法函数示例
    auto add_with_saturation = [](ac_int<8, false> x, ac_int<8, false> y) -> ac_int<8, false>
    {
        ac_int<9, false> temp = x + y;
        if (temp > 255)
        {
            return 255;
        }
        return temp.template slc<8>(0);
    };

    ac_int<8, false> saturated_sum = add_with_saturation(a, b);
    std::cout << "Saturated Sum: " << saturated_sum << std::endl;  // 输出 255

    return 0;
}
